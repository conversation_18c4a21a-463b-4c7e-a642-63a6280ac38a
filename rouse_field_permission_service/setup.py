import setuptools

with open("README.md", "r") as fh:
    long_description = fh.read()

setuptools.setup(
    name="rouse_field_permission_service",
    include_package_data=True,
    version="0.13.0",
    author="<PERSON>",
    author_email="<EMAIL>",
    description="Field Permission Service.",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/RouseServices/infrastructure-tools",
    packages=setuptools.find_packages(),
    classifiers=[
        #   3 - Alpha
        #   4 - Beta
        #   5 - Production/Stable
        'Development Status :: 3',
        "Programming Language :: Python :: 3",
        "Operating System :: OS Independent",
    ],
    install_requires=[],
    python_requires=">=3.6",
)
