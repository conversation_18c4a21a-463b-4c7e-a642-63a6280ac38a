% Algo Pricing Pipeline: Current Status and Proposal
% <PERSON>
% April 23, 2025

<!-- markdownlint-disable MD041 -->
<!-- markdownlint-disable MD004 -->
<!-- markdownlint-disable MD029 -->
<!-- markdownlint-disable MD040 -->

# Introduction 🚀

## Current Status and Challenges of Algo Pricing Solution 📊

* Comprehensive analysis of the current Algo Pricing ML workflow
* Identification of key challenges in the current implementation
* Evaluation of alternative solutions for improvement

::: notes
This presentation outlines our analysis of the current Algo Pricing machine learning workflow, identifies key challenges, and proposes improvements leveraging modern MLOps solutions. We'll cover the current state, considerations for different teams, compare alternatives, and present our recommended approach.
:::

# Considerations 🔍

## Data Science Team Enablers 🧪 <img src="google-cloud-icons/vertexai.svg" height="32px"/>

:::::::::::::: {.columns}
::: {.column width="50%"}
- Multiple workflow runs
  - Development iterations
  - Research experimentation
  - Parallel testing
:::
::: {.column width="50%"}
- ML-focused tools
  - Model artifact management
  - Experiment tracking
  - Operational automation
:::
::::::::::::::

::: notes
Data scientists need the ability to run multiple workflow iterations for development and research purposes. They also need specialized ML tools for managing model artifacts, tracking experiments, and automating operational tasks. The current solution requires significant manual intervention and lacks integrated ML-specific features.
:::

## Engineering Team Enablers 🛠️ <img src="google-cloud-icons/monitoring.svg" height="32px"/>

:::::::::::::: {.columns}
::: {.column width="50%"}
- Better monitoring
  - Resource utilization
  - Cost tracking
  - Performance metrics
:::
::: {.column width="50%"}
- Operational improvements
  - Simplified support
  - Automated deployment
  - SOC2 compliance
:::
::::::::::::::

::: notes
The engineering team requires better monitoring capabilities to track resource utilization, costs, and performance metrics. They also need simplified operational support, automated deployment processes, and solutions that maintain SOC2 compliance. The current VM-based approach makes these aspects challenging to implement and maintain.
:::

## Platform Engineering Benefits 🏗️ <img src="google-cloud-icons/cloud_run.svg" height="32px"/>

- Improved expense tracking
  - Granular cost allocation
  - Resource optimization
  - Usage-based billing
- Infrastructure standardization
  - Consistent deployment patterns
  - Reduced maintenance overhead
  - Scalable architecture

::: notes
Platform engineering benefits from improved expense tracking with granular cost allocation, resource optimization, and usage-based billing. Infrastructure standardization provides consistent deployment patterns, reduced maintenance overhead, and a more scalable architecture. The current solution lacks these capabilities due to its custom nature.
:::

# Solution Comparison 📊

## Current Solution: VMs + Bash Scripts + Docker 🖥️

```{.mermaid format=png background=transparent}
---
title: Current Algo Pricing Architecture
config:
  theme: neutral
  themeVariables:
    primaryColor: "#4682B4"
    secondaryColor: "#B0C4DE"
    lineColor: "#708090"
    textColor: "#2F4F4F"
---
graph TD
    A[Data Scientist] -->|Develops Model| B[Local Development]
    B -->|Commits Code| C[Git Repository]
    C -->|Triggers| D[build_and_push.sh]
    D -->|Builds & Pushes| E[Docker Image]
    G[MLOps Engineer] -->|Runs| H[scheduler.sh]
    H -->|Checks Environment| I{Production?}
    I -->|Yes/No| L[spinup_orchestration_vm.py]
    L -->|Creates| M[VM for Orchestration]
    M -->|Runs| N[get_and_run_docker_image.sh]
    N -->|Executes| P[stackingBase.py]
    P -->|Trains Models| T[Model Artifacts]
    T -->|Stored in| U[Cloud Storage]
```

::: notes
The current solution relies on custom scripting, Docker containers, and VM orchestration. It involves multiple manual steps and requires extensive system knowledge to operate effectively. The workflow includes building Docker images, orchestrating VMs, and executing the ML pipeline within containers. This approach has significant operational overhead and limited scalability.
:::

## Vertex AI Pipelines Solution 🧠 <img src="google-cloud-icons/vertexai.svg" height="32px"/>

```{.mermaid format=png background=transparent}
---
title: Vertex AI Pipeline Architecture
config:
  theme: default
  themeVariables:
    primaryColor: "#00ff00"
    primaryTextColor: "#000"
---
graph TD
    A[Data Scientist] -->|Develops Model| B[Vertex AI Notebooks]
    B -->|Commits Code| C[Git Repository]
    C -->|Triggers| D[Cloud Build CI/CD]
    D -->|Builds & Compiles| G[Pipeline Definition]
    I[Data Scientist/MLOps] -->|Triggers| J[Vertex AI Pipeline]
    J -->|Executes| K[Data Preparation]
    K -->|Processes| M[Dataset]
    M -->|Input to| N[Model Training]
    N -->|Trains Models| R[Model Evaluation]
    R -->|If Passes| S[Model Registry]
    S -->|Deploys to| T[Vertex AI Endpoint]
```

::: notes
Vertex AI Pipelines provides a purpose-built solution for ML workflows with excellent ML service integration, native artifact tracking, visual DAG representation, and built-in experiment tracking. It offers a managed execution environment, eliminating the need for VM orchestration, and provides comprehensive monitoring and scaling capabilities. This solution significantly reduces operational overhead and improves the developer experience.
:::

## Astronomer (Airflow) Solution 🌬️ <img src="google-cloud-icons/workflows.svg" height="32px"/>

```{.mermaid format=png background=transparent}
---
title: Airflow Pipeline Architecture
config:
  theme: forest
  themeVariables:
    primaryColor: "#8B4513"
    lineColor: "#A0522D"
    primaryTextColor: "#FDF5E6"
---
graph TD
    A[Data Scientist] -->|Develops Model| B[Local Development]
    B -->|Commits Code| C[Git Repository]
    C -->|Triggers| D[Cloud Build CI/CD]
    D -->|Builds| E[Container Images]
    G[MLOps Engineer] -->|Defines| H[Airflow DAGs]
    J[Cloud Composer] -->|Executes| H
    H -->|Triggers| K[Data Preparation]
    K -->|Processes| M[Dataset]
    M -->|Input to| N[Model Training Tasks]
    N -->|Trains Models| R[Model Evaluation]
    R -->|If Passes| S[Model Registry]
    S -->|Registers Model in| T[Vertex AI Model Registry]
```

::: notes
Astronomer (Airflow) provides a general-purpose orchestration tool with good ML service integration. It offers a familiar workflow definition format (DAGs), extensive operator library, and good monitoring capabilities. However, it requires Airflow knowledge and has a higher operational overhead compared to Vertex AI Pipelines. It's a good option if the team already has Airflow expertise or needs to integrate with existing Airflow workflows.
:::

## Solution Comparison Table 📋

| Aspect | Current | Vertex AI | Astronomer (Airflow) |
|--------|---------|-----------|----------------------|
| **Ease of Use** | Low | High | Medium |
| **Operational Overhead** | High | Low | Medium |
| **Scalability** | Limited | High | High |
| **ML-specific Features** | Custom | Excellent | Good |
| **Monitoring** | Limited | Excellent | Good |
| **Cost Model** | VM-based | Per run | Instance hours |
| **Development Time** | Days | Minutes | Hours |
| **Maintenance** | High | Low | Medium |

::: notes
This table compares the three solutions across various aspects. Vertex AI Pipelines offers the best ease of use for data scientists and excellent ML-specific features. Astronomer (Airflow) provides a good balance of features but requires Airflow knowledge. The current solution has the highest operational overhead and limited scalability.
:::

## Detailed Workflow Comparison: VM+Bash vs. Vertex AI 🔄

:::::::::::::: {.columns}
::: {.column width="50%"}
**Current VM+Bash Workflow**

- Manual script execution
- Sequential processing
- Limited parallelization
- Manual error handling
- Custom logging solutions
- Manual resource provisioning
- Explicit VM creation/deletion
:::
::: {.column width="50%"}
**Vertex AI Pipeline Workflow**

- Declarative pipeline definition
- Parallel execution capabilities
- Automatic retries and error handling
- Integrated logging and monitoring
- Automatic resource provisioning
- Ephemeral compute resources
:::
::::::::::::::

::: notes
The current workflow relies heavily on manual script execution and explicit VM management. Each step must be carefully orchestrated, with error handling implemented through custom bash scripts. In contrast, Vertex AI Pipelines offers a declarative approach where the pipeline is defined as a DAG, enabling automatic parallelization, built-in error handling, and efficient resource utilization. This significantly reduces the operational burden on both data scientists and MLOps engineers.
:::

## Workflow Procedural Analysis 🔍

```{.mermaid format=png background=transparent}
---
title: Procedural Workflow Comparison
config:
  theme: neutral
  themeVariables:
    primaryColor: "#4682B4"
    secondaryColor: "#B0C4DE"
    lineColor: "#708090"
    textColor: "#2F4F4F"
---
graph TD
    subgraph "Current VM+Bash Approach"
    A1[Manual Script Execution] -->|Sequential| B1[VM Provisioning]
    B1 -->|Wait for VM| C1[Docker Image Pull]
    C1 -->|Manual Monitoring| D1[Model Training]
    D1 -->|Manual Validation| E1[Result Storage]
    E1 -->|Manual Cleanup| F1[VM Termination]
    end

    subgraph "Vertex AI Pipeline Approach"
    A2[Pipeline Definition] -->|Parallel Execution| B2[Automatic Resource Allocation]
    B2 -->|No Wait Time| C2[Container Execution]
    C2 -->|Automatic Monitoring| D2[Model Training]
    D2 -->|Automated Validation| E2[Artifact Management]
    E2 -->|Automatic Cleanup| F2[Resource Release]
    end
```

::: notes
This diagram illustrates the procedural differences between the current VM+bash approach and the Vertex AI Pipeline solution. The current approach requires multiple manual steps, including VM provisioning, Docker image management, and explicit resource cleanup. Each step introduces potential points of failure and requires active monitoring. The Vertex AI approach automates these operational tasks, allowing data scientists to focus on model development rather than infrastructure management.
:::

## Artifact Repository Comparison: Git vs. Artifact Registry 📦

:::::::::::::: {.columns}
::: {.column width="50%"}
**Current Git-based Approach**

- Models stored in Git repository
- Limited versioning capabilities
- Size constraints for large models
- Manual tracking of model lineage
- No built-in model serving
- Difficult to manage binary artifacts
- Limited metadata support
:::
::: {.column width="50%"}
**Artifact Registry Approach**

- Purpose-built for ML artifacts
- Comprehensive versioning
- Optimized for large binary files
- Automatic lineage tracking
- Integrated with model serving
- Native support for containers
- Rich metadata capabilities
:::
::::::::::::::

::: notes
The current approach uses Git to store model artifacts, which presents several limitations. Git is designed for code versioning, not for managing large binary files like ML models. This leads to repository bloat and slow operations. Artifact Registry, in contrast, is purpose-built for storing and versioning artifacts like ML models and containers. It provides rich metadata capabilities, automatic lineage tracking, and seamless integration with model serving platforms. This enables better governance, compliance, and reproducibility of ML models.
:::

## Cost Comparison Analysis 💰

### Cost Formula Components

| Component | VM+Bash Approach | Vertex AI Approach |
|-----------|------------------|-------------------|
| **Compute** | VM instance hours (n1-highmem-96) | Pipeline run hours (on-demand) |
| **Storage** | Persistent disk + Cloud Storage | Artifact Registry + Cloud Storage |
| **Networking** | VM egress + Docker Hub pulls | Internal GCP traffic |
| **Management** | Manual operations time | Automated management |
| **Idle Resources** | ~40% idle time | Pay only for active execution |

### Estimated Monthly Cost Formula

**VM+Bash Approach:**

```
Cost = (VM_hourly_rate × 24 × 30 × utilization_factor) +
       (storage_cost) + (network_cost) +
       (engineer_hourly_rate × manual_hours)
```

**Vertex AI Approach:**

```
Cost = (pipeline_hourly_rate × actual_run_hours) +
       (artifact_storage_cost) +
       (engineer_hourly_rate × reduced_manual_hours)
```

::: notes
The cost comparison reveals significant differences between the two approaches. The current VM+bash solution incurs costs even when resources are idle, as VMs are provisioned for extended periods. The n1-highmem-96 machines used are expensive, costing approximately $5.32 per hour, resulting in over $3,800 per month if running continuously. In contrast, Vertex AI Pipelines uses a pay-per-use model, charging only for the actual computation time. Additionally, the reduced operational overhead translates to fewer engineering hours spent on maintenance, further decreasing the total cost of ownership. A conservative estimate suggests potential cost savings of 30-40% with the Vertex AI approach, particularly as the number of model training runs increases.
:::

# Proposal 💡

## Recommended Approach: Vertex AI Pipelines 🎯

:::::::::::::: {.columns}
::: {.column width="50%"}

### Key Benefits

- Purpose-built for ML
- Managed infrastructure
- Integrated experiment tracking
- Simplified deployment
- Comprehensive monitoring
:::
::: {.column width="50%"}

### Implementation Components

- Pipeline definition
- Component containerization
- Artifact management
- Model registry
- CI/CD integration
:::
::::::::::::::

::: notes
We recommend adopting Vertex AI Pipelines as the primary solution for the Algo Pricing workflow. This approach offers the best balance of ML-specific features, ease of use, and operational efficiency. The implementation will involve converting existing scripts into pipeline components, containerizing components for consistent execution, leveraging ML Metadata for artifact management, and using the Model Registry for model versioning and deployment.
:::

## Implementation Plan 📝

1. **Assessment and Planning**
   * Detailed assessment of current codebase
   * Identification of components for migration
   * Development of detailed migration plan

2. **Proof of Concept**
   * Implementation of a simple pipeline in Vertex AI
   * Migration of one model type (e.g., XGBoost)
   * Validation of results against current implementation

::: notes
We recommend a staged implementation approach to minimize disruption. The first two stages involve a detailed assessment of the current codebase and the development of a proof of concept to validate the approach with one model type. This allows us to identify and address any issues early in the process.
:::

## Implementation Plan (Continued) 📝

3. **Incremental Migration**
   * Systematic migration of remaining model types
   * Implementation of CI/CD pipelines
   * Development of monitoring and alerting

4. **Optimization and Training**
   * Performance optimization
   * Documentation and knowledge transfer
   * Training for data scientists and MLOps engineers

::: notes
The next stages involve the incremental migration of the remaining model types and the implementation of CI/CD pipelines. The final stage focuses on optimization, documentation, and training to ensure a smooth transition and effective use of the new platform. This approach allows us to deliver value incrementally while minimizing risk.
:::

# Conclusion 🏁

## Summary and Next Steps 📋

* **Current State**: Custom scripts, VMs, and Docker containers with significant operational overhead
* **Recommendation**: Migrate to Vertex AI Pipelines for comprehensive MLOps capabilities
* **Benefits**: Reduced complexity, improved collaboration, faster iteration, better monitoring
* **Implementation**: Staged approach to minimize disruption

**Next Steps**:

1. Stakeholder review and feedback
2. Decision on proceeding with the proposed changes
3. Detailed implementation planning

::: notes
In conclusion, we've analyzed the current Algo Pricing ML workflow and recommended migrating to Vertex AI Pipelines to address the identified challenges. The proposed solution offers significant benefits for both data scientists and MLOps engineers. We recommend proceeding with stakeholder review and detailed implementation planning to ensure a successful migration.
:::

## Thank You 🙏

**Questions?**

Contact: Juan Herrera

::: notes
Thank you for your attention. I'm happy to answer any questions about the proposal and discuss next steps for implementation.
:::
