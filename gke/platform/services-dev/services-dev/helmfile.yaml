helmfiles:
  - path: "../../../helmfiles/nginx-ingress/helmfile.yaml"
    values:
      - nginx_ingress_ip.yaml
      - nginxIngressRelease: ingx
        nginxIngressMetricsEnabled: false
repositories:
  - name: rouse-charts
    url: gs://images-4a3fb6-helm-charts/
  - name: bitnami
    url: https://charts.bitnami.com/bitnami
  - name: defectdojo
    url: https://raw.githubusercontent.com/DefectDojo/django-DefectDojo/helm-charts
  - name: k8s-as-helm
    url: https://ameijer.github.io/k8s-as-helm/
  - name: kedacore
    url: https://kedacore.github.io/charts
  - name: haproxytech
    url: https://haproxytech.github.io/helm-charts
  - name: prometheus-community
    url: https://prometheus-community.github.io/helm-charts
  - name: jetstack
    url: https://charts.jetstack.io
releases:
  - name: "honeycomb-refinery"
    labels:
      name: "honeycomb-refinery"
      group: "honeycomb-refinery"
    namespace: honeycomb-refinery
    chart: rouse-charts/honeycomb-refinery # ../../../../../helm-charts/charts/honeycomb-refinery
    version: 2.1.3
    values:
      - honeycomb-refinery-values.yaml
  - name: kube-state-metrics-to-sd
    chart: rouse-charts/prometheus-to-sd
    namespace: monitoring
    values:
      - serviceAccountName: "monitoring"
    labels:
      name: "kube-state-metrics-to-sd"
      group: "kube-state-metrics-to-sd"
  - name: "haproxy-rfm04-monitoring"
    labels:
      name: "haproxy-rfm04-monitoring"
      group: "haproxy-rfm04-monitoring"
    namespace: enterprise-pgbouncer-rfm04
    chart: rouse-charts/haproxy-monitoring
    values:
      - haproxy-rfm04-monitoring-values.yaml
  - name: "haproxy-rfm04"
    labels:
      name: "haproxy-rfm04"
      group: "haproxy-rfm04"
    namespace: enterprise-pgbouncer-rfm04
    chart: haproxytech/haproxy
    version: 1.20.0
    values:
      - haproxy-rfm04-values.yaml
  - name: "appraisals-report-develop"
    labels:
      name: "appraisals-report-develop"
      group: "appraisals-report-develop"
    namespace: appraisals-portal
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - appraisals-report-values.yaml
  - name: "appraisals-report-beta"
    labels:
      name: "appraisals-report-beta"
      group: "appraisals-report-beta"
    namespace: appraisals-portal
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - appraisals-beta-report-values.yaml
  - name: "appraisals-report-stage"
    labels:
      name: "appraisals-report-stage"
      group: "appraisals-report-stage"
    namespace: appraisals-portal
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - appraisals-stage-report-values.yaml
  - name: "catalog-assets"
    labels:
      name: "catalog-assets"
      group: "catalog-assets"
    namespace: sales-catalog
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - catalog-assets-values.yaml
  - name: "pypi-repo"
    namespace: pypi-repo
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - pypi-repo-values.yaml
  - name: "rabbitmq"
    labels:
      name: "rabbitmq"
      group: "rabbitmq"
    namespace: appraisals-rabbitmq
    chart: bitnami/rabbitmq
    version: ~10.3.9
    values:
      - rabbitmq-values.yaml
  - name: "auth-assets"
    labels:
      name: "auth-assets"
      group: "auth-assets"
    namespace: enterprise-authentication
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - auth-assets-values.yaml
  - name: "proposal-assets"
    labels:
      name: "proposal-assets"
      group: "proposal-assets"
    namespace: sales-proposals
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - proposal-assets-values.yaml
  - name: "inspector-assets"
    labels:
      name: "inspector-assets"
      group: "inspector-assets"
    namespace: appraisals-portal
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - inspector-assets-values.yaml
  - name: "gcsbucketproxy-test"
    labels:
      name: "gcsbucketproxy-test"
      group: "gcsbucketproxy-test"
    namespace: gcsbucketproxy-test
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - gcsbucketproxy-test-values.yaml

  - name: "playwright-gcsbucketproxy"
    labels:
      name: "playwright-gcsbucketproxy"
      group: "playwright-gcsbucketproxy"
    namespace: playwright-gcsbucketproxy
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - playwright-gcsbucketproxy-values.yaml

  - name: "mobile-assets-gcsbucketproxy"
    labels:
      name: "mobile-assets-gcsbucketproxy"
      group: "mobile-assets-gcsbucketproxy"
    namespace: mobile-assets
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - mobile-assets-gcsbucketproxy-values.yaml

  # Instances meant to run PGBouncer temporary tests
  # - name: "pgbouncer-dev"
  #   labels:
  #     name: "pgbouncer-dev"
  #     group: "pgbouncer-dev"
  #   namespace: "enterprise-pgbouncer-dev"
  #   chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
  #   version: ~0.2.8
  #   values:
  #     - pgbouncer-devops-values.yaml

  # - name: "pgbouncer-dev-replica"
  #   labels:
  #     name: "pgbouncer-dev-replica"
  #     group: "pgbouncer-dev-replica"
  #   namespace: "enterprise-pgbouncer-dev"
  #   chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
  #   version: ~0.2.8
  #   values:
  #     - pgbouncer-devops-replica-values.yaml

  - name: "pgbouncer-platform"
    labels:
      name: "pgbouncer-platform"
      group: "pgbouncer-platform"
    namespace: "enterprise-pgbouncer-platform"
    chart: rouse-charts/pgbouncer
    version: ~0.2.8
    values:
      - pgbouncer-platform-values.yaml
  - name: "keda"
    labels:
      name: "keda"
      group: "keda"
    namespace: enterprise-keda
    chart: kedacore/keda
    version: ~v2.14.0
  - name: circleci-runner-dev
    chart: rouse-charts/circleci-runner
    namespace: enterprise-circleci
    values:
      - circleci-values.yaml
      - secrets:
        runnerToken: '{{ fetchSecretValue "ref+gcpsecrets://services-dev-525bf6/circleci_self_hosted_runner" }}'
    labels:
      name: "circleci-runner-dev"
      group: "circleci-runner-dev"
  - name: circleci-runner-dev-priv
    chart: rouse-charts/circleci-runner
    version: 0.2.0
    namespace: enterprise-circleci-priv
    values:
      - circleci-values-priv.yaml
      - secrets:
        runnerToken: '{{ fetchSecretValue "ref+gcpsecrets://services-dev-525bf6/circleci_self_hosted_runner_priv" }}'
    labels:
      name: "circleci-runner-dev-priv"
      group: "circleci-runner-dev-priv"
  # - name: "defectdojo-pvc"
  #   labels:
  #     name: "defectdojo"
  #     group: "defectdojo"
  #   namespace: "enterprise-defectdojo"
  #   chart: k8s-as-helm/pvc
  #   version: ~1.0.5
  #   values:
  #     - defectdojo-pvc-values.yaml
  # - name: "defectdojo"
  #   labels:
  #     name: "defectdojo"
  #     group: "defectdojo"
  #   namespace: "enterprise-defectdojo"
  #   chart: defectdojo/defectdojo
  #   version: ~1.6.27
  #   values:
  #     - defectdojo-values.yaml
  - name: "selenium-grid"
    labels:
      name: "selenium-grid"
      group: "selenium-grid"
    namespace: enterprise-selenium-grid
    chart: rouse-charts/selenium-grid
    version: ~0.2.0
    values:
      - selenium-grid-values.yaml
  - name: "sysdig-castai-default"
    labels:
      name: "sysdig-castai-default"
      group: "sysdig"
    namespace: sysdig-agent
    chart: sysdig/sysdig-deploy
    version: 1.70.0
    set:
    - name: global.sysdig.accessKey
      value:  {{ fetchSecretValue "ref+gcpsecrets://services-dev-525bf6/sysdig_access_key" }}
    values:
      - sysdig-values-default.yaml
  - name: "sysdig-external-nodes"
    labels:
      name: "sysdig-external-nodes"
      group: "sysdig"
    namespace: sysdig-agent
    chart: sysdig/sysdig-deploy
    version: 1.70.0
    set:
    - name: global.sysdig.accessKey
      value:  {{ fetchSecretValue "ref+gcpsecrets://services-dev-525bf6/sysdig_access_key" }}
    values:
      - sysdig-values-external.yaml
  - name: "pgbouncer"
    labels:
      name: "pgbouncer"
      group: "pgbouncer"
    namespace: "enterprise-pg-bouncer"
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.8
    values:
      - pgbouncer-values.yaml
  - name: "pgbouncer-rfm03"
    labels:
      name: "pgbouncer-rfm03"
      group: "pgbouncer-rfm"
    namespace: "enterprise-pgbouncer-rfm03"
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.8
    values:
      - pgbouncer-rfm03-values.yaml
  - name: "pgbouncer-rfm03-replica"
    labels:
      name: "pgbouncer-rfm03-replica"
      group: "pgbouncer-rfm"
    namespace: "enterprise-pgbouncer-rfm03"
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.8
    values:
      - pgbouncer-rfm03-replica-values.yaml

  - name: "pgbouncer-rfm04"
    labels:
      name: "pgbouncer-rfm04"
      group: "pgbouncer-rfm"
    namespace: "enterprise-pgbouncer-rfm04"
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.10
    values:
      - pgbouncer-rfm04-values.yaml
  - name: "pgbouncer-rfm04-replica"
    labels:
      name: "pgbouncer-rfm04-replica"
      group: "pgbouncer-rfm"
    namespace: "enterprise-pgbouncer-rfm04"
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.10
    values:
      - pgbouncer-rfm04-replica-values.yaml

  - name: "pgbouncer-rfm07"
    labels:
      name: "pgbouncer-rfm07"
      group: "pgbouncer-rfm"
    namespace: "enterprise-pgbouncer-rfm07"
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.8
    values:
      - pgbouncer-rfm07-values.yaml
    set:
      - name: replicaCount
        value: '{{ fetchSecretValue "ref+gcpsecrets://services-dev-525bf6/rfm07-dev-pgbouncer-replica-count" }}'

  - name: "pgbouncer-rfm07-replica"
    labels:
      name: "pgbouncer-rfm07-replica"
      group: "pgbouncer-rfm"
    namespace: "enterprise-pgbouncer-rfm07"
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.10
    values:
      - pgbouncer-rfm07-replica-values.yaml

  - name: "haproxy-rfm07-monitoring"
    labels:
      name: "haproxy-rfm07-monitoring"
      group: "haproxy-rfm07-monitoring"
    namespace: enterprise-pgbouncer-rfm07
    chart: rouse-charts/haproxy-monitoring
    values:
      - haproxy-rfm07-monitoring-values.yaml
  - name: "haproxy-rfm07"
    labels:
      name: "haproxy-rfm07"
      group: "haproxy-rfm07"
    namespace: enterprise-pgbouncer-rfm07
    chart: haproxytech/haproxy
    version: 1.20.0
    values:
      - haproxy-rfm07-values.yaml

  - name: "pgbouncer-rfm-services01"
    labels:
      name: "pgbouncer-rfm-services01"
      group: "pgbouncer-rfm"
    namespace: "pgbouncer-rfmservices01"
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.8
    values:
      - pgbouncer-rfm-services01-values.yaml

  - name: "confluentd-monitoring"
    labels:
      name: "confluentd-monitoring"
      group: "confluentd-monitoring"
    namespace: "confluentd-monitoring"
    chart: rouse-charts/confluentd-prometheus
    values:
      - confluentd-monitoring.yaml
  - name: "rdo-downloads-develop"
    labels:
      name: "rdo-downloads-develop"
      group: "rdo-downloads-develop"
    namespace: analytics-rdo-api
    chart: rouse-charts/web-application
    version: ~3.5.0
    values:
      - rdo-downloads-values.yaml
  - name: "pgbouncer-alloydb-rfm08"
    labels:
      name: "pgbouncer-alloydb-rfm08"
      group: "pgbouncer-rfm"
    namespace: enterprise-pgbouncer-alloydb
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.13
    values:
      - pgbouncer-alloydb-rfm-values.yaml
  - name: "pgbouncer-rdo101"
    labels:
      name: "pgbouncer-rdo101"
      group: "pgbouncer-rdo"
    namespace: enterprise-pgbouncer-rdo101
    chart: rouse-charts/pgbouncer # ../../../../../helm-charts/charts/pgbouncer
    version: ~0.2.13
    values:
      - pgbouncer-rdo101-values.yaml
  - name: "haproxy-alloydb-monitoring"
    labels:
      name: "haproxy-alloydb-monitoring"
      group: "haproxy-alloydb-monitoring"
    namespace: enterprise-pgbouncer-alloydb
    chart: rouse-charts/haproxy-monitoring
    values:
      - haproxy-alloydb-monitoring-values.yaml
  - name: "haproxy-alloydb"
    labels:
      name: "haproxy-alloydb"
      group: "haproxy"
    namespace: enterprise-pgbouncer-alloydb
    chart: haproxytech/haproxy
    version: 1.20.0
    values:
      - haproxy-alloydb-values.yaml
  - name: "haproxy-etcxl-monitoring"
    labels:
      name: "haproxy-etcxl-monitoring"
      group: "haproxy-monitoring"
    namespace: enterprise-pgbouncer-rfm07
    chart: rouse-charts/haproxy-monitoring
    values:
      - haproxy-clients/haproxy-etcxl-monitoring-values.yaml
  - name: "haproxy-etcxl"
    labels:
      name: "haproxy-etcxl"
      group: "haproxy-rfm07"
    namespace: enterprise-pgbouncer-rfm07
    chart: haproxytech/haproxy
    version: 1.20.0
    values:
      - haproxy-clients/haproxy-etcxl-values.yaml
  - name: "haproxy-hg-monitoring"
    labels:
      name: "haproxy-hg-monitoring"
      group: "haproxy-monitoring"
    namespace: enterprise-pgbouncer-rfm07
    chart: rouse-charts/haproxy-monitoring
    values:
      - haproxy-clients/haproxy-hg-monitoring-values.yaml
  - name: "haproxy-hg"
    labels:
      name: "haproxy-hg"
      group: "haproxy-rfm07"
    namespace: enterprise-pgbouncer-rfm07
    chart: haproxytech/haproxy
    version: 1.20.0
    values:
      - haproxy-clients/haproxy-hg-values.yaml
  - name: "haproxy-u1-monitoring"
    labels:
      name: "haproxy-u1-monitoring"
      group: "haproxy-monitoring"
    namespace: enterprise-pgbouncer-rfm07
    chart: rouse-charts/haproxy-monitoring
    values:
      - haproxy-clients/haproxy-u1-monitoring-values.yaml
  - name: "haproxy-u1"
    labels:
      name: "haproxy-u1"
      group: "haproxy-rfm07"
    namespace: enterprise-pgbouncer-rfm07
    chart: haproxytech/haproxy
    version: 1.20.0
    values:
      - haproxy-clients/haproxy-u1-values.yaml
