web-application:
  containerPort: 5000

  service:
    type: NodePort
    port: 80
    targetPort: 5000

  serviceAccountName: sales-txns

  livenessProbe:
    httpGet:
      path: /hz
      port: 5000
    periodSeconds: 40
    successThreshold: 1
    timeoutSeconds: 1
    failureThreshold: 2

  readinessProbe:
    httpGet:
      path: /hz
      port: 5000
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 1
    failureThreshold: 2

  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 3
    targetCPUUtilizationPercentage: 95

  resources:
    requests:
      cpu: 200m
      memory: 256Mi

  volumeMounts:
    - mountPath: /home/<USER>
      name: config-volume

  volumes:
    - name: config-volume
      configMap:
        name: rfm-docs-proxy-web-application

  nodeSelector:
    auth-type: workload-identity

  tolerations:
    - key: application
      operator: "Equal"
      value: "web"
      effect: "NoSchedule"
    - key: workload-identity
      operator: "Equal"
      value: "enabled"
      effect: "NoSchedule"

  config:
    enabled: true
    data:
      config.yaml: |
        bucketName: services-dev-525bf6-rfm-documentation
        bindAddress: 0.0.0.0:5000
        defaultIndex: index.html

  appVersion: 0.0.177
  image:
    repository: us-docker.pkg.dev/images-4a3fb6/gcr.io/gcsbucketproxy

  ingress:
    annotations:
      kubernetes.io/ingress.class: "gce-internal"
      kubernetes.io/ingress.regional-static-ip-name: "rfm-docs-proxy"
      kubernetes.io/ingress.allow-http: "false"
    enabled: true
    hosts:
      - host: "rfm-docs.develop.rouseservices.com"
        paths:
          - path: /*
    tls:
      - secretName: develop-rouseservices-com

  branch_name: "dev"
  github_repo_url: "github.com-RouseServices-gcsbucketproxy"
  project_name: "gcsbucketproxy"
  environment_name: "dev" 
