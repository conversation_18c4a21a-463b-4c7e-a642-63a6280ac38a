pgbouncer:
  replicaCount: 12
  internalPort: 5432
  servicePort: 5432

  resources:
    requests:
      cpu: 1
      memory: "128Mi"

  pgbouncerExporter:
    enabled: false

  antiAffinity: soft
  tolerations:
    - key: application
      operator: "Equal"
      value: "web"
      effect: "NoSchedule"
    - key: workload-identity
      operator: "Equal"
      value: "enabled"
      effect: "NoSchedule"

  nodeSelector:
    auth-type: workload-identity

  poolMode: statement

  budget:
    minAvailable: 0

  image:
    repository: us-docker.pkg.dev/images-4a3fb6/gcr.io/pgbouncer
    tag: "2.0"
    pullPolicy: Always

  imagePullSecrets: []

  databases:
    "*":
      host: &ip 0.0.0.0
      port: &port 5433
    rfm_pipeline:
      dbname: rfm
      host: *ip
      port: *port
      pool_mode: session
      pool_size: 50
      min_pool_size: 0

  existingUsersSecret: "pgbouncer-pgbouncer-secret-userlist-txt"
  existingClientKey: "pgbouncer-cert-client"
  existingClientCert: "pgbouncer-cert-client"
  existingClientCa: "pgbouncer-cert-client"

  settings:
    auth_type: md5

  connectionLimits:
    maxClientConn: 20000
    maxUserConnections: 0 # unlimited
    defaultPoolSize: 100
    maxDbConnections: 500 # (8000 - replicas * 500)
    minPoolSize: 10
    reservePoolSize: 0
    reservePoolTimeout: 0

  helm2selector: false

  logConnections: 0
  logDisconnections: 0
  logStats: 0
  logPoolerErrors: 0
  verbose:  0
  statsPeriod: 60
  serverLifetime: 3600
  serverIdleTimeout: 300

  customSettings:
    unix_socket_dir: "/tmp"
    max_prepared_statements: "7"

  service:
    enabled: true
    type: LoadBalancer
    name: ""
    annotations:
      networking.gke.io/load-balancer-type: "Internal"
    loadBalancerIP: "************"

  global:
    namespacedDatabases: false

  serverTLSSSLMode: disable
  clientTLSSSLMode: verify-ca

  sidecarContainers: 
    - name: alloydb-proxy
      imagePullPolicy: IfNotPresent
      image: us-docker.pkg.dev/images-4a3fb6/gcr.io/alloydb-proxy:1.0
      command: ['/alloydb-auth-proxy', '-p=5433', 'projects/services-prod-e6fffc/locations/us-central1/clusters/rfm1001-prod/instances/rfm1001rp0-prod']
      ports:
        - name: alloydb
          containerPort: 5433
    
    
  serviceAccountName: enterprise-pgbouncer-rfm1001
