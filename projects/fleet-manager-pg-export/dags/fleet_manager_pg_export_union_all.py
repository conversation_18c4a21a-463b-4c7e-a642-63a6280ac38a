import os
import ast
from datetime import datetime, timedelta
from pathlib import Path
import importlib
import pendulum

from airflow import DAG
from airflow.models import TaskInstance
from airflow.models import Variable
from airflow.operators.python import PythonOperator, BranchPythonOperator
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)

from airflow.utils.task_group import TaskGroup
from shared_libs.bigquery_dataset import refresh_bq_dataset_list, CreateBigQueryDataset
from shared_libs.default_args import get_default_args
from shared_libs.helper_tasks.fleet_manager_maintain_union_all_table import (
    list_task_runs_within_window,
    enrich_task_runs_with_xcom,
)
from shared_libs.image_versions import get_full_image_name, get_gcr_registry
from shared_libs.kubernetes import get_image_pull_policy
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert
from shared_libs.slack_callback import task_fail_slack_alert

ENVIRONMENT = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
GCR_REGISTRY = get_gcr_registry(ENVIRONMENT)
IMAGE_PULL_POLICY = get_image_pull_policy(ENVIRONMENT)

VERSION_ID = "{{ logical_date.strftime('%Y%m%d%H%M%S') }}"
LOCAL_TZ = pendulum.timezone("America/Los_Angeles")

NAMESPACE = SERVICE_ACCOUNT_NAME = "fleet-manager-pg-export"

START_DATE = datetime(2025, 3, 28, tzinfo=LOCAL_TZ)

AIRFLOW_BQ_TRANSAC_CONCURR_POOL_TEMPLATE = (
    "ims-fleet-manager-pg-export-bq-transaction-concurrency-tablename-{TABLE_NAME}-pool"
)

if ENVIRONMENT != "prod":
    SCHEDULE_INTERVAL = "0,30 * * * *"
    BQ_DATASET_ENV = "_dev"
    GCS_PATH_ENV = "-dev"
    BQ_GCP_PROJECT_ID_UNION = "sales-data-dev-9ffb6c"
    BQ_GCP_DATASET_ID_UNION = BQ_DATASET_VIEW_ID = "fleet_manager_dev"
    BQ_DATASET_STAGE_ID = "fleet_manager_stage_dev"

else:  # PRODUCTION
    SCHEDULE_INTERVAL = Variable.get(
        "fleet_manager_pg_export_union_all_schedule_interval", "@hourly"
    )
    BQ_DATASET_ENV = ""
    GCS_PATH_ENV = ""
    BQ_GCP_PROJECT_ID_UNION = "sales-data-prod-2b1264"
    BQ_GCP_DATASET_ID_UNION = BQ_DATASET_VIEW_ID = "fleet_manager"
    BQ_DATASET_STAGE_ID = "fleet_manager_stage"

if ENVIRONMENT == "local":
    SCHEDULE_INTERVAL = None

MIN_WINDOW_VAR_NAME = "fleet_manager_pg_export_union_all_{TABLE_NAME}_min_window"

PWD = Path(os.path.dirname(os.path.realpath(__file__)))
SQL_FOLDER = PWD / "dag_file_dependencies" / "union_all"


def read_file(dependency_path):
    with open(SQL_FOLDER / dependency_path) as f:
        local_file = f.read()
    return local_file


# Airflow variable to override default shards, useful when testing in dev
override_client_shards = Variable.get(
    "fleet_manager_pg_export_override_client_shards", None
)

PG_FM_SCHEMA = "fleet_manager"

PIPELINE_CONFIG = importlib.import_module(
    ("fleet-manager-pg-export." if ENVIRONMENT != "local" else "")
    + "dag_file_dependencies.config"
)
PIPELINE_UTILS = importlib.import_module(
    ("fleet-manager-pg-export." if ENVIRONMENT != "local" else "")
    + "dag_file_dependencies.utils"
)

MAX_BQ_KEY_CONFIG = PIPELINE_UTILS.get_max_bq_key_config()

DAG_ID_PREFIX = "fleet-manager-pg-export"
BQ_GCP_PROJECT_ID_TEMPLATE = "rs-client-{CLIENT_CODE}-{CLIENT_VERSION}"
PG_AVRO_WRITER_CODEC = "snappy"
PG_GCS_AVRO_FILE_EXTENSION = (
    f".avro.{PG_AVRO_WRITER_CODEC}" if PG_AVRO_WRITER_CODEC else ".avro"
)
PG_AVRO_EXPORT_FILE_NAME_TEMPLATE = (
    f"gs://{BQ_GCP_PROJECT_ID_TEMPLATE}-raw-files{GCS_PATH_ENV}/pipeline"
    f"/{DAG_ID_PREFIX}/{{VERSION_ID}}/avro/{{PG_SCHEMA}}"
    f"/{{TABLE_NAME}}_{{CLIENT_CODE}}_{{VERSION_ID}}{PG_GCS_AVRO_FILE_EXTENSION}"
)
pull_parameter = "{{{{ task_instance.xcom_pull(task_ids='{task_ids}',key='{key}') }}}}"

# default dag args
custom_args = {
    "start_date": START_DATE,
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "namespace": NAMESPACE,
    "service_account_name": SERVICE_ACCOUNT_NAME,
    "image": get_full_image_name("fleet-manager-pg-export", GCR_REGISTRY),
    "on_failure_callback": task_fail_slack_alert,
    "owner": "<EMAIL>",
}
default_args = get_default_args(custom_args)


def update_var_date_control(variable_name, upper_window):
    Variable.set(
        variable_name,
        {"lower_window": upper_window},
        description="Next min date to identify clients that ran publishing pipeline and should upsert union all table. "
        f"NOTE: This Variable is updated by the pipeline 'fleet-manager-pg-export-union-all'",
        serialize_json=True,
    )


with open(SQL_FOLDER / "federated-create-clustered-table.sql") as f:
    create_table_bq_fed_sql_template = f.read()

with open(SQL_FOLDER / "federated-upsert-load.sql") as f:
    upsert_load_bq_fed_sql_template = f.read()

with open(SQL_FOLDER / "federated-upsert-delete.sql") as f:
    delete_bq_fed_sql_template = f.read()

with DAG(
    "fleet-manager-pg-export-union-all",
    default_args=default_args,
    description="Manage federated (all client's) tables in BigQuery",
    schedule=SCHEDULE_INTERVAL,
    catchup=False,
    dagrun_timeout=timedelta(hours=2),
    max_active_runs=1,
    tags=["fleet-manager"],
    on_failure_callback=dag_fail_pagerduty_alert(
        "fleet_manager_pg_export_pagerduty_api_key"
    ),
) as dag:
    upper_window = "{{ logical_date }}"

    variable_dict = {
        "ENVIRONMENT": ENVIRONMENT,
        "UNION_PROJECT_ID": BQ_GCP_PROJECT_ID_UNION,
        "UNION_DATASET_ID": BQ_GCP_DATASET_ID_UNION,
        "DATASET_VIEW_ID": BQ_DATASET_VIEW_ID,
        "DATASET_STAGE_ID": BQ_DATASET_STAGE_ID,
        # only valid for non MT clients
        "CLIENT_LEVEL_COUNT_STRATEGY": "metadata_table_count",
        # only valid for non MT clients
        "CLIENT_LEVEL_FLEET_CUSTOMER_ID_FILTER": "",
        "BQ_ENV_SUFFIX": BQ_DATASET_ENV,
    }

    create_refresh_datasets = refresh_bq_dataset_list(
        dag,
        BQ_GCP_PROJECT_ID_UNION,
        datasets=[
            CreateBigQueryDataset(
                dataset=BQ_GCP_DATASET_ID_UNION,
                project_id=BQ_GCP_PROJECT_ID_UNION,
                table_expiration_days=None,
            ),
        ],
    )

    def get_next_task_for_branch_operator(
        source_task_id, continue_task_id, ti: TaskInstance
    ):
        clients = ti.xcom_pull(task_ids=source_task_id).keys()
        if clients:
            return continue_task_id

    with TaskGroup(group_id=f"{PG_FM_SCHEMA}-tables", dag=dag) as fm_tables:

        for table_name, values in PIPELINE_UTILS.PG_TABLES_TO_EXPORT[
            "fleet_manager_tables"
        ].items():
            final_table_name = f"{PG_FM_SCHEMA}.{table_name}"
            control_var_name = MIN_WINDOW_VAR_NAME.format(TABLE_NAME=table_name)

            table_config = PIPELINE_CONFIG.pg_tables_config.get(final_table_name, None)

            incremental_config = PIPELINE_CONFIG.PG_EXPORT_TABLE_LIST_CONFIG.get(
                table_name, {}
            ).get("incremental", {})

            list_task_runs = PythonOperator(
                task_id=f"list-task-runs-{table_name}",
                python_callable=list_task_runs_within_window,
                op_kwargs={
                    "upper_window": upper_window,
                    "lower_window": "{{ var.json.get('"
                    + control_var_name
                    + "',{}).get('lower_window', '') }}",
                    "dag_id_pattern": "fleet-manager-pg-export-rfm%",
                    "is_per_shard_dag": True,
                    "task_id_pattern": f"%{table_name}-bq-version",
                    "dag_run_state": None,
                    "ouput_key": "dag_run_id",
                    "environment": ENVIRONMENT,
                    "granularity": "task_instance",
                },
                provide_context=True,
            )

            group_task_id = f"{PG_FM_SCHEMA}-tables"
            enrich_list_task_runs = PythonOperator(
                task_id=f"enrich-list-task-runs-{table_name}",
                python_callable=enrich_task_runs_with_xcom,
                op_kwargs={
                    "source_task_id": list_task_runs.task_id,
                    "tasks_config": {
                        f"{group_task_id}.{table_name}-aggregate-pg-metrics": [
                            {
                                "output_as": "source_table_key_min_value",
                                "value": f"min_{values['pk']}",
                            },
                            {
                                "output_as": "source_table_count_value",
                                "value": "num_rows",
                            },
                        ],
                        f"{group_task_id}.{table_name}-pg-to-gcs": [
                            {
                                "multiple_matches": True,
                                "output_as": "avro_source_uris",
                                "value": "avro_gcs_path",
                            }
                        ],
                    },
                    "union_all_chunk_size": "{{ var.value.fleet_manager_pg_export_union_all_chunk_size }}",
                },
                provide_context=True,
            )

            branch_check_clients = BranchPythonOperator(
                dag=dag,
                task_id=f"check-clients-{table_name}",
                provide_context=True,
                python_callable=get_next_task_for_branch_operator,
                op_kwargs={
                    "source_task_id": list_task_runs.task_id,
                    "continue_task_id": enrich_list_task_runs.task_id,
                },
            )

            declare_vars = """
            DECLARE min_stage NUMERIC;
            DECLARE max_current NUMERIC;
            DECLARE count_client NUMERIC;
            DECLARE count_federated NUMERIC;
            DECLARE count_stage NUMERIC;
            DECLARE column_names ARRAY<STRING>;
            DECLARE insert_statement STRING;

            CREATE TEMP TABLE debug_logs (log_timestamp TIMESTAMP, message STRING);
            """
            variable_dict["TABLE_KEY"] = values["pk"]
            variable_dict["LOAD_TABLE_TYPE"] = (
                "history"
                if incremental_config and "key" in incremental_config.keys()
                else "modified"
            )

            variable_dict["TABLE_NAME"] = table_name
            variable_dict["TABLE_KEY_CREATE"] = (
                f", {values['pk']}" if table_name != "fleet_customers" else ""
            )

            upsert_env = {
                "PROJECT_ID": BQ_GCP_PROJECT_ID_UNION,
                "PRINT_QUERY_RESULTS": "True",
                "VARIABLE_DICT": str(variable_dict),
                "FEDERATED_DECLARE_VARS": declare_vars,
                "FEDERATED_CREATE_QUERY": create_table_bq_fed_sql_template,
                "FEDERATED_LOAD_QUERY": upsert_load_bq_fed_sql_template,
                "FEDERATED_DELETE_QUERY": delete_bq_fed_sql_template,
            }

            def prepare_env(upsert_env_dict, task_run_list):
                task_run_list_fmt = ast.literal_eval(task_run_list)
                return [
                    {
                        **upsert_env_dict,
                        "TASK_RUN_LIST": str(task),
                    }
                    for task in task_run_list_fmt
                ]

            format_upsert_env = PythonOperator(
                task_id=f"format-upsert-env-{table_name}",
                python_callable=prepare_env,
                op_kwargs={
                    "upsert_env_dict": upsert_env,
                    "task_run_list": (
                        "{{ task_instance.xcom_pull("
                        f"task_ids='{enrich_list_task_runs.task_id}'"
                        ") }}"
                    ),
                },
            )

            upsert_table_union_id = f"upsert-{table_name}-union"
            upsert_table_union = GKEStartPodOperator.partial(
                task_id=upsert_table_union_id,
                name=upsert_table_union_id,
                arguments=[
                    "python",
                    "-m",
                    "fleet_manager_pg_export.cli",
                    "upsert-table-union",
                ],
                do_xcom_push=True,
                pool=AIRFLOW_BQ_TRANSAC_CONCURR_POOL_TEMPLATE.format(
                    TABLE_NAME=table_name
                ),
                max_active_tis_per_dag=1,
            ).expand(env_vars=format_upsert_env.output)

            update_date_control_var = PythonOperator(
                task_id=f"update-date-control-{table_name}",
                python_callable=update_var_date_control,
                op_kwargs={
                    "variable_name": control_var_name,
                    "upper_window": upper_window,
                },
            )

            (
                list_task_runs
                >> branch_check_clients
                >> enrich_list_task_runs
                >> format_upsert_env
                >> upsert_table_union
                >> update_date_control_var
            )

    (create_refresh_datasets >> fm_tables)
