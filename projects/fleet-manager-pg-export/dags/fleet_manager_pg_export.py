import ast
import collections
import importlib
import logging
import os
from datetime import timed<PERSON><PERSON>, datetime
from pathlib import Path

import pendulum
from airflow import DAG
from airflow.kubernetes.secret import Secret
from airflow.models import Variable
from airflow.models.taskinstance import TaskInstance
from airflow.operators.python import <PERSON><PERSON>perator, BranchPythonOperator
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule
from sensors.self_dagrun_conf_filter_sensor import SelfDagRunConfFilterSensor

from client_projects_constants import client_mappings
from fleet_manager_sharding_constants import sharded_clients_by_fleet_customer_id
from postgres_config import pg_db_shard_servers
from sharding_constants import sharded_clients
from shared_libs.bigquery_dataset import refresh_bq_dataset_list, CreateBigQueryDataset
from shared_libs.default_args import get_default_args, get_ssl_pgbouncer_secrets
from shared_libs.image_versions import get_full_image_name, get_gcr_registry
from shared_libs.kubernetes import get_image_pull_policy
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert
from shared_libs.slack_callback import task_fail_slack_alert
from shared_libs.trigger_dag import execute_trigger_dag
from airflow_datasets import RouseDatasets, register_dynamic_outlet_task


logging.basicConfig(format="%(message)s")
log = logging.getLogger(__name__)


ENVIRONMENT = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
GCR_REGISTRY = get_gcr_registry(ENVIRONMENT)
IMAGE_PULL_POLICY = get_image_pull_policy(ENVIRONMENT)


VERSION_ID = "{{ logical_date.strftime('%Y%m%d%H%M%S') }}"
UPPER_WINDOW = "{{ logical_date.strftime('%Y-%m-%d %H:%M:%S') }}"
LOCAL_TZ = pendulum.timezone("America/Los_Angeles")

DELTA_DAYS_DEFAULT = "{{ var.value.fleet_manager_pg_export_incremental_delta_days }}"
DELTA_DATES_VAR_NAME = "fleet_manager_pg_export_incremental_delta_dates"

NAMESPACE = "fleet-manager-pg-export"
SERVICE_ACCOUNT_NAME = NAMESPACE

DEFAULT_SERVER_NAME = "pg_bouncer"
ENGINE_CONFIG = Variable.get("postgres_engine_configuration", {}, deserialize_json=True)
POSTGRES_DEFAULT_SHARD_SERVER = Variable.get("postgres_default_shard_server")
SERVER_NAME = ENGINE_CONFIG.get("server", DEFAULT_SERVER_NAME)

PG_DB_SERVER = pg_db_shard_servers[POSTGRES_DEFAULT_SHARD_SERVER]["replica"][
    ENVIRONMENT
]
USE_CERT = str(pg_db_shard_servers[POSTGRES_DEFAULT_SHARD_SERVER]["use_certificates"])


# per envionment settings
# Development & Local# dev / local

if ENVIRONMENT != "prod":
    PROJECT_NAME = "management-dev-d6ba4d"
    CLUSTER_NAME = "composer-jobs-v3-dev"
    DB_SECRET_NAME = "de-fleet-manager-pg-export-dev"
    PG_DB_SECRET_NAME = "de-pg-fleet-manager-pg-export-dev"
    START_DATE = datetime(2022, 3, 22, tzinfo=LOCAL_TZ)
    BQ_GCP_PROJECT_ID_APPRAISALS = "appraisals-data-dev-c55fa4"
    BQ_GCP_PROJECT_ID_UNION = "sales-data-dev-9ffb6c"
    BQ_DATASET_ENV = "_dev"
    GCS_PATH_ENV = "-dev"
    RAS_SAS_HOST = "***********"  # gfdev03.rasgcp.net
    GETL01_SQL_SERVER = "*************"  # gdevetl01.rasgcp.net
    GETL01_LINKED_SERVER_ALIAS = "gdevetl01"
    END_TOPIC_PROJECT = "appraisals-data-dev-c55fa4"
    END_TOPIC_NAME = "ims-after-fleet-manager-pg-export-dev"

else:  # PRODUCTION
    PROJECT_NAME = "management-prod-837a97"
    CLUSTER_NAME = "composer-jobs-v3-prod"
    DB_SECRET_NAME = "de-fleet-manager-pg-export-prod"
    PG_DB_SECRET_NAME = "de-pg-fleet-manager-pg-export-prod"
    START_DATE = datetime(2022, 3, 23, tzinfo=LOCAL_TZ)
    BQ_GCP_PROJECT_ID_APPRAISALS = "appraisals-data-prod-707493"
    BQ_GCP_PROJECT_ID_UNION = "sales-data-prod-2b1264"
    BQ_DATASET_ENV = ""
    GCS_PATH_ENV = ""
    RAS_SAS_HOST = "***********"  # gdb02.rasgcp.net
    GETL01_SQL_SERVER = "getl01.rasgcp.net"
    GETL01_LINKED_SERVER_ALIAS = "getl01"
    END_TOPIC_PROJECT = "appraisals-data-prod-707493"
    END_TOPIC_NAME = "ims-after-fleet-manager-pg-export-prod"

DAG_ID_PREFIX = "fleet-manager-pg-export"
DAG_DEFAULT_TAGS = ["fleet-manager"]

PIPELINE_CONFIG = importlib.import_module(
    ("fleet-manager-pg-export." if ENVIRONMENT != "local" else "")
    + "dag_file_dependencies.config"
)
PIPELINE_UTILS = importlib.import_module(
    ("fleet-manager-pg-export." if ENVIRONMENT != "local" else "")
    + "dag_file_dependencies.utils"
)
# MSSQL settings
MSSQL_DB_TEMPLATE = "ras_DataMart_Sales_{CLIENT_ID}"

# Bigquery dataset settings
_bq_dataset = "fleet_manager"
BQ_DATASET_ID = f"{_bq_dataset}{BQ_DATASET_ENV}"
BQ_DATASET_STAGE_ID = f"{_bq_dataset}_stage{BQ_DATASET_ENV}"
BQ_DATASET_VERSION_ID = f"{_bq_dataset}_version{BQ_DATASET_ENV}"

_ssrs_bq_dataset = "ssrs_reports_sales_tables"
SSRS_BQ_DATASET_ID = f"{_ssrs_bq_dataset}{BQ_DATASET_ENV}"
SSRS_BQ_DATASET_VERSION_ID = f"{_ssrs_bq_dataset}_version{BQ_DATASET_ENV}"

_book_bq_dataset = "appraisal_book"
BOOK_BQ_DATASET_ID = f"{_book_bq_dataset}{BQ_DATASET_ENV}"

_sales_txn_bq_dataset = "sales_txn"
SALES_TXN_BQ_DATASET_ID = f"{_sales_txn_bq_dataset}{BQ_DATASET_ENV}"

_equipment_bq_dataset = "equipment"
EQUIPMENT_BQ_DATASET_ID = f"{_equipment_bq_dataset}{BQ_DATASET_ENV}"

BQ_GCP_PROJECT_ID_TEMPLATE = "rs-client-{CLIENT_CODE}-{CLIENT_VERSION}"
PG_AVRO_WRITER_CODEC = "snappy"
PG_GCS_AVRO_FILE_EXTENSION = (
    f".avro.{PG_AVRO_WRITER_CODEC}" if PG_AVRO_WRITER_CODEC else ".avro"
)
PG_AVRO_EXPORT_FILE_NAME_TEMPLATE = (
    f"gs://{BQ_GCP_PROJECT_ID_TEMPLATE}-raw-files{GCS_PATH_ENV}/pipeline"
    f"/{DAG_ID_PREFIX}/{{VERSION_ID}}/avro/{{PG_SCHEMA}}"
    f"/{{TABLE_NAME}}_{{CLIENT_CODE}}_{{VERSION_ID}}{PG_GCS_AVRO_FILE_EXTENSION}"
)

SSRS_AVRO_EXPORT_FILE_NAME_TEMPLATE = (
    f"gs://{BQ_GCP_PROJECT_ID_TEMPLATE}-raw-files{GCS_PATH_ENV}/pipeline"
    f"/{DAG_ID_PREFIX}/{{VERSION_ID}}/avro/{_ssrs_bq_dataset}"
    f"/{{TABLE_NAME}}_{{CLIENT_CODE}}_{{VERSION_ID}}.avro"
)

AIRFLOW_PG_SHARD_POOL = "ims-fleet-manager-pg-export-pg-{SHARD}-pool"
AIRFLOW_MSSQL_POOL = "ims-fleet-manager-pg-export-mssql-pool"
AIRFLOW_BQ_FEDERATED_SSRS_POOL = "ims-fleet-manager-pg-export-ssrs-federated-bq-pool"

PG_FM_SCHEMA = "fleet_manager"

PWD = Path(os.path.dirname(os.path.realpath(__file__)))
SQL_FOLDER = PWD / "dag_file_dependencies"
SQL_FOLDER_QA_REPORTS = SQL_FOLDER / "qa_reports"
SQL_FOLDER_SSRS_TABLES = SQL_FOLDER / "export_ssrs_sales_tables"
SQL_FOLDER_UNION_PROJECT = SQL_FOLDER / "union_project_operations"

with open(SQL_FOLDER_UNION_PROJECT / "federated-create-clustered-table.sql") as f:
    create_table_bq_fed_sql_template = f.read()

with open(SQL_FOLDER_UNION_PROJECT / "client-create-clustered-table.sql") as f:
    create_table_bq_client_sql_template = f.read()

with open(SQL_FOLDER_UNION_PROJECT / "client-upsert-load.sql") as f:
    upsert_load_bq_client_sql_template = f.read()

with open(SQL_FOLDER_UNION_PROJECT / "overwrite-load-data.sql") as f:
    overwrite_load_bq_sql_template = f.read()


NEVER_EXPIRE_TABLE = {}

EXPORT_MSSQL_TABLE_CONFIG = {
    "sfdc_account": {
        "DB_NAME": "ras_DataMart_Analytics_Cloud",
        "DB_SERVER": GETL01_SQL_SERVER,
    },
}

REPORT_QA_TABLE_CONFIG = {
    "operational_listed_need_cost_reviewed": {
        "SOURCE_ENGINE": "BQ",
    },
    "invoice_detail": {
        "SQL_DB": "ras_sas",
        "SQL_HOST": RAS_SAS_HOST,
    },
}

# client list
Client = collections.namedtuple(
    "Client",
    [
        "sales_code",
        "sales_client_id",
        "appraisals_client_id",
        "host_name",
        "ras_code",
        "client_version",
        "ims_conversion_status",
        "partition_id",
        "fleet_customer_id",
        "pg_db_server_name",
    ],
)

# Airflow variable to override default shards, useful when testing in dev
override_client_shards = Variable.get(
    "fleet_manager_pg_export_override_client_shards", None
)

IMS_ACTIVE_CLIENTS = Variable.get("ims_active_clients", "[]", deserialize_json=True)

TABLE_BATCHES_CONFIG = Variable.get(
    "fleet_manager_pg_export_table_batches", {}, deserialize_json=True
)


IMS_STATUS_LEGACY = "legacy"
IMS_STATUS_IMS = "ims"
IMS_STATUS_IMS_PREFERRED = "ims_preferred"


def check_status_legacy(ims_conversion_status, fleet_customer_id):
    return (
        ims_conversion_status == IMS_STATUS_IMS_PREFERRED and fleet_customer_id
    ) or (ims_conversion_status == IMS_STATUS_LEGACY)


DICT_SHARD_SERVERS = {
    item["client_code"]: (
        override_client_shards if override_client_shards else item["shard_server"]
    )
    for item in sharded_clients
}

ALL_IMS_ACTIVE_CLIENTS = {
    str(value["fleet_customer_id"]): {
        "sales_code": value["sales_client_code"],
        "sales_client_id": str(value["sales_client_id"]),
        "appraisals_client_id": str(value.get("appraisals_client_id", "")),
        "host_name": DICT_SHARD_SERVERS.get(ras_code),
        "ras_code": ras_code,
        "client_version": str(value["client_version"]),
        "ims_conversion_status": value.get("ims_conversion_status", IMS_STATUS_LEGACY),
        "partition_id": str(value.get("partition_id", value["fleet_customer_id"])),
        "fleet_customer_id": str(value["fleet_customer_id"]),
        "pg_db_server_name": (
            sharded_clients_by_fleet_customer_id.get(
                str(value.get("fleet_customer_id")), {}
            ).get("shard", POSTGRES_DEFAULT_SHARD_SERVER)
            if SERVER_NAME == DEFAULT_SERVER_NAME
            else SERVER_NAME
        ),
    }
    for ras_code, value in client_mappings.items()
    if value.get("fleet_customer_id", None) is not None
    and "sales_client_code" in value
    and "sales_client_id" in value
    and ras_code in IMS_ACTIVE_CLIENTS
}

PG_SERVER_NAMES = set(
    [client["pg_db_server_name"] for client in ALL_IMS_ACTIVE_CLIENTS.values()]
)

# default dag args
CUSTOM_ARGS = {
    "owner": "<EMAIL>",
    "start_date": START_DATE,
    "retries": 4,
    "retry_delay": timedelta(minutes=5),
    "retry_exponential_backoff": True,
    "max_retry_delay": timedelta(minutes=10),
    "namespace": NAMESPACE,
    "service_account_name": SERVICE_ACCOUNT_NAME,
    "project_id": PROJECT_NAME,
    "cluster_name": CLUSTER_NAME,
    "image": get_full_image_name("fleet-manager-pg-export", GCR_REGISTRY),
    "weight_rule": "upstream",
    "on_failure_callback": task_fail_slack_alert,
    "pool": "fleet-manager-pg-export-task-limit-pool",
}
DEFAULT_ARGS = get_default_args(CUSTOM_ARGS)

# secrets/passwords
SECRET_DB_USERNAME = Secret(
    deploy_type="env", deploy_target="WIN_USER", secret=DB_SECRET_NAME, key="username"
)
SECRET_DB_PASSWORD = Secret(
    deploy_type="env",
    deploy_target="WIN_PASSWORD",
    secret=DB_SECRET_NAME,
    key="password",
)

SECRET_PG_DB_USERNAME = Secret(
    deploy_type="env",
    deploy_target="PG_USERNAME",
    secret=PG_DB_SECRET_NAME,
    key="username",
)

SECRET_PG_DB_PASSWORD = Secret(
    deploy_type="env",
    deploy_target="PG_PASSWORD",
    secret=PG_DB_SECRET_NAME,
    key="password",
)

pull_parameter = "{{{{ task_instance.xcom_pull(task_ids='{task_ids}',key='{key}') }}}}"


def get_parameters(**kwargs):
    fleet_customer_id = str(kwargs["dag_run"].conf.get("fleet_customer_id"))
    values = ALL_IMS_ACTIVE_CLIENTS[fleet_customer_id]
    task_instance = kwargs["task_instance"]
    for k, v in values.items():
        task_instance.xcom_push(key=k, value=v)

    pg_db_server_name = values["pg_db_server_name"]
    task_instance.xcom_push(
        key="pg_db_server",
        value=pg_db_shard_servers[pg_db_server_name]["replica"][ENVIRONMENT],
    )
    task_instance.xcom_push(
        key="pg_db_name", value=pg_db_shard_servers[pg_db_server_name]["db_name"]
    )
    task_instance.xcom_push(
        key="use_cert",
        value=str(pg_db_shard_servers[pg_db_server_name]["use_certificates"]),
    )

    host_name = values.get("host_name")
    task_instance.xcom_push(
        key="db_link_shard",
        value=(
            f"[{host_name.split('.')[0]}]."
            if ENVIRONMENT == "prod" and host_name
            else ""
        ),
    )

    task_instance.xcom_push(key="fleet_customer_id", value=fleet_customer_id)

    return


# Util functions
def list_sql_files_in_dir(dir_name):
    return [r for r in dir_name.iterdir() if r.is_dir() and any(r.iterdir())]


def format_extraction_query_fn(
    ti: TaskInstance,
    table_name: str,
    query: str,
    variable_dict: dict,
    metrics: dict,
    upper_window: str,
    delta_days_default: int,
    ras_code: str,
):
    query = PIPELINE_UTILS.get_query_formated(
        query=query,
        variable_dict=variable_dict,
        metrics=metrics,
        upper_window=upper_window,
        delta_days_default=delta_days_default,
        ras_code=ras_code,
        rfm_schema=f"rfm_{ras_code}",
    )

    ti.xcom_push(key="query", value=query)
    ti.xcom_push(
        key="never_expire_copy",
        value="true" if table_name in NEVER_EXPIRE_TABLE.get(ras_code, []) else "false",
    )


def format_extraction_query_multiple_queries_fn(
    ti: TaskInstance,
    table_name: str,
    query: str,
    variable_dict: dict,
    metrics: dict,
    upper_window: str,
    delta_days_default: int,
    ras_code: str,
    table_key: str,
    index_metrics: str,
):
    queries = {}
    query = PIPELINE_UTILS.get_query_formated(
        query=query,
        variable_dict=variable_dict,
        metrics=metrics,
        upper_window=upper_window,
        delta_days_default=delta_days_default,
        ras_code=ras_code,
        rfm_schema=f"rfm_{ras_code}",
    )

    if index_metrics:
        index_metrics = ast.literal_eval(index_metrics)
        for v in index_metrics:
            queries[int(v["rownumber"])] = (
                f"{query} AND {table_key} BETWEEN {v['min_key_id']} AND {v['max_key_id']}"
            )
            queries["metrics"] = str({"num_rows": v["total_count"]})

    else:
        for i in range(TABLE_BATCHES_CONFIG.get(table_name)):
            queries[i] = query
            queries["metrics"] = str({"num_rows": 0})

    ti.xcom_push(key="queries", value=queries)
    ti.xcom_push(
        key="never_expire_copy",
        value="true" if table_name in NEVER_EXPIRE_TABLE.get(ras_code, []) else "false",
    )


# Reading SQL file templates once for SSRS Tables and QA Reports
SSRS_PATHS = list_sql_files_in_dir(SQL_FOLDER_SSRS_TABLES)

# skipping sfdc_account for dev, this server most os time is off online, can be exported on demand
if ENVIRONMENT != "prod":
    SSRS_PATHS = [p for p in SSRS_PATHS if "sfdc_account" not in p.name]

SSRS_TABLE_NAMES = [p.name for p in SSRS_PATHS]
SSRS_SQL_TEMPLATES = {}
for table in SSRS_PATHS:
    with open(table / "extraction_query.sql") as f:
        mssql_query = f.read()

    SSRS_SQL_TEMPLATES[table] = {
        "mssql_query": mssql_query,
    }


REPORTS_SQL_TEMPLATES = {}
for report in list_sql_files_in_dir(SQL_FOLDER_QA_REPORTS):
    with open(report / "mssql-query.sql") as f:
        mssql_query = f.read()

    with open(report / "bq-query.sql") as f:
        bq_query = f.read()

    REPORTS_SQL_TEMPLATES[report] = {
        "mssql_query": mssql_query,
        "bq_query": bq_query,
    }


MAX_BQ_KEY_CONFIG = PIPELINE_UTILS.get_max_bq_key_config()
secrets_pg_db_ssl = get_ssl_pgbouncer_secrets(ENVIRONMENT)


FORMAT_QUERY_TASK_ID_TEMPLATE = "{table_name}-format-extraction-query"
GET_INDEXES_TASK_ID_TEMPLATE = "{table_name}-get-indexes"

DAGRUN_TIMEOUT = timedelta(hours=14)

if PIPELINE_UTILS.PG_TABLES_TO_EXPORT:
    for shard_name in PG_SERVER_NAMES:
        dag_id = f"{DAG_ID_PREFIX}-{shard_name}"
        AIRFLOW_PG_POOL = AIRFLOW_PG_SHARD_POOL.format(SHARD=shard_name)
        with DAG(
            dag_id,
            default_args=DEFAULT_ARGS,
            description="Export fleet manager data from RFM to BigQuery RFM Prod",
            schedule_interval=None,
            catchup=False,
            dagrun_timeout=DAGRUN_TIMEOUT,
            max_active_runs=int(
                Variable.get("fleet_manager_pg_export_max_active_runs", 40)
            ),
            tags=DAG_DEFAULT_TAGS,
            on_failure_callback=dag_fail_pagerduty_alert(
                "fleet_manager_pg_export_pagerduty_api_key"
            ),
            max_active_tasks=384,
        ) as dag:

            wait_client_dag_id = "wait-client-dag"
            wait_client_dag = SelfDagRunConfFilterSensor(
                task_id=wait_client_dag_id,
                instances_limit=0,
                poke_interval=int(timedelta(minutes=5).total_seconds()),
                mode="reschedule",
                timeout=int(DAGRUN_TIMEOUT.total_seconds()),
            )

            get_parameters_task_id = f"get-parameters"
            get_parameters_task = PythonOperator(
                task_id=get_parameters_task_id,
                do_xcom_push=True,
                provide_context=True,
                python_callable=get_parameters,
            )

            bq_gcp_project_id = BQ_GCP_PROJECT_ID_TEMPLATE.format(
                CLIENT_CODE=pull_parameter.format(
                    task_ids=get_parameters_task_id, key="ras_code"
                ),
                CLIENT_VERSION=pull_parameter.format(
                    task_ids=get_parameters_task_id, key="client_version"
                ),
            )

            create_refresh_datasets = refresh_bq_dataset_list(
                dag,
                bq_gcp_project_id,
                datasets=[
                    CreateBigQueryDataset(
                        dataset=BQ_DATASET_ID,
                        project_id=bq_gcp_project_id,
                        table_expiration_days=None,
                    ),
                    CreateBigQueryDataset(
                        dataset=BQ_DATASET_STAGE_ID,
                        project_id=bq_gcp_project_id,
                        table_expiration_days=PIPELINE_CONFIG.BQ_TABLE_EXPIRATION_DAYS,
                    ),
                    CreateBigQueryDataset(
                        dataset=BQ_DATASET_VERSION_ID,
                        project_id=bq_gcp_project_id,
                        table_expiration_days=PIPELINE_CONFIG.BQ_TABLE_EXPIRATION_DAYS,
                    ),
                    CreateBigQueryDataset(
                        dataset=BQ_DATASET_ID,
                        project_id=BQ_GCP_PROJECT_ID_UNION,
                        table_expiration_days=None,
                    ),
                    CreateBigQueryDataset(
                        dataset=SSRS_BQ_DATASET_ID,
                        project_id=bq_gcp_project_id,
                        table_expiration_days=None,
                    ),
                    CreateBigQueryDataset(
                        dataset=SSRS_BQ_DATASET_VERSION_ID,
                        project_id=bq_gcp_project_id,
                        table_expiration_days=PIPELINE_CONFIG.BQ_TABLE_EXPIRATION_DAYS,
                    ),
                    CreateBigQueryDataset(
                        dataset=SSRS_BQ_DATASET_ID,
                        project_id=BQ_GCP_PROJECT_ID_UNION,
                        table_expiration_days=None,
                    ),
                ],
            )

            chars_to_replace = [" ", ".", ",", "#", "-", "_"]
            variable_dict = {
                "ENVIRONMENT": ENVIRONMENT,
                "CLIENT_ID": pull_parameter.format(
                    task_ids=get_parameters_task_id, key="client_id"
                ),
                "CLIENT_APPRAISALS_ID": pull_parameter.format(
                    task_ids=get_parameters_task_id, key="appraisals_client_id"
                ),
                "CLIENT_CODE": pull_parameter.format(
                    task_ids=get_parameters_task_id, key="ras_code"
                ),
                "CLIENT_VERSION": pull_parameter.format(
                    task_ids=get_parameters_task_id, key="client_version"
                ),
                "PARTITION_ID": pull_parameter.format(
                    task_ids=get_parameters_task_id, key="partition_id"
                ),
                "FLEET_CUSTOMER_ID": pull_parameter.format(
                    task_ids=get_parameters_task_id, key="fleet_customer_id"
                ),
                "CLIENT_LEVEL_FLEET_CUSTOMER_ID_FILTER": "",
                "CLIENT_LEVEL_COUNT_STRATEGY": "metadata_table_count",
                "PROJECT_ID": bq_gcp_project_id,
                "DATASET_ID": BQ_DATASET_VERSION_ID,
                "DATASET_VIEW_ID": BQ_DATASET_ID,
                "DATASET_STAGE_ID": BQ_DATASET_STAGE_ID,
                "SSRS_DATASET_ID": SSRS_BQ_DATASET_VERSION_ID,
                "BOOK_BQ_DATASET_ID": BOOK_BQ_DATASET_ID,
                "EQUIPMENT_BQ_DATASET_ID": EQUIPMENT_BQ_DATASET_ID,
                "SALES_TXN_BQ_DATASET_ID": SALES_TXN_BQ_DATASET_ID,
                "PG_SCHEMA": PG_FM_SCHEMA,
                "VERSION_ID": VERSION_ID,
                "APPRAISALS_PROJECT_ID": BQ_GCP_PROJECT_ID_APPRAISALS,
                "UNION_PROJECT_ID": BQ_GCP_PROJECT_ID_UNION,
                "UNION_DATASET_ID": BQ_DATASET_ID,
                "SSRS_UNION_DATASET_ID": SSRS_BQ_DATASET_ID,
                "SSRS_DATASET_VIEW_ID": SSRS_BQ_DATASET_ID,
                "GETL01_LINKED_SERVER_ALIAS": GETL01_LINKED_SERVER_ALIAS,
                "REPLACE_PREFIX": "LOWER(" + ("REPLACE(" * len(chars_to_replace)),
                "REPLACE_SUFFIX": "".join([f", '{c}', '')" for c in chars_to_replace])
                + ")",
                "DB_LINK_SHARD": pull_parameter.format(
                    task_ids=get_parameters_task_id, key="db_link_shard"
                ),
            }
            export_ssrs_task_ids = {}

            end_outlet_task = register_dynamic_outlet_task(
                dag=dag,
                fleet_customer_id_xcom_template=pull_parameter.format(
                    task_ids=get_parameters_task_id, key="fleet_customer_id"
                ),
                dataset_callback=RouseDatasets.fleet_manager_pg_export,
            )

            update_date_control_var = PythonOperator(
                task_id="update-dates-control",
                python_callable=PIPELINE_UTILS.update_var_date_control,
                op_kwargs={
                    "upper_window": UPPER_WINDOW,
                    "ras_code": pull_parameter.format(
                        task_ids=get_parameters_task_id, key="ras_code"
                    ),
                },
                execution_timeout=timedelta(minutes=180),
                pool="fleet-manager-pg-export-dates-control-pool",
            )

            trigger_attributes = {
                "env": ENVIRONMENT,
                "source": CLUSTER_NAME,
                "timestamp": "{{ ts }}",
                "version_id": VERSION_ID,
                "fleet_customer_id": pull_parameter.format(
                    task_ids=get_parameters_task_id, key="fleet_customer_id"
                ),
                "ras_code": pull_parameter.format(
                    task_ids=get_parameters_task_id, key="ras_code"
                ),
                "process": DAG_ID_PREFIX,
                "state": "finished",
                "status": "success",
            }
            trigger_publish_task = GKEStartPodOperator(
                task_id="publish-pubsub-end-message",
                name="publish-pubsub-end-message",
                dag=dag,
                image=get_full_image_name("publish-to-ps", GCR_REGISTRY),
                arguments=[
                    "bash",
                    "script.sh",
                ],
                env_vars={
                    "PROJECT_ID": END_TOPIC_PROJECT,
                    "TOPIC_ID": END_TOPIC_NAME,
                    "KEY_VALUE_PAIRS": ",".join(
                        [f"{k}={v}" for k, v in trigger_attributes.items()]
                    ),
                },
            )

            trigger_rfm_archiving = PythonOperator(
                task_id="trigger-rfm-archiving",
                python_callable=execute_trigger_dag,
                op_kwargs={
                    "trigger_dag_id": "rfm-archiving-trigger",
                    "dag_config": {
                        "fleet_customer_id": pull_parameter.format(
                            task_ids=get_parameters_task_id, key="fleet_customer_id"
                        )
                    },
                },
            )

            with TaskGroup(group_id=f"{PG_FM_SCHEMA}-tables", dag=dag) as fm_tables:

                bq_max_key_id = "bq-max-key"
                bq_max_key = GKEStartPodOperator(
                    task_id=bq_max_key_id,
                    name=bq_max_key_id,
                    arguments=[
                        "python",
                        "-m",
                        "fleet_manager_pg_export.cli",
                        "query-max-bigquery",
                    ],
                    do_xcom_push=True,
                    env_vars={
                        "PROJECT_ID": bq_gcp_project_id,
                        "DATASET_ID": BQ_DATASET_ID,
                        "MAX_BQ_KEY_CONFIG": str(MAX_BQ_KEY_CONFIG),
                    },
                )

                for table_name, values in PIPELINE_UTILS.PG_TABLES_TO_EXPORT[
                    "fleet_manager_tables"
                ].items():

                    # Per client Tasks for client tables (GCP Client Project)
                    gcs_path = PG_AVRO_EXPORT_FILE_NAME_TEMPLATE.format(
                        TABLE_NAME=table_name, **variable_dict
                    )

                    final_table_name = f"{PG_FM_SCHEMA}.{table_name}"

                    itersize_default = "100000"
                    export_pg_env = {
                        "GCS_GCP_PROJECT": bq_gcp_project_id,
                        "GCS_PATH": gcs_path,
                        "DB_SERVER": pull_parameter.format(
                            task_ids=get_parameters_task_id, key="pg_db_server"
                        ),
                        "DB_NAME": pull_parameter.format(
                            task_ids=get_parameters_task_id, key="pg_db_name"
                        ),
                        "USE_PG_CERT": pull_parameter.format(
                            task_ids=get_parameters_task_id, key="use_cert"
                        ),
                        "AVRO_WRITER_CODEC": PG_AVRO_WRITER_CODEC,
                        "KEY_COUNT_METRIC": "num_rows",
                        "ITERSIZE": itersize_default,
                        "ALLOW_TO_USE_SERVER_SIDE_CURSOR": str(False),
                        "QA_CHECKS_INFO": str({"num_rows": [], "min": [values["pk"]]}),
                    }

                    table_config = PIPELINE_CONFIG.pg_tables_config.get(
                        final_table_name, None
                    )
                    if table_config:
                        export_pg_env["AVRO_SCHEMA_OVERRIDE_FIELD_TYPES"] = str(
                            table_config.get("avro_schema_override_field_types", "")
                        )
                        export_pg_env["ITERSIZE"] = str(
                            table_config.get("itersize", itersize_default)
                        )
                        export_pg_env["PG_PARSE_JSON_FIELDS_AS_STRING"] = str(
                            table_config.get("parse_json_fields_as_string", False)
                        )

                    incremental_config = (
                        PIPELINE_CONFIG.PG_EXPORT_TABLE_LIST_CONFIG.get(
                            table_name, {}
                        ).get("incremental", {})
                    )

                    if incremental_config:
                        export_pg_env["TABLE_KEY_CONFIG"] = str(
                            {
                                "key": values["pk"],
                                "selecting_operator": "MIN_MAX",
                            }
                        )

                    table_metrics = (
                        "{{ task_instance.xcom_pull("
                        f"task_ids='{bq_max_key.task_id}'"
                        f")['metrics']['{table_name}'] }}}}"
                        if incremental_config
                        else None
                    )

                    export_pg_task_ids = []
                    if table_name in TABLE_BATCHES_CONFIG.keys():
                        get_indexes_task_id = GET_INDEXES_TASK_ID_TEMPLATE.format(
                            table_name=table_name
                        )
                        get_indexes_task = GKEStartPodOperator(
                            task_id=get_indexes_task_id,
                            name=get_indexes_task_id,
                            do_xcom_push=True,
                            dag=dag,
                            resources=PIPELINE_CONFIG.RESOURCES_SMALL_TASK,
                            execution_timeout=timedelta(hours=3),
                            arguments=[
                                "python",
                                "-m",
                                "fleet_manager_pg_export.cli",
                                "get-indexes",
                            ],
                            secrets=[SECRET_PG_DB_USERNAME, SECRET_PG_DB_PASSWORD]
                            + secrets_pg_db_ssl,
                            env_vars={
                                "ENV": ENVIRONMENT,
                                "PARTITION_ID": pull_parameter.format(
                                    task_ids=get_parameters_task_id, key="partition_id"
                                ),
                                "DB_SERVER": pull_parameter.format(
                                    task_ids=get_parameters_task_id, key="pg_db_server"
                                ),
                                "DB_NAME": pull_parameter.format(
                                    task_ids=get_parameters_task_id, key="pg_db_name"
                                ),
                                "USE_PG_CERT": pull_parameter.format(
                                    task_ids=get_parameters_task_id, key="use_cert"
                                ),
                                "NUMBER_OF_BATCHES": str(
                                    TABLE_BATCHES_CONFIG.get(table_name)
                                ),
                                "BATCH_SIZE": str(
                                    export_pg_env.get("ITERSIZE", itersize_default)
                                ),
                                "METRICS": table_metrics,
                                "TABLE_KEY": values["pk"],
                                "TABLE_NAME": table_name,
                            },
                            get_logs=True,
                            on_failure_callback=task_fail_slack_alert,
                        )

                        index_metrics = (
                            "{{ task_instance.xcom_pull("
                            f"task_ids='{get_indexes_task.task_id}'"
                            ")['metrics']}}"
                        )
                        format_extraction_query = PythonOperator(
                            task_id=FORMAT_QUERY_TASK_ID_TEMPLATE.format(
                                table_name=table_name
                            ),
                            python_callable=format_extraction_query_multiple_queries_fn,
                            op_kwargs={
                                "table_name": table_name,
                                "query": values["query"],
                                "variable_dict": variable_dict,
                                "metrics": table_metrics,
                                "upper_window": UPPER_WINDOW,
                                "delta_days_default": DELTA_DAYS_DEFAULT,
                                "ras_code": pull_parameter.format(
                                    task_ids=get_parameters_task_id, key="ras_code"
                                ),
                                "table_key": values["pk"],
                                "index_metrics": index_metrics,
                            },
                        )

                        with TaskGroup(
                            group_id=f"{table_name}-pg-to-gcs", dag=dag
                        ) as export_pg:

                            formatted_queries = (
                                "{{ task_instance.xcom_pull("
                                f"task_ids='{format_extraction_query.task_id}',key='queries'"
                                ") }}"
                            )

                            def get_valid_query_indexes(
                                queries: str, export_pg_id: str
                            ):
                                query_indexes = ast.literal_eval(queries).keys()
                                return [
                                    export_pg_id.format(i)
                                    for i in query_indexes
                                    if i != "metrics"
                                ]

                            export_pg_id = f"{table_name}-pg-to-gcs-{{}}"

                            branch_valid_indexes = BranchPythonOperator(
                                dag=dag,
                                task_id=f"{table_name}-branch-indexes",
                                provide_context=True,
                                python_callable=get_valid_query_indexes,
                                op_kwargs={
                                    "queries": formatted_queries,
                                    "export_pg_id": f"{export_pg.group_id}.{export_pg_id}",
                                },
                            )

                            for i in range(TABLE_BATCHES_CONFIG.get(table_name)):
                                formatted_query = (
                                    "{{ task_instance.xcom_pull("
                                    f"task_ids='{format_extraction_query.task_id}',key='queries'"
                                    f")['{i}'] }}}}"
                                )

                                export_pg_env["QUERY"] = formatted_query
                                export_pg_env["GCS_PATH"] = gcs_path.replace(
                                    ".avro", f"_{i}.avro"
                                )

                                export_pg_task = GKEStartPodOperator(
                                    task_id=export_pg_id.format(i),
                                    name=export_pg_id.format(i),
                                    image=get_full_image_name(
                                        "pg-to-gcs", GCR_REGISTRY
                                    ),
                                    arguments=[
                                        "python",
                                        "-m" "src.main",
                                        "load-from-query",
                                    ],
                                    resources=(
                                        table_config.get(
                                            "resources",
                                            PIPELINE_CONFIG.RESOURCES_MEDIUM_TASK,
                                        )
                                        if table_config
                                        else PIPELINE_CONFIG.RESOURCES_MEDIUM_TASK
                                    ),
                                    secrets=[
                                        SECRET_PG_DB_USERNAME,
                                        SECRET_PG_DB_PASSWORD,
                                    ]
                                    + secrets_pg_db_ssl,
                                    execution_timeout=DAGRUN_TIMEOUT,
                                    do_xcom_push=True,
                                    env_vars=export_pg_env,
                                    pool=AIRFLOW_PG_POOL,
                                )
                                export_pg_task_ids.append(export_pg_task.task_id)
                                branch_valid_indexes >> export_pg_task

                        avro_source_uris = gcs_path.replace(".avro", f"*.avro")

                        (
                            bq_max_key
                            >> get_indexes_task
                            >> format_extraction_query
                            >> export_pg
                        )
                    else:
                        format_extraction_query = PythonOperator(
                            task_id=FORMAT_QUERY_TASK_ID_TEMPLATE.format(
                                table_name=table_name
                            ),
                            python_callable=format_extraction_query_fn,
                            op_kwargs={
                                "table_name": table_name,
                                "query": values["query"],
                                "variable_dict": variable_dict,
                                "metrics": table_metrics,
                                "upper_window": UPPER_WINDOW,
                                "delta_days_default": DELTA_DAYS_DEFAULT,
                                "ras_code": pull_parameter.format(
                                    task_ids=get_parameters_task_id, key="ras_code"
                                ),
                            },
                        )

                        formatted_query = (
                            "{{ task_instance.xcom_pull("
                            f"task_ids='{format_extraction_query.task_id}',key='query'"
                            ") }}"
                        )

                        export_pg_env["QUERY"] = formatted_query

                        export_pg_id = f"{table_name}-pg-to-gcs"
                        export_pg = GKEStartPodOperator(
                            task_id=export_pg_id,
                            name=export_pg_id,
                            image=get_full_image_name("pg-to-gcs", GCR_REGISTRY),
                            arguments=["python", "-m" "src.main", "load-from-query"],
                            resources=(
                                table_config.get(
                                    "resources", PIPELINE_CONFIG.RESOURCES_MEDIUM_TASK
                                )
                                if table_config
                                else PIPELINE_CONFIG.RESOURCES_MEDIUM_TASK
                            ),
                            secrets=[SECRET_PG_DB_USERNAME, SECRET_PG_DB_PASSWORD]
                            + secrets_pg_db_ssl,
                            execution_timeout=DAGRUN_TIMEOUT,
                            do_xcom_push=True,
                            env_vars=export_pg_env,
                            pool=AIRFLOW_PG_POOL,
                        )
                        export_pg_task_ids.append(export_pg.task_id)
                        avro_source_uris = (
                            "{{ task_instance.xcom_pull("
                            f"task_ids='{export_pg.task_id}'"
                            ")['avro_gcs_path'] }}"
                        )

                        format_extraction_query >> export_pg
                        if incremental_config:
                            bq_max_key >> format_extraction_query

                    agg_pg_metrics_task = PythonOperator(
                        task_id=f"{table_name}-aggregate-pg-metrics",
                        python_callable=PIPELINE_UTILS.aggregate_pg_metrics,
                        provide_context=True,
                        trigger_rule=TriggerRule.NONE_FAILED,
                        op_kwargs={
                            "task_ids": export_pg_task_ids,
                            "min_key": f"min_{values['pk']}",
                        },
                    )

                    source_table_key_min_value = (
                        "{{ task_instance.xcom_pull("
                        f"task_ids='{agg_pg_metrics_task.task_id}'"
                        f")['min_{values['pk']}']"
                        "}}"
                    )
                    source_table_count_value = (
                        "{{ task_instance.xcom_pull("
                        f"task_ids='{agg_pg_metrics_task.task_id}'"
                        ")['num_rows'] }}"
                    )

                    avro_to_bq_id = f"{table_name}-avro-to-bq"
                    avro_to_bq = GKEStartPodOperator(
                        task_id=avro_to_bq_id,
                        name=avro_to_bq_id,
                        image=get_full_image_name("avro-to-bq", GCR_REGISTRY),
                        arguments=["python3", "main.py", "load-in-bq"],
                        execution_timeout=timedelta(minutes=10),
                        trigger_rule=TriggerRule.NONE_FAILED,
                        env_vars={
                            "SOURCE_URIS": avro_source_uris,
                            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
                            "DECIMAL_TARGET_TYPES": "NUMERIC,BIGNUMERIC",
                            "GCP_PROJECT_ID": bq_gcp_project_id,
                            "DESTINATION": f"{BQ_DATASET_STAGE_ID}.{table_name}_{VERSION_ID}",
                            "PUBLISH_PRODUCTION_VIEW": "False",
                            "TABLE_TYPE": "{{ var.value.fleet_manager_pg_export_bq_stage_table_type }}",
                        },
                    )

                    never_expire_copy_value = (
                        "{{ task_instance.xcom_pull("
                        f"task_ids='{format_extraction_query.task_id}',key='never_expire_copy'"
                        ") or 'false' }}"
                    )

                    upsert_load_client_bq_sql = (
                        upsert_load_bq_client_sql_template.strip().format(
                            TABLE_NAME=table_name,
                            SOURCE_URIS=avro_source_uris,
                            TABLE_KEY=values["pk"],
                            CREATE_VERSION_COPY=(
                                "true"
                                if incremental_config.get("datetime_range")
                                or table_name == "fleet_customers"
                                else "false"
                            ),
                            NEVER_EXPIRE_COPY=never_expire_copy_value,
                            STAGE_VERSION_TABLE_KEY_MIN=source_table_key_min_value,
                            STAGE_VERSION_TABLE_COUNT=source_table_count_value,
                            LOAD_TABLE_TYPE=(
                                "history"
                                if incremental_config
                                and "key" in incremental_config.keys()
                                else "modified"
                            ),
                            **variable_dict,
                        )
                    )

                    create_table_client_bq_sql = (
                        create_table_bq_client_sql_template.strip().format(
                            TABLE_NAME=table_name,
                            TABLE_KEY=(
                                f", {values['pk']}"
                                if table_name != "fleet_customers"
                                else ""
                            ),
                            **variable_dict,
                        )
                    )

                    declare_vars = """
                    DECLARE min_stage NUMERIC;
                    DECLARE max_current NUMERIC;
                    DECLARE count_client NUMERIC;
                    DECLARE count_federated NUMERIC;
                    DECLARE count_stage NUMERIC;
                    DECLARE column_names ARRAY<STRING>;
                    DECLARE insert_statement STRING;
                    """

                    create_bq_version_id = f"{table_name}-bq-version"
                    create_bq_version = GKEStartPodOperator(
                        task_id=create_bq_version_id,
                        name=create_bq_version_id,
                        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                        arguments=["python3", "main.py", "execute-bigquery"],
                        env_vars={
                            "GCP_PROJECT_ID": bq_gcp_project_id,
                            "PRINT_QUERY_RESULTS": "True",
                            "QUERY": "\n".join(
                                [
                                    declare_vars,
                                    create_table_client_bq_sql,
                                    upsert_load_client_bq_sql,
                                ]
                            ),
                        },
                        retry_delay=timedelta(minutes=10),
                    )

                    migrate_bq_table_id = f"{table_name}-migrate-bq-schema"
                    migrate_bq_table = GKEStartPodOperator(
                        task_id=migrate_bq_table_id,
                        name=migrate_bq_table_id,
                        arguments=[
                            "python",
                            "-m",
                            "fleet_manager_pg_export.cli",
                            "execute-bq-migration",
                        ],
                        execution_timeout=timedelta(minutes=10),
                        env_vars={
                            "PROJECT_ID_UNION": BQ_GCP_PROJECT_ID_UNION,
                            "PROJECT_ID": bq_gcp_project_id,
                            "DATASET_ID": BQ_DATASET_STAGE_ID,
                            "DATASET_CURRENT": BQ_DATASET_ID,
                            "TABLE_ID": f"{table_name}_{VERSION_ID}",
                            "TABLE_NAME": table_name,
                        },
                    )

                    export_qa_id = f"{table_name}-qa"
                    export_qa = GKEStartPodOperator(
                        task_id=export_qa_id,
                        name=export_qa_id,
                        arguments=[
                            "python",
                            "-m",
                            "fleet_manager_pg_export.cli",
                            "qa-bigquery",
                        ],
                        env_vars={
                            "VERSION_ID": VERSION_ID,
                            "PARTITION_ID": pull_parameter.format(
                                task_ids=get_parameters_task_id, key="partition_id"
                            ),
                            "FLEET_CUSTOMER_ID": pull_parameter.format(
                                task_ids=get_parameters_task_id, key="fleet_customer_id"
                            ),
                            "PROJECT_ID": bq_gcp_project_id,
                            "DATASET_ID": BQ_DATASET_VERSION_ID,
                            "DATASET_STAGE_ID": BQ_DATASET_STAGE_ID,
                            "DATASET_VIEW_ID": BQ_DATASET_ID,
                            "TABLE_NAME": table_name,
                            "QA_COLUMNS": str({"num_rows": []}),
                            "METRICS": (
                                f"{{{{ task_instance.xcom_pull(task_ids='{export_pg.task_id}')['metrics'] }}}}"
                                if table_name not in TABLE_BATCHES_CONFIG.keys()
                                else f"{{{{ task_instance.xcom_pull(task_ids='{format_extraction_query.task_id}',key='queries')['metrics'] }}}}"
                            ),
                            "INCREMENTAL_CONFIG": (
                                str(incremental_config) if incremental_config else ""
                            ),
                            "METRICS_EXTRA": table_metrics,
                            "TABLE_KEY": values["pk"],
                        },
                    )

                    export_pg >> agg_pg_metrics_task >> create_bq_version
                    (
                        export_pg
                        >> avro_to_bq
                        >> migrate_bq_table
                        >> create_bq_version
                        >> export_qa
                    )

            fm_tables >> [
                update_date_control_var,
                trigger_publish_task,
                trigger_rfm_archiving,
                end_outlet_task,
            ]

            variable_dict_ssrs = variable_dict.copy()
            variable_dict_ssrs["UNION_DATASET_ID"] = SSRS_BQ_DATASET_ID
            variable_dict_ssrs["DATASET_ID"] = SSRS_BQ_DATASET_VERSION_ID
            variable_dict_ssrs["DATASET_VIEW_ID"] = SSRS_BQ_DATASET_ID

            with TaskGroup(group_id="ssrs-sales-tables", dag=dag) as ssrs_tables:

                ssrs_upsert_bq_queries = []
                ssrs_export_qas = []

                for table, sql_templates in SSRS_SQL_TEMPLATES.items():
                    table_name = table.name

                    # Per client Tasks for client tables (GCP Client Project)
                    ssrs_gcs_path = SSRS_AVRO_EXPORT_FILE_NAME_TEMPLATE.format(
                        TABLE_NAME=table_name, **variable_dict
                    )

                    mssql_query = sql_templates["mssql_query"].format(**variable_dict)

                    table_config = EXPORT_MSSQL_TABLE_CONFIG.get(table_name, {})

                    mssql_to_gcs_id = f"{table_name}-ss-to-gcs"
                    mssql_to_gcs = GKEStartPodOperator(
                        task_id=mssql_to_gcs_id,
                        name=mssql_to_gcs_id,
                        image=get_full_image_name("ss-to-gcs", GCR_REGISTRY),
                        do_xcom_push=True,
                        resources=PIPELINE_CONFIG.RESOURCES_DEFAULT,
                        arguments=["python3", "main.py", "load-from-query"],
                        secrets=[SECRET_DB_USERNAME, SECRET_DB_PASSWORD],
                        env_vars={
                            "DB_SERVER": table_config.get("DB_SERVER", RAS_SAS_HOST),
                            "DB_NAME": table_config.get("DB_NAME", "ras_sas"),
                            "QUERY": mssql_query,
                            "GCP_PROJECT": bq_gcp_project_id,
                            "GCS_PATH": ssrs_gcs_path,
                            "QA_COLUMNS": str({"num_rows": []}),
                            "WRITE_LOGS": "False",
                        },
                        execution_timeout=timedelta(minutes=180),
                        pool=AIRFLOW_MSSQL_POOL,
                    )
                    export_ssrs_task_ids[table_name] = mssql_to_gcs.task_id

                    avro_to_bq_id = f"{table_name}-avro-to-bq"
                    avro_to_bq = GKEStartPodOperator(
                        task_id=avro_to_bq_id,
                        name=avro_to_bq_id,
                        image=get_full_image_name("avro-to-bq", GCR_REGISTRY),
                        arguments=["python3", "main.py", "load-in-bq"],
                        execution_timeout=timedelta(minutes=10),
                        env_vars={
                            "SOURCE_URIS": ssrs_gcs_path,
                            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
                            "GCP_PROJECT_ID": bq_gcp_project_id,
                            "DESTINATION": f"{SSRS_BQ_DATASET_VERSION_ID}.{table_name}_{VERSION_ID}",
                            "VIEW_DESTINATION": f"{bq_gcp_project_id}.{SSRS_BQ_DATASET_ID}.{table_name}",
                            "TABLE_TYPE": "{{ var.value.fleet_manager_pg_export_bq_stage_table_type }}",
                        },
                    )

                    export_qa_id = f"{table_name}-qa"
                    export_qa = GKEStartPodOperator(
                        task_id=export_qa_id,
                        name=export_qa_id,
                        arguments=[
                            "python",
                            "-m",
                            "fleet_manager_pg_export.cli",
                            "qa-bigquery",
                        ],
                        env_vars={
                            "VERSION_ID": VERSION_ID,
                            "PARTITION_ID": pull_parameter.format(
                                task_ids=get_parameters_task_id, key="partition_id"
                            ),
                            "FLEET_CUSTOMER_ID": pull_parameter.format(
                                task_ids=get_parameters_task_id, key="fleet_customer_id"
                            ),
                            "KEY_COUNT_METRIC": "num_rows",
                            "PROJECT_ID": bq_gcp_project_id,
                            "DATASET_ID": SSRS_BQ_DATASET_VERSION_ID,
                            "DATASET_VIEW_ID": SSRS_BQ_DATASET_ID,
                            "TABLE_NAME": table_name,
                            "QA_COLUMNS": str({"num_rows": []}),
                            "METRICS": "{{ task_instance.xcom_pull("
                            f"task_ids='{mssql_to_gcs.task_id}'"
                            ")['metrics'] }}",
                        },
                    )

                    # Per client Tasks for Federated tables (GCP Sales Project)
                    ssrs_table_key = PIPELINE_CONFIG.PG_EXPORT_TABLE_LIST_CONFIG[
                        table_name
                    ]["key"]
                    ssrs_create_table_bq_sql = (
                        create_table_bq_fed_sql_template.strip().format(
                            TABLE_NAME=table_name,
                            TABLE_KEY=f", {ssrs_table_key}",
                            **variable_dict_ssrs,
                        )
                    )

                    ssrs_upsert_bq_sql = overwrite_load_bq_sql_template.strip().format(
                        TABLE_NAME=table_name,
                        SOURCE_URIS=ssrs_gcs_path,
                        TABLE_KEY=(
                            f"CONCAT({ssrs_table_key})"
                            if table_name == "ras_sas_customer_asset_dimension_value"
                            else ssrs_table_key
                        ),
                        **variable_dict_ssrs,
                    )

                    ssrs_upsert_bq_queries.append(
                        "\n".join([ssrs_create_table_bq_sql, ssrs_upsert_bq_sql])
                    )

                    ssrs_export_qas.append(export_qa)

                    (mssql_to_gcs >> avro_to_bq >> export_qa)

                create_table_union_id = "upsert-table-union"
                create_table_union = GKEStartPodOperator(
                    task_id=create_table_union_id,
                    name=create_table_union_id,
                    image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                    arguments=["python3", "main.py", "execute-bigquery"],
                    env_vars={
                        "QUERY": "\n".join(ssrs_upsert_bq_queries),
                        "GCP_PROJECT_ID": BQ_GCP_PROJECT_ID_UNION,
                    },
                    pool=AIRFLOW_BQ_FEDERATED_SSRS_POOL,
                )

                ssrs_export_qas >> create_table_union

            # TODO: see: FMC-149
            # ignore_report_qa_checks = True
            # if not ignore_report_qa_checks and check_status_legacy(
            #     ims_conversion_status, fleet_customer_id
            # ):
            #
            #     with TaskGroup(
            #         group_id=f"{PG_FM_SCHEMA}-bi-reports-qa", dag=dag
            #     ) as fm_bi_reports_qa:
            #
            #         mssql_db = MSSQL_DB_TEMPLATE.format(**variable_dict)
            #
            #         for report, sql_templates in REPORTS_SQL_TEMPLATES.items():
            #             report_name = report.name
            #
            #             mssql_query = sql_templates["mssql_query"].format(
            #                 **variable_dict
            #             )
            #             bq_query = sql_templates["bq_query"].format(**variable_dict)
            #
            #             report_qa_table_config = REPORT_QA_TABLE_CONFIG.get(
            #                 report_name, {}
            #             )
            #
            #             report_qa_id = f"{report_name}-qa"
            #             report_qa = GKEStartPodOperator(
            #                 task_id=report_qa_id,
            #                 name=report_qa_id,
            #                 task_group=fm_bi_reports_qa,
            #                 secrets=[SECRET_DB_USERNAME, SECRET_DB_PASSWORD],
            #                 arguments=[
            #                     "python",
            #                     "-m",
            #                     "fleet_manager_pg_export.cli",
            #                     "qa-report",
            #                 ],
            #                 execution_timeout=timedelta(hours=3),
            #                 env_vars={
            #                     "ENV": ENVIRONMENT,
            #                     "SQL_DB": report_qa_table_config.get(
            #                         "SQL_DB", mssql_db
            #                     ),
            #                     "SQL_HOST": report_qa_table_config.get(
            #                         "SQL_HOST", host_name
            #                     ),
            #                     "PROJECT_ID": bq_gcp_project_id,
            #                     "BQ_QUERY": bq_query,
            #                     "SOURCE_QUERY": mssql_query,
            #                     "SOURCE_ENGINE": report_qa_table_config.get(
            #                         "SOURCE_ENGINE", "MSSQL"
            #                     ),
            #                 },
            #                 pool=AIRFLOW_MSSQL_POOL,
            #             )
            #
            #             [ssrs_tables, fm_tables] >> fm_bi_reports_qa

            create_refresh_datasets >> ssrs_tables

            (
                wait_client_dag
                >> get_parameters_task
                >> create_refresh_datasets
                >> fm_tables
            )
