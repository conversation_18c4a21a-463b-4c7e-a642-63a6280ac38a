-- *** START OF CREATE SCRIPT ***
CREATE TABLE IF NOT EXISTS
    `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}`
CLUSTER BY
    fleet_customer_id{TABLE_KEY_CREATE}
AS
    SELECT * FROM `{PROJECT_ID}.{DATASET_VIEW_ID}.{TABLE_NAME}`
    WHERE FALSE; -- we want just the table metadata in order to create our table in first time run

-- recreate federated table using clustering (if not clustered already) (SFMC-739)
IF (
      select count(1) from (
        SELECT ddl like '%CLUSTER BY%' clustered
        FROM `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.INFORMATION_SCHEMA.TABLES`
        WHERE table_name = '{TABLE_NAME}'
      ) where clustered is true
   ) = 0 THEN
    CREATE TABLE
        `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}_clustered`
    CLUSTER BY
        fleet_customer_id{TABLE_KEY_CREATE}
    AS
        SELECT * FROM `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}`;

    ALTER TABLE `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}` RENAME TO {TABLE_NAME}_bkp_cluster;

    ALTER TABLE `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}_clustered` RENAME TO {TABLE_NAME};

    DROP TABLE `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}_bkp_cluster`;
END IF;
-- *** END OF CREATE SCRIPT ***
