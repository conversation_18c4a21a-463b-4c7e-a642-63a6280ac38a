-- *** START OF LOAD SCRIPT ***
INSERT INTO debug_logs
SELECT CURRENT_TIMESTAMP(),
       FORMAT("Loading avro files to %s", '{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}');

LOAD DATA INTO `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}`
FROM FILES(
    format='AVRO',
    uris = [{MULTIPLE_SOURCE_URIS}],
    enable_logical_types = true,
    decimal_target_types = ['NUMERIC', 'BIGNUMERIC']
);

INSERT INTO debug_logs
  SELECT CURRENT_TIMESTAMP(),
         FORMAT("Executing client level table using strategy %s",
                '{CLIENT_LEVEL_COUNT_STRATEGY}');

IF '{CLIENT_LEVEL_COUNT_STRATEGY}' = 'metadata_table_count' THEN
  SET count_client = ({ALL_CLIENT_COUNT_SUM});
ELSE
  -- used for multi tenant clients
  SET count_client = (
    SELECT COUNT(1) FROM `{PROJECT_ID}.{DATASET_VIEW_ID}.{TABLE_NAME}`
    {CLIENT_LEVEL_FLEET_CUSTOMER_ID_FILTER}
  );
END IF;

SET count_federated = (
  SELECT COUNT(1) FROM `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}`
  WHERE fleet_customer_id IN ({FLEET_CUSTOMER_IDS})
);

-- if counts does not match in DEV only, we rebuild the federated table
IF count_client != count_federated AND '{ENVIRONMENT}' = 'dev' THEN
    INSERT INTO debug_logs
    SELECT CURRENT_TIMESTAMP(),
           FORMAT("DEV ONLY HELPER: Recreating stale table %s for fleet_customer_id %s",
                  '{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}',
                  '{FLEET_CUSTOMER_IDS}');

    DELETE
    FROM `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}`
    WHERE fleet_customer_id IN ({FLEET_CUSTOMER_IDS});

    SET column_names = (
        SELECT ARRAY_AGG(column_name ORDER BY ordinal_position)
        FROM `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.INFORMATION_SCHEMA.COLUMNS`
        WHERE table_name = '{TABLE_NAME}'
    );

    SET insert_statement = (
        SELECT CONCAT(
            'INSERT INTO `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}` (',
            ARRAY_TO_STRING(column_names, ','),
            ') SELECT ',
            ARRAY_TO_STRING(column_names, ','),
            ' FROM (',
            {DEV_INSERT_UNION_ALL},
            ')'
        )
    );

    EXECUTE IMMEDIATE insert_statement;
    -- update count after rebuild
    SET count_federated = (
        SELECT COUNT(1) FROM `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}`
        WHERE fleet_customer_id IN ({FLEET_CUSTOMER_IDS})
    );
END IF;

-- perform QA check
SELECT CURRENT_TIMESTAMP(),
      IF(count_client = count_federated,
        'QA Count: PASS',
        ERROR('QA Count: FAIL between federated table and client project')
      )
UNION ALL
SELECT * FROM debug_logs
order by 1;
-- *** END OF LOAD SCRIPT ***
