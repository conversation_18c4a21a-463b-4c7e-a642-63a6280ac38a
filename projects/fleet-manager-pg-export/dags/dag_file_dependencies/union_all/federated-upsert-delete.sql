-- *** START OF DELETE SCRIPT ***
-- perform upsert operation, only if needed (checking max IDs from current dataset vs stage/delta records)
INSERT INTO debug_logs
SELECT CURRENT_TIMESTAMP(),
        FORMAT("Retry execution identified, upsert data using delete from %s. count_stage: %s min_stage: %s max_current: %s",
              '{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}',
              CAST(count_stage AS STRING),
              CAST(min_stage AS STRING),
              CAST(max_current AS STRING));

IF '{LOAD_TABLE_TYPE}' = 'history' THEN
  DELETE FROM
    `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}`
  WHERE
    {DELETE_HISTORY_CONDITIONS}
  -- Gets expanded as 
  --(
  --  fleet_customer_id in (FLEET_CUSTOMER_ID_1)
  --  AND TABLE_KEY >= MIN_STAGE_1
  --) OR (
  --  fleet_customer_id in (FLEET_CUSTOMER_ID_2)
  --  AND TABLE_KEY >= MIN_STAGE_2
  --)
  ;
ELSE
  -- replacement for delete with join
  MERGE
    `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}` T
  USING (
    SELECT
      *
    FROM (
      {DELETE_MODIFIED_UNION_ALL}
      -- Gets expanded as
      --CLIENT 1
      -- SELECT fleet_customer_id, TABLE_KEY FROM `PROJECT_ID.DATASET_STAGE_ID.TABLE_NAME_VERSION_ID`
      -- UNION ALL
      --CLIENT 2
      -- SELECT fleet_customer_id, TABLE_KEY FROM `PROJECT_ID.DATASET_STAGE_ID.TABLE_NAME_VERSION_ID`
    )) S
    ON
      T.fleet_customer_id IN ({FLEET_CUSTOMER_IDS})
      AND T.fleet_customer_id = S.fleet_customer_id
      AND T.{TABLE_KEY} = S.{TABLE_KEY}
    WHEN MATCHED THEN
      DELETE
  ;
END IF;
-- *** END OF DELETE SCRIPT ***
