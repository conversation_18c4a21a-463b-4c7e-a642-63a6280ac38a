"""
The goal of this script is to contain the actual implementation of listing the tables which are going to be read from Postgres
into BigQuery.
"""

import ast
import json
import logging as log
import os
import sqlalchemy

import psycopg2
import psycopg2.extras
from google.cloud import bigquery, storage
from google.cloud.exceptions import NotFound
from urllib.parse import urlparse
from datetime import datetime
from fleet_manager_pg_export import settings
from fleet_manager_pg_export.qa_check import run_bq_queries
import pandas as pd

# logging
log.basicConfig(format=settings.formatting)
log.getLogger().setLevel(log.INFO)


def write_xcom(**kwargs):
    with open("/airflow/xcom/return.json", "w") as fh:
        json.dump(kwargs, fh)
    log.info(json.dumps(kwargs))


def write_ssl_files(ssl_dict):
    """
    Create ssl files and check permissions
    @param ssl_dict: dictionary, path with data to create
    @return: None
    """

    for path, value in ssl_dict.items():
        f = open(path, "w")
        f.write(value)
        f.close()
        os.chmod(path, 0o600)


def get_sql_connection(db_config):
    ssl_config = ""
    if db_config["use_cert"]:
        temp_folder = "/tmp"
        ssl_root_cert_file_name = f"{temp_folder}/server-ca.pem"
        ssl_cert_file_name = f"{temp_folder}/client-cert.pem"
        ssl_key_file_name = f"{temp_folder}/client-key.pem"

        write_ssl_files(
            {
                ssl_root_cert_file_name: db_config["ssl_root_cert"],
                ssl_cert_file_name: db_config["ssl_cert"],
                ssl_key_file_name: db_config["ssl_key"],
            }
        )
        ssl_config = (
            f"?sslmode=verify-ca&sslrootcert={ssl_root_cert_file_name}"
            f"&sslcert={ssl_cert_file_name}"
            f"&sslkey={ssl_key_file_name}"
        )

    conn_string = (
        f"postgresql+psycopg2://{db_config['username']}:"
        f"{db_config['password']}@{db_config['hostname']}"
        f"/{db_config['database']}"
    ) + ssl_config
    schema = db_config.get("schema", None)
    create_engine_parameters = {"isolation_level": "AUTOCOMMIT"}
    if schema:
        create_engine_parameters["connect_args"] = {
            "options": f"-csearch_path={schema}"
        }
    engine = sqlalchemy.create_engine(conn_string, **create_engine_parameters)

    conn = engine.connect()
    return engine, conn


def setup_pg_engine(db_config):
    ssl_config = ""
    if db_config["use_cert"]:
        temp_folder = "/tmp"
        ssl_root_cert_file_name = f"{temp_folder}/server-ca.pem"
        ssl_cert_file_name = f"{temp_folder}/client-cert.pem"
        ssl_key_file_name = f"{temp_folder}/client-key.pem"

        write_ssl_files(
            {
                ssl_root_cert_file_name: db_config["ssl_root_cert"],
                ssl_cert_file_name: db_config["ssl_cert"],
                ssl_key_file_name: db_config["ssl_key"],
            }
        )

        ssl_config = (
            f"?sslmode=verify-ca&sslrootcert={ssl_root_cert_file_name}"
            f"&sslcert={ssl_cert_file_name}"
            f"&sslkey={ssl_key_file_name}"
        )

    conn_string = (
        f"postgresql+psycopg2://{db_config['username']}:"
        f"{db_config['password']}@{db_config['hostname']}"
        f"/{db_config['database']}"
    ) + ssl_config

    schema = db_config.get("schema", None)
    create_engine_parameters = {"isolation_level": "AUTOCOMMIT"}
    if schema:
        create_engine_parameters["connect_args"] = {
            "options": f"-csearch_path={schema}"
        }

    engine = sqlalchemy.create_engine(conn_string, **create_engine_parameters)

    conn = engine.raw_connection()
    return engine, conn


def extract_schema_tables(
    pg_user: str,
    pg_password: str,
    pg_host: str,
    pg_db_name: str,
    pg_use_cert: str,
    table_columns_config: str,
    table_name_exclusions: list,
    gcs_path: str,
    file_name: str,
    schema_filter: str,
    ssl_root_cert: str,
    ssl_cert: str,
    ssl_key: str,
):
    db_config = {
        "username": pg_user,
        "password": pg_password,
        "hostname": pg_host,
        "database": pg_db_name,
        "use_cert": pg_use_cert,
        "ssl_root_cert": ssl_root_cert,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
    }

    engine, connection = setup_pg_engine(db_config)

    table_columns_config = ast.literal_eval(table_columns_config)

    log.info(f"Tables config:: {table_columns_config}")
    log.info(f"Excluding this tables: {', '.join(table_name_exclusions)}")

    # get the list of tables we want (Ordinary and Partitioned tables)
    table_extraction_queries = {}
    schema_filter = json.loads(schema_filter)
    for schema, object_config in schema_filter.items():
        kind = object_config.get("kind", "table")
        filters = object_config.get("filters", [])
        filters = [f"{f} = {{{f.upper()}}}" for f in filters]

        if kind == "table":
            relkind = "('p', 'r')"
        else:
            relkind = "('v')"
        extra_filter = " AND ".join(
            [f"table_name NOT LIKE '{f}'" for f in table_name_exclusions]
        )
        # DOC FOR PG_CLASS: https://www.postgresql.org/docs/12/catalog-pg-class.html
        sql_query = f"""
            with table_pks AS (
                SELECT
                  c.oid as c_oid,
                  n.oid as n_oid,
                  a2.attname
                FROM
                    pg_catalog.pg_class c
                JOIN
                    pg_catalog.pg_namespace n
                ON
                    n.oid = c.relnamespace
                JOIN
                    pg_catalog.pg_index i2
                ON
                    i2.indrelid = c.oid
                AND i2.indisprimary
                JOIN
                    pg_catalog.pg_attribute a2
                ON
                    a2.attrelid = c.oid
                AND a2.attnum = any(i2.indkey)
                AND a2.attname not in ('fleet_customer_id', 'partition_id')
            )
            , ordinary_and_partitioned_tables AS (
                SELECT
                    n.nspname AS table_schema,
                    c.relname AS table_name, 
                    CASE c.relkind
                        WHEN 'p' THEN 'partitioned table'
                        WHEN 'r' THEN 'ordinary table'
                        ELSE 'unknown table type'
                    END AS table_type,
                    case when c.relname = 'fleet_customers' then 'fleet_customer_id' else pk.attname end as table_pk
                FROM
                    pg_catalog.pg_class c
                JOIN
                    pg_catalog.pg_namespace n 
                ON 
                    n.oid = c.relnamespace
                LEFT JOIN
                    table_pks pk
                ON
                    pk.c_oid = c.oid
                AND pk.n_oid = n.oid 
                WHERE  
                    c.relkind in {relkind}       -- table types, 'r': ordinary, 'p': partitioned
                AND NOT c.relispartition          -- exclude child partitions
            ) 
            SELECT 
                * 
            FROM 
                ordinary_and_partitioned_tables
            WHERE
                table_schema = '{schema}' -- filter only schema
        """ + (
            f" AND {extra_filter}" if extra_filter else ""
        )

        cursor: psycopg2.extras.DictCursor = connection.cursor(
            cursor_factory=psycopg2.extras.RealDictCursor
        )

        # Display the PostgreSQL version installed
        cursor.execute("SELECT version();")
        record = cursor.fetchone()
        log.info(f"You are connected into the - {record}")

        # get list of tables
        log.info(f"Running query on PG: {sql_query}")
        cursor.execute(sql_query)
        result = cursor.fetchall()
        log.info(f"Result query on PG: {result}")
        table_dict = {temp["table_name"]: {"pk": temp["table_pk"]} for temp in result}
        table_names = list(table_dict.keys())

        table_names.sort()

        schema_tables = {}
        for table in table_names:
            table_config = table_columns_config.get(table, {})
            version_columns = "CAST({VERSION_ID} AS BIGINT) _version"
            query = f"SELECT *, {version_columns} FROM {schema}.{table}"

            # we only need a single sample in order to get table metadata based on the query
            cursor.execute(f"SELECT * FROM {schema}.{table} WHERE FALSE")
            columns = [(t.name, t.type_code) for t in cursor.description]

            ignored_columns_config = table_config.get("ignored_columns", [])
            log.info(ignored_columns_config)
            if ignored_columns_config:
                columns = [c for c in columns if c[0] not in ignored_columns_config]
                column_names = [
                    c[0] for c in columns if c[0] not in ignored_columns_config
                ]
                query = f"SELECT {', '.join(column_names)}, {version_columns} FROM {schema}.{table}"

            avro_schema_override_field_types = set(
                table_config.get("avro_schema_override_field_types", {}).keys()
            )

            json_types = {"JSON", "JSONB"}
            json_to_text_config = [
                col_name
                for col_name, col_type in columns
                if str(
                    psycopg2.extensions.string_types.get(col_type, " 'UNKNOWN' ")
                ).split("'")[1]
                in json_types
                and col_name not in avro_schema_override_field_types
            ]

            if json_to_text_config:

                columns_copy = [col_name for col_name, _ in columns]
                for col in json_to_text_config:
                    # Jinja2 escaping
                    columns_copy[columns_copy.index(col)] = (
                        col + " #>> {{ \"'{{}}'\" }} AS " + col
                    )

                query = f"SELECT {', '.join(columns_copy)}, {version_columns} FROM {schema}.{table}"

            if filters:
                query += " WHERE " + " AND ".join(filters)

            incremental_config = table_config.get("incremental")
            if incremental_config:
                if incremental_config.get("key"):
                    key = incremental_config["key"]
                    query += f" AND {key} > {{MAX_KEY_ID}}"
                elif incremental_config.get("datetime_range"):
                    cols_filter = [
                        (
                            f"{col} BETWEEN TO_TIMESTAMP('{{MIN_UPDATED_AT}}', 'YYYY-MM-DD HH24:MI:SS') "
                            "AND TO_TIMESTAMP('{MAX_UPDATED_AT}', 'YYYY-MM-DD HH24:MI:SS')"
                        )
                        for col in incremental_config["datetime_range"]
                    ]
                    cols_filter = " OR ".join(cols_filter)
                    query += f" AND ({cols_filter})"

            schema_tables[table] = {
                "query": query,
                "pk": table_dict[table].get("pk"),
            }
        table_extraction_queries[f"{schema}_tables"] = schema_tables

    write_gcloud(
        content=table_extraction_queries, file_name=file_name, gcs_path=gcs_path
    )


def write_gcloud(content: dict, file_name: str, gcs_path: str):
    """
    Upload local file to gcloud storage
    """
    if not content:
        raise ValueError(f"check the result from the endpoint: {content}")
    full_content = (
        f"{file_name} = {content}"
    )

    url = urlparse(gcs_path, allow_fragments=False)
    client = storage.Client()
    bucket = client.bucket(url.netloc)
    blob = bucket.blob(url.path.lstrip("/"))
    blob.upload_from_string(full_content, content_type="application/json")
    log.info(f"Uploading to gcs_path: {gcs_path}")


def get_table_schema(client, dataset_id, table_id):

    dataset_ref = client.dataset(dataset_id)
    table_ref = dataset_ref.table(table_id)
    table = client.get_table(table_ref)  # API Request

    # View table properties
    schema_dict = {}
    for schema in table.schema:
        schema_dict[schema.name] = schema.field_type

    return schema_dict


def execute_bq_migration(
    project_id_union: str,
    project_id: str,
    dataset_id: str,
    table_id: str,
    dataset_current: str,
    table_name: str,
):
    client = bigquery.Client(project=project_id)

    bq_query_view_definition = f"""
        SELECT * 
        FROM `{project_id}.{dataset_current}`.INFORMATION_SCHEMA.TABLES 
        WHERE table_name = '{table_name}'
          and table_type != 'VIEW'
    """

    current_full_table_name = f"{project_id}.{dataset_current}.{table_name}"
    df_bq = run_bq_queries(client, [bq_query_view_definition])[0]
    if df_bq.empty:
        log.info(
            f"Table not exists: {current_full_table_name} ... Migrations are no needed"
        )
        return

    bq_test_query = "SELECT 1 FROM `{}` WHERE FALSE"
    # Ensure both tables exits
    try:
        run_bq_queries(
            client,
            [bq_test_query.format(current_full_table_name)],
        )
        run_bq_queries(
            client, [bq_test_query.format(f"{project_id}.{dataset_id}.{table_id}")]
        )
    except NotFound:
        log.info("Table not exists... Migrations are no needed")
        return

    table_current_columns = get_table_schema(client, dataset_current, table_name).keys()
    table_id_columns = get_table_schema(client, dataset_id, table_id).keys()

    if table_name in ("fleet_valuations", "sold_valuations"):
        drop_column_query = f"""
            IF (SELECT data_type
            FROM `{project_id}.{dataset_current}.INFORMATION_SCHEMA.COLUMNS`
            WHERE table_name = '{table_name}'
            AND column_name = 'baseline_residual_flv') = 'NUMERIC'
            THEN 
            ALTER TABLE `{project_id}.{dataset_current}.{table_name}`
            DROP COLUMN  baseline_residual_flv;
            END IF;

            IF (SELECT data_type
            FROM `{project_id}.{dataset_current}.INFORMATION_SCHEMA.COLUMNS`
            WHERE table_name = '{table_name}'
            AND column_name = 'baseline_residual_fmv') = 'NUMERIC'
            THEN 
            ALTER TABLE `{project_id}.{dataset_current}.{table_name}`
            DROP COLUMN  baseline_residual_fmv;
            END IF;

            IF (SELECT data_type
            FROM `{project_id}.{dataset_current}.INFORMATION_SCHEMA.COLUMNS`
            WHERE table_name = '{table_name}'
            AND column_name = 'baseline_residual_auction') = 'NUMERIC'
            THEN 
            ALTER TABLE `{project_id}.{dataset_current}.{table_name}`
            DROP COLUMN  baseline_residual_auction;
            END IF;

            IF (SELECT data_type
            FROM `{project_id}.{dataset_current}.INFORMATION_SCHEMA.COLUMNS`
            WHERE table_name = '{table_name}'
            AND column_name = 'baseline_residual_retail') = 'NUMERIC'
            THEN 
            ALTER TABLE `{project_id}.{dataset_current}.{table_name}`
            DROP COLUMN  baseline_residual_retail;
            END IF;
        """

        log.info(f"Dropping baselines columns with the wrong type in {table_name}")
        run_bq_queries(client, [drop_column_query])

    query_columns_changes = f"""
    WITH 
    CURRENT_COLS AS (
        SELECT column_name, data_type 
        FROM `{project_id}.{dataset_current}`.INFORMATION_SCHEMA.COLUMNS 
        WHERE table_name = '{table_name}'
        AND column_name in ({",".join([f"'{c}'" for c in table_current_columns])}) -- due time travel BigQuery, not considering dropped columns
    )
    , VERSION_COLS AS (
        SELECT column_name, data_type
        FROM `{project_id}.{dataset_id}`.INFORMATION_SCHEMA.COLUMNS 
        WHERE table_name = '{table_id}'
        AND column_name in ({",".join([f"'{c}'" for c in table_id_columns])}) -- due time travel BigQuery, not considering dropped columns 
    )
    , CHANGES AS (
        SELECT 
            CASE
                WHEN B.column_name IS NULL THEN 'ADDED'
                WHEN A.column_name IS NULL THEN 'REMOVED'
                WHEN NOT A.data_type = B.data_type THEN 'CHANGED'
                ELSE NULL
            END AS status
            , COALESCE(A.column_name, B.column_name) AS column_name
            , COALESCE(A.data_type, B.data_type) AS data_type
        FROM VERSION_COLS A 
        FULL OUTER JOIN CURRENT_COLS B 
        ON A.column_name = B.column_name
    )
    SELECT * FROM CHANGES WHERE status IS NOT NULL
    """

    df_bq = run_bq_queries(client, [query_columns_changes])[0]

    tables_to_migrate = [current_full_table_name]

    if project_id_union:
        current_union_full_table_name = (
            f"{project_id_union}.{dataset_current}.{table_name}"
        )

        # Only migrate union table if exists
        try:
            run_bq_queries(
                client, [bq_test_query.format(current_union_full_table_name)]
            )
            # Improvement, could compare if union table and source table contain the same columns
            # could be included in union all dag, but timing could affect data load
            tables_to_migrate.append(current_union_full_table_name)
        except NotFound:
            log.info(
                f"Union table not exists ('{current_union_full_table_name}')... Migration is not needed to this table"
            )

    migration_commands = []

    changes_to_migrate = df_bq.to_dict(orient="records")
    for table_name in tables_to_migrate:
        changes_to_apply = []
        for change in changes_to_migrate:
            if change["status"] == "ADDED":
                changes_to_apply.append(
                    f"ADD COLUMN IF NOT EXISTS {change['column_name']} {change['data_type']}"
                )
            elif change["status"] == "REMOVED":
                changes_to_apply.append(
                    f"DROP COLUMN IF EXISTS {change['column_name']}"
                )
            elif change["status"] == "CHANGED":
                changes_to_apply.append(
                    f"ALTER COLUMN IF EXISTS {change['column_name']} SET DATA TYPE {change['data_type']}"
                )
            else:
                raise Exception(f"Status: {change['status']} is not supported yet !")

        if changes_to_apply:
            migration_commands.append(
                f"ALTER TABLE `{table_name}` \n" + "\n,".join(changes_to_apply) + ";"
            )

    # Execute all migrations
    if migration_commands:
        log.info(f"Start running migrations to {table_name}")
        run_bq_queries(client, migration_commands)
    else:
        log.info("Tables schema remain the same, migrations are no needed")


def query_max_bigquery(
    project_id: str, dataset_id: str, fleet_customer_id, max_bq_key_config
):
    max_bq_key_config = ast.literal_eval(max_bq_key_config)

    log.info(f"Max key config:: {max_bq_key_config}")

    client = bigquery.Client(project=project_id)

    metrics = {}
    for config in max_bq_key_config:
        metrics.update(
            _query_max_bq(client, dataset_id, project_id, fleet_customer_id, **config)
        )

    write_xcom(metrics=metrics)


def _query_max_bq(
    client, dataset_id, project_id, fleet_customer_id, max_key_column, table_id
):
    table_exists = True
    count = 0
    max_key = 0
    try:
        coalesce_query = (
            f", COALESCE(MAX({max_key_column}), 0) as max_key" if max_key_column else ""
        )
        bq_query = f"SELECT COUNT(1) as count {coalesce_query} FROM `{project_id}.{dataset_id}.{table_id}`"

        if fleet_customer_id:
            bq_query += f" WHERE fleet_customer_id in ({fleet_customer_id})"

        df_bq = run_bq_queries(client, [bq_query])[0]

        count = df_bq.iloc[0]["count"]
        if max_key_column:
            max_key = df_bq.iloc[0]["max_key"]

    except NotFound:
        log.info(f"Table not exists: `{project_id}.{dataset_id}.{table_id}`")
        table_exists = False

    return {
        table_id: str(
            {
                "table_exists": table_exists,
                "num_rows": count,
                "max_key": max_key,
            }
        )
    }


def get_indexes(
    db_config: dict, query: str, query_number_of_batches: str, variable_dict: dict
):
    """execute query to get parameters for next tasks"""
    try:
        engine, conn = get_sql_connection(db_config)

        query_number_of_batches = query_number_of_batches.format(**variable_dict)
        number_of_batches = pd.read_sql(
            query_number_of_batches, conn.connection
        ).to_dict(orient="records")[0]
        log.info(f"using number_of_batches: {number_of_batches}")

        number_of_batches = number_of_batches["number_of_batches"]
        variable_dict["NUMBER_OF_BATCHES"] = number_of_batches

        query = query.format(**variable_dict)
        log.info(query)
        query_result_df = pd.read_sql(query, conn.connection)
        if not query_result_df.empty:
            query_result_dict = query_result_df.to_dict(orient="records")
        else:
            query_result_dict = []
            for i in range(number_of_batches):
                query_result_dict.append(
                    {"rownumber": i, "min_key_id": 0, "max_key_id": 0, "total_count": 0}
                )
        write_xcom(**{"metrics": str(query_result_dict)})

    finally:
        conn.close()
        engine.dispose()
        log.info("Connection closed")


def get_federated_max_keys(client, variable_dict):
    query = """
    SELECT
        fleet_customer_id,
        IFNULL(MAX({TABLE_KEY}), 0) max_current
    FROM
        `{UNION_PROJECT_ID}.{UNION_DATASET_ID}.{TABLE_NAME}`
    WHERE
        fleet_customer_id IN ({FLEET_CUSTOMER_IDS})
    GROUP BY
        fleet_customer_id
    """
    df = run_bq_queries(client, [query.format(**variable_dict)])[0]
    return dict(df.values)


def format_upsert_query(
    bq_client,
    task_run_list,
    variable_dict,
    federated_declare_vars,
    federated_create_query,
    federated_delete_query,
    federated_load_query,
):
    delete_history_table_template = """
    (
        fleet_customer_id = {FLEET_CUSTOMER_ID}
        AND {TABLE_KEY} >= {SOURCE_TABLE_KEY_MIN_VALUE}
    )
    """
    delete_modified_cols_set = {"fleet_customer_id"}
    delete_modified_table_template = """
    SELECT {DELETE_MODIFIED_TABLE_KEYS} FROM `{PROJECT_ID}.{DATASET_STAGE_ID}.{TABLE_NAME}_{VERSION_ID}`
    """
    count_client_template = """
        SELECT
            row_count
        FROM
            `{PROJECT_ID}.{DATASET_VIEW_ID}.__TABLES__`
        WHERE
            table_id = '{TABLE_NAME}'
    """
    sum_count_client_template = """
        SELECT
            SUM(row_count) sum_row_count
        FROM (
            {COUNT_CLIENT_UNION_ALL}
        )
    """
    dev_insert_statement_template = """
        ' SELECT ',
            ARRAY_TO_STRING(column_names, ','),
        ' FROM',
        '     `{PROJECT_ID}.{DATASET_VIEW_ID}.{TABLE_NAME}`',
        ' WHERE',
        '     fleet_customer_id IN ({FLEET_CUSTOMER_ID})'
    """
    # Two types of tables history and modified
    delete_history_condition_list = []
    delete_modified_condition_list = []
    client_count_list = set()
    source_uris_list = []
    fleet_customer_id_list = set()
    dev_insert_statement_list = set()
    max_dag_run_id = -1
    last_project_id_processed = None
    for _, client_config in task_run_list.items():
        version_id = datetime.fromisoformat(client_config["dag_execution_date"]).strftime("%Y%m%d%H%M%S")
        variable_dict["PROJECT_ID"] = client_config["gcp_project_id"]
        variable_dict["VERSION_ID"] = version_id
        variable_dict["FLEET_CUSTOMER_ID"] = client_config["fleet_customer_id"]
        variable_dict["SOURCE_TABLE_KEY_MIN_VALUE"] = client_config["xcom"][
            "source_table_key_min_value"
        ]
        variable_dict["SOURCE_TABLE_COUNT_VALUE"] = client_config["xcom"][
            "source_table_count_value"
        ]
        if client_config["dag_run_id"] > max_dag_run_id:
            last_project_id_processed = client_config["gcp_project_id"]
            max_dag_run_id = client_config["dag_run_id"]

        fleet_customer_id_list.add(str(client_config["fleet_customer_id"]))
        for single_uri in client_config["xcom"]["avro_source_uris"]:
            source_uris_list.append(f"'{single_uri}'")
        client_count_list.add(count_client_template.format(**variable_dict))
        dev_insert_statement_list.add(
            dev_insert_statement_template.format(**variable_dict)
        )
    multiple_source_uris = ",".join(source_uris_list)
    fleet_customer_ids = ",".join(fleet_customer_id_list)
    client_counts = " UNION ALL ".join(client_count_list)
    dev_insert_union_all = "' UNION ALL '".join(dev_insert_statement_list)
    variable_dict["FLEET_CUSTOMER_IDS"] = fleet_customer_ids
    variable_dict["MULTIPLE_SOURCE_URIS"] = multiple_source_uris
    variable_dict["ALL_CLIENT_COUNT_SUM"] = sum_count_client_template.format(
        COUNT_CLIENT_UNION_ALL=client_counts
    )
    variable_dict["DEV_INSERT_UNION_ALL"] = dev_insert_union_all

    bq_max_keys = get_federated_max_keys(bq_client, variable_dict)
    # Special handle for tables with key=fleet_customer_id
    delete_modified_cols_set.add(variable_dict['TABLE_KEY'])
    delete_modified_cols = ",".join(delete_modified_cols_set)
    variable_dict["DELETE_MODIFIED_TABLE_KEYS"] = delete_modified_cols

    for _, client_config in task_run_list.items():
        version_id = datetime.fromisoformat(client_config["dag_execution_date"]).strftime("%Y%m%d%H%M%S")
        variable_dict["PROJECT_ID"] = client_config["gcp_project_id"]
        variable_dict["VERSION_ID"] = version_id
        variable_dict["FLEET_CUSTOMER_ID"] = client_config["fleet_customer_id"]
        variable_dict["SOURCE_TABLE_KEY_MIN_VALUE"] = client_config["xcom"][
            "source_table_key_min_value"
        ]
        variable_dict["SOURCE_TABLE_COUNT_VALUE"] = client_config["xcom"][
            "source_table_count_value"
        ]
        if (
            client_config["xcom"]["source_table_count_value"] > 0
            and bq_max_keys.get(client_config["fleet_customer_id"], 0)
            >= client_config["xcom"]["source_table_key_min_value"]
        ):
            delete_history_condition_list.append(
                delete_history_table_template.format(**variable_dict)
            )
            delete_modified_condition_list.append(
                delete_modified_table_template.format(**variable_dict)
            )
    # format federated_delete_query
    # Do not perform deletes if there are no clients needing it (default)
    federated_delete_query_fmt = ""
    if (
        len(delete_history_condition_list) > 0
        and len(delete_modified_condition_list) > 0
    ):
        delete_history_conditions = " OR ".join(delete_history_condition_list)
        delete_modified_union_all = " UNION ALL ".join(delete_modified_condition_list)
        variable_dict["DELETE_HISTORY_CONDITIONS"] = delete_history_conditions
        variable_dict["DELETE_MODIFIED_UNION_ALL"] = delete_modified_union_all
        federated_delete_query_fmt = federated_delete_query.format(**variable_dict)

    federated_load_query_fmt = federated_load_query.format(**variable_dict)
    # use last client to create table if needed
    variable_dict["PROJECT_ID"] = last_project_id_processed
    federated_create_query_fmt = federated_create_query.format(**variable_dict)
    return "\n".join(
        [
            federated_declare_vars,
            federated_create_query_fmt,
            federated_delete_query_fmt,
            federated_load_query_fmt,
        ]
    )


def upsert_table_union(
    project_id,
    print_query_results,
    task_run_list,
    variable_dict,
    federated_declare_vars,
    federated_create_query,
    federated_delete_query,
    federated_load_query,
):
    bq_client = bigquery.Client(project=project_id)
    # Need to run this in image as we need to refresh the max keys from the federated tables in case of a retry
    variable_dict = ast.literal_eval(variable_dict)
    task_run_list = ast.literal_eval(task_run_list)
    # enrich_variable_dict(bq_client, task_run_list, variable_dict)
    upsert_query = format_upsert_query(
        bq_client,
        task_run_list,
        variable_dict,
        federated_declare_vars,
        federated_create_query,
        federated_delete_query,
        federated_load_query,
    )
    log.info(upsert_query)
    query_job = bq_client.query(upsert_query)
    if print_query_results:
        if results := query_job.result():
            log.info("Query results:")
            for row in results:
                log.info(row)
