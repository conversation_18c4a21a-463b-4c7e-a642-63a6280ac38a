import ast
import click

from fleet_manager_pg_export import el, qa_check, settings


@click.group()
@click.version_option(version="1.0.0")
def cli():
    """
    Sends data to google cloud storage and big query so that it resides in
    cloud as a versioned table
    """
    return


@cli.command()
@click.option(
    "--pg_user",
    required=True,
    type=str,
    help="Postgres user with read access",
    envvar="PG_USERNAME",
)
@click.option(
    "--pg_password",
    required=True,
    type=str,
    help="Postgres password",
    envvar="PG_PASSWORD",
)
@click.option("--pg_host", type=str, envvar="DB_SERVER", help="Postgres hostname")
@click.option(
    "--pg_db_name", required=True, help="database name in Postgres", envvar="DB_NAME"
)
@click.option(
    "--pg_use_cert",
    type=bool,
    envvar="USE_PG_CERT",
    required=True,
    help="Flag to use or not pg ssl",
)
@click.option(
    "--table_columns_config",
    envvar="TABLE_COLUMNS_CONFIG",
    default="{}",
    help="Table columns to be excluded, e.g: 'table_name' or '%_name'",
)
@click.option(
    "--table_name_exclusions",
    multiple=True,
    envvar="TABLE_NAME_EXCLUSIONS",
    default=[],
    help="Table names to be excluded, e.g: 'table_name' or '%_name'",
)
@click.option(
    "--gcs_path",
    envvar="GCS_PATH",
    default="",
    help="GCS PATH to save the tables to be extracted",
)
@click.option(
    "--file_name",
    envvar="FILE_NAME",
    default="",
    help="file name to save the tables to be extracted",
)
@click.option(
    "--schema_filter",
    required=False,
    envvar="SCHEMA_FILTER",
    type=str,
    default='{"fleet_manager": {"kind": "table", "filters": ["partition_id", "fleet_customer_id"]} }',
    help="List of schemas to filter",
)
@click.option(
    "--ssl_root_cert",
    type=str,
    envvar="SSL_ROOT_CERT",
    help="contains SSL certificate authority (CA) certificate(s)",
)
@click.option(
    "--ssl_cert",
    type=str,
    envvar="SSL_CERT",
    help="the client SSL certificate",
)
@click.option(
    "--ssl_key",
    type=str,
    envvar="SSL_KEY",
    help="the secret key used for the client certificate",
)
def get_fleet_manager_tables(**kwargs):
    el.extract_schema_tables(**kwargs)


@cli.command()
@click.option(
    "--mssql_domain",
    type=str,
    envvar="WIN_USER_DOMAIN",
    help="Windows domain. Must be able to resolve into an IP",
)
@click.option(
    "--mssql_username",
    type=str,
    envvar="WIN_USER",
    help="Windows username",
    required=True,
)
@click.option(
    "--mssql_password",
    type=str,
    envvar="WIN_PASSWORD",
    help="Windows password",
    required=True,
)
@click.option(
    "--mssql_db", type=str, envvar="SQL_DB", help="SQL Server hostname", required=True
)
@click.option(
    "--mssql_host",
    type=str,
    envvar="SQL_HOST",
    help="SQL Server hostname",
    required=True,
)
@click.option(
    "--gcp_project_id",
    type=str,
    required=True,
    envvar="PROJECT_ID",
    help="Google project ID",
)
@click.option(
    "--bq_query",
    type=str,
    required=True,
    envvar="BQ_QUERY",
    help="BigQuery query to run QA Checks",
)
@click.option(
    "--source_query",
    type=str,
    required=True,
    envvar="SOURCE_QUERY",
    help="SQL Server query to run QA Checks",
)
@click.option(
    "--source_engine",
    type=str,
    default="MSSQL",
    envvar="SOURCE_ENGINE",
    help="Were to run Source Query, MSSQL or BQ",
)
def qa_report(**kwargs):
    qa_check.qa_report(**kwargs)


@cli.command()
@click.option("--project_id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--dataset_id",
    type=str,
    envvar="DATASET_ID",
    help="BigQuery dataset Version ID",
)
@click.option(
    "--dataset_view_id",
    type=str,
    envvar="DATASET_VIEW_ID",
    help="BigQuery dataset View ID",
)
@click.option(
    "--dataset_stage_id",
    type=str,
    envvar="DATASET_STAGE_ID",
    help="BigQuery dataset Stage ID",
)
@click.option(
    "--table_name", type=str, envvar="TABLE_NAME", help="Table name to process"
)
@click.option(
    "--qa_columns",
    default=None,
    help="Columns and operations that should be checked",
    envvar="QA_COLUMNS",
)
@click.option(
    "--incremental_config",
    default=None,
    help="Date range config used to load table, if any",
    envvar="INCREMENTAL_CONFIG",
)
@click.option(
    "--metrics",
    default=None,
    help="Metrics from source",
    envvar="METRICS",
)
@click.option(
    "--metrics_extra",
    default=None,
    help="Extra Metrics from source",
    envvar="METRICS_EXTRA",
)
@click.option(
    "--fleet_customer_id",
    type=str,
    envvar="FLEET_CUSTOMER_ID",
    help="Fleet customer id",
)
@click.option(
    "--table_key",
    type=str,
    envvar="TABLE_KEY",
    help="Table Key",
)
@click.option(
    "--version_id", type=str, envvar="VERSION_ID", required=True, help="Version ID"
)
def qa_bigquery(
    project_id,
    dataset_id,
    dataset_view_id,
    dataset_stage_id,
    table_name,
    qa_columns,
    incremental_config,
    metrics,
    metrics_extra,
    fleet_customer_id,
    table_key,
    version_id,
):
    """Run data quality checks on comparing from Postgres and
    output from BigQuery
    """
    qa_check.qa_bigquery(
        project_id,
        dataset_id,
        dataset_view_id,
        dataset_stage_id,
        table_name,
        qa_columns,
        incremental_config,
        metrics,
        metrics_extra,
        fleet_customer_id,
        table_key,
        version_id,
    )


@cli.command()
@click.option("--project_id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option("--dataset_id", type=str, envvar="DATASET_ID", help="BigQuery dataset ID")
@click.option(
    "--fleet_customer_id",
    type=str,
    default=None,
    envvar="FLEET_CUSTOMER_ID",
    help="Fleet customer id",
)
@click.option(
    "--max_bq_key_config",
    type=str,
    default=None,
    envvar="MAX_BQ_KEY_CONFIG",
    help="Column configs to get max in Big Query if provided as well count for the table",
)
def query_max_bigquery(project_id, dataset_id, fleet_customer_id, max_bq_key_config):
    """Run data quality checks on comparing from Postgres and
    output from BigQuery
    """
    el.query_max_bigquery(project_id, dataset_id, fleet_customer_id, max_bq_key_config)


@cli.command()
@click.option(
    "--project_id_union",
    type=str,
    default=None,
    envvar="PROJECT_ID_UNION",
    help="Google project ID for Unified tables",
)
@click.option(
    "--project_id",
    type=str,
    envvar="PROJECT_ID",
    help="Google project ID",
    required=True,
)
@click.option(
    "--dataset_id",
    type=str,
    envvar="DATASET_ID",
    help="BigQuery dataset ID",
    required=True,
)
@click.option(
    "--table_id", type=str, envvar="TABLE_ID", help="Table in Big Query", required=True
)
@click.option(
    "--dataset_current",
    type=str,
    envvar="DATASET_CURRENT",
    help="BigQuery dataset Current",
    required=True,
)
@click.option(
    "--table_name",
    type=str,
    envvar="TABLE_NAME",
    help="Table Name in Big Query",
    required=True,
)
def execute_bq_migration(
    project_id_union, project_id, dataset_id, table_id, dataset_current, table_name
):
    """
    Generate Schema Migration Script for BigQuery
    based on new version being created and current table status
    """
    el.execute_bq_migration(
        project_id_union,
        project_id,
        dataset_id,
        table_id,
        dataset_current,
        table_name,
    )


# get indexes
@cli.command()
@click.option(
    "--username",
    type=str,
    envvar="PG_USERNAME",
    help="Postgres username",
    required=True,
)
@click.option(
    "--password",
    type=str,
    envvar="PG_PASSWORD",
    help="Postgres password",
    required=True,
)
@click.option(
    "--table-name",
    type=str,
    envvar="TABLE_NAME",
    help="Table name to process",
)
@click.option(
    "--partition_id",
    type=str,
    envvar="PARTITION_ID",
    help="Partition ID",
)
@click.option("--pg-host", type=str, envvar="DB_SERVER", help="Postgres hostname")
@click.option(
    "--use-cert",
    type=bool,
    envvar="USE_PG_CERT",
    required=True,
    help="Flag to use or not pg ssl",
)
@click.option(
    "--ssl-root-cert",
    type=str,
    envvar="SSL_ROOT_CERT",
    help="contains SSL certificate authority (CA) certificate(s)",
)
@click.option(
    "--ssl-cert",
    type=str,
    envvar="SSL_CERT",
    help="the client SSL certificate",
)
@click.option(
    "--ssl-key",
    type=str,
    envvar="SSL_KEY",
    help="the secret key used for the client certificate",
)
@click.option(
    "--db-name",
    type=str,
    envvar="DB_NAME",
    required=True,
    help="Database to connect to",
)
@click.option(
    "--metrics",
    type=str,
    envvar="METRICS",
    required=True,
    help="Metrics",
)
@click.option(
    "--number-of-batches",
    type=int,
    envvar="NUMBER_OF_BATCHES",
    required=True,
    help="Number of batches to create",
)
@click.option(
    "--batch-size",
    type=int,
    envvar="BATCH_SIZE",
    required=True,
    help="Number of records will be read for batch iteration",
)
@click.option(
    "--table-key",
    type=str,
    envvar="TABLE_KEY",
    required=True,
    help="Table key",
)
def get_indexes(
    username: str,
    password: str,
    table_name: str,
    partition_id: str,
    pg_host: str,
    use_cert: str,
    ssl_root_cert: str,
    ssl_cert: str,
    ssl_key: str,
    db_name: str,
    metrics: str,
    number_of_batches: int,
    batch_size: int,
    table_key: str,
):
    """Get indexes"""

    metrics = ast.literal_eval(metrics)

    variable_dict = {
        "TABLE_NAME": table_name,
        "PARTITION_ID": partition_id,
        "MAX_KEY_ID": metrics["max_key"],
        "BATCHE_SIZE": batch_size,
        "NUMBER_OF_BATCHES": number_of_batches,
        "TABLE_KEY": table_key,
    }

    table = settings.table_dict["large_tables"]
    pg_query = table.pg_query
    pg_query_number_of_batches = table.pg_query_number_of_batches

    db_config = {
        "username": username,
        "password": password,
        "hostname": pg_host,
        "database": db_name,
        "use_cert": use_cert,
        "ssl_root_cert": ssl_root_cert,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
    }

    el.get_indexes(
        db_config=db_config,
        query=pg_query,
        query_number_of_batches=pg_query_number_of_batches,
        variable_dict=variable_dict,
    )


@cli.command()
@click.option("--project_id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--print_query_results",
    type=bool,
    default=False,
    envvar="PRINT_QUERY_RESULTS",
    help="If enabled should collect and print query results",
)
@click.option(
    "--task_run_list",
    type=str,
    envvar="TASK_RUN_LIST",
    help="List of recent task runs and various XCOM values",
)
@click.option(
    "--variable_dict",
    type=str,
    default=None,
    envvar="VARIABLE_DICT",
    help="Dictionary contianing values useful for formatting",
)
@click.option(
    "--federated_declare_vars",
    type=str,
    default=None,
    envvar="FEDERATED_DECLARE_VARS",
    help="Variable declaration section from upsert query",
)
@click.option(
    "--federated_create_query",
    type=str,
    default=None,
    envvar="FEDERATED_CREATE_QUERY",
    help="Table creation section from upsert query",
)
@click.option(
    "--federated_load_query",
    type=str,
    default=None,
    envvar="FEDERATED_LOAD_QUERY",
    help="Data loading/insertion section from upsert query",
)
@click.option(
    "--federated_delete_query",
    type=str,
    default=None,
    envvar="FEDERATED_DELETE_QUERY",
    help="Data deletion section from upsert query",
)
def upsert_table_union(
    project_id,
    print_query_results,
    task_run_list,
    variable_dict,
    federated_declare_vars,
    federated_create_query,
    federated_load_query,
    federated_delete_query,
):
    """Run data quality checks on comparing from Postgres and
    output from BigQuery
    """
    el.upsert_table_union(
        project_id,
        print_query_results,
        task_run_list,
        variable_dict,
        federated_declare_vars,
        federated_create_query,
        federated_delete_query,
        federated_load_query,
    )


if __name__ == "__main__":
    cli()
