FROM python:3.9-buster

ENV DEBIAN_FRONTEND noninteractive

# Install Dependencies & gcloud CLI
RUN apt-get update && \
    apt-get install --yes --no-install-recommends \
    python3 dnsutils vim htop wget sudo strace pigz pv buffer net-tools \
    freetds-dev build-essential libboost-all-dev unixodbc-dev unixodbc \
    curl gnupg apt-transport-https ca-certificates && \
    echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list && \
    curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key --keyring /usr/share/keyrings/cloud.google.gpg add - && \
    apt-get update && \
    apt-get install --yes --no-install-recommends google-cloud-sdk && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Create a non-root user
RUN useradd -m -s /bin/bash appuser
USER appuser
WORKDIR /home/<USER>

# Copy the script
COPY --chown=appuser:appuser src/ src/

# Set the entrypoint
CMD ["python3", "valuations-algo-vm-monitor.py"]