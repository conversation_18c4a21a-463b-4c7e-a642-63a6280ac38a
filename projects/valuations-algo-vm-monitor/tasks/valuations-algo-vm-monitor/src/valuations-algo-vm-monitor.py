import logging
import json
import os
import subprocess
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)

# Environment variables
TARGET_PROJECT = os.environ.get("TARGET_PROJECT", "algo-vms-prod-bd7bdf")

# Slack message template
slack_msg_template = [
    {"type": "header", "text": {"type": "plain_text", "text": "Unused VM Reservations Alert"}}
]

def write_xcom(**kwargs):
    with open("/airflow/xcom/return.json", "w") as fh:
        json.dump(kwargs, fh)
    logging.info(kwargs)

class Reservation:
    def __init__(self, reservation_data):
        self.name = reservation_data.get("name")
        self.zone = reservation_data.get("zone").split("/")[-1] if reservation_data.get("zone") else "unknown"
        self.reserved_count = reservation_data.get("specificReservation", {}).get("count", 0)
        self.in_use_count = reservation_data.get("specificReservation", {}).get("inUseCount", 0)
        self.specific_required = reservation_data.get("specificReservationRequired", False)
        self.creation_time = reservation_data.get("creationTimestamp")

        # Calculate how long the reservation has been unused
        if self.creation_time:
            creation_dt = datetime.strptime(self.creation_time, "%Y-%m-%dT%H:%M:%S.%f%z")
            now = datetime.now().astimezone()
            self.age_hours = (now - creation_dt).total_seconds() / 3600
        else:
            self.age_hours = 0

def main():
    # Get list of unused reservations using both methods for redundancy
    unused_reservations = []

    try:
        # Method 1: Use gcloud command to list unused reservations
        logging.info(f"checking for unused reservations via gcloud command for {TARGET_PROJECT}")
        cmd = [
            "gcloud", "compute", "reservations", "list",
            f"--project={TARGET_PROJECT}",
            "--filter=specificReservation.inUseCount=0",
            "--format=json"
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        reservations_data = json.loads(result.stdout)
        logging.info(f"command : '{cmd}'")
        logging.info(f"command output: '{result.stdout}'")

        # Process each unused reservation
        for reservation_data in reservations_data:
            unused_reservations.append(Reservation(reservation_data))

        # Log the findings
        if unused_reservations:
            logging.info(f"Found {len(unused_reservations)} unused reservations")
            msg = create_message(unused_reservations)
            write_xcom(message=str(msg))
        else:
            logging.info("No unused reservations found")
            write_xcom(message="")

    except subprocess.CalledProcessError as e:
        error_msg = f"Error running gcloud command: {e.stderr}"
        logging.error(error_msg)
        write_xcom(key="error_message", value=str(e))
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logging.error(error_msg)

def create_message(unused_reservations):
    if not unused_reservations:
        return None

    # Create a summary section
    summary = f"*Found {len(unused_reservations)} unused VM reservations in project {TARGET_PROJECT}*\n\n"
    summary += "These reservations are incurring costs without being utilized.\n\n"

    # Create a table of unused reservations
    table = "```\n"
    table += f"{'NAME':<30} {'ZONE':<15} {'RESERVED':<10} {'IN_USE':<10} {'AGE (HOURS)':<15} {'SPECIFIC REQUIRED':<20}\n"
    table += f"{'-'*30} {'-'*15} {'-'*10} {'-'*10} {'-'*15} {'-'*20}\n"

    for reservation in unused_reservations:
        table += f"{reservation.name:<30} {reservation.zone:<15} {reservation.reserved_count:<10} {reservation.in_use_count:<10} {reservation.age_hours:<15.2f} {reservation.specific_required}\n"

    table += "```\n\n"

    # Add command to view and delete reservations
    commands = "*To view these reservations in detail, run:*\n"
    commands += f"```gcloud compute reservations list --project={TARGET_PROJECT} --filter=\"specificReservation.inUseCount=0\" --format=\"table(name,specificReservation.count:label=RESERVED,specificReservation.inUseCount:label=IN_USE,zone.basename(),specificReservationRequired)\"```\n\n"

    commands += "*To delete an unused reservation, run:*\n"
    commands += f"```gcloud compute reservations delete RESERVATION_NAME --project={TARGET_PROJECT} --zone=ZONE```\n"

    # Combine all sections
    summary_section = {"type": "section", "text": {"type": "mrkdwn", "text": summary}}
    table_section = {"type": "section", "text": {"type": "mrkdwn", "text": table}}
    commands_section = {"type": "section", "text": {"type": "mrkdwn", "text": commands}}
    divider = {"type": "divider"}

    message = slack_msg_template.copy()
    message.append(summary_section)
    message.append(divider)
    message.append(table_section)
    message.append(divider)
    message.append(commands_section)

    return message


if __name__ == "__main__":
    main()