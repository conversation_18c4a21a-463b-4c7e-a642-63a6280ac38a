import re
from datetime import datetime, timedelta

from airflow.models import Dag<PERSON>un, TaskInstance, XCom
from airflow.models import Variable
from typing import Optional
from airflow.utils.session import provide_session
from airflow.utils.state import DagRunState, State, TaskInstanceState
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from client_projects_constants import client_mappings


IMS_ACTIVE_CLIENTS = Variable.get("ims_active_clients", "[]", deserialize_json=True)
ALL_CLIENTS = {
    ras_code: {
        "gcp_project_id": "rs-client-{CLIENT_CODE}-{CLIENT_VERSION}".format(
            CLIENT_CODE=ras_code, CLIENT_VERSION=value["client_version"]
        ),
        "sales_client_id": value["sales_client_id"],
        "fleet_customer_id": value.get("fleet_customer_id", ""),
        "sales_pipeline_type": value.get("sales_pipeline_type", ""),
        "is_ims_active_client": ras_code in IMS_ACTIVE_CLIENTS,
    }
    for ras_code, value in client_mappings.items()
    if "sales_client_code" in value and "sales_client_id" in value
}

GCP_PROJECT_ID_MULTITENANT = {
    "local": "rfm-dev-1e0721",
    "dev": "rfm-dev-1e0721",
    "prod": "rfm-prod-5f68c8",
    "ppe": "rfm-prod-5f68c8",
}


def process_dag_runs(
    upper_window: str,
    lower_window: str,
    dag_id_pattern: str,
    session: Session,
    ti: TaskInstance,
    all_active_fleet_customer_ids: dict,
    all_active_clients: dict,
    task_id_pattern: Optional[str],
    is_per_shard_dag: Optional[bool],
    dag_run_state: Optional[str] = "SUCCESS",
    ouput_key: Optional[str] = "client_code",
):
    query = (
        session.query(DagRun)
        .filter(
            DagRun.dag_id.like(dag_id_pattern),
            DagRun.end_date > lower_window,
            DagRun.end_date <= upper_window,
        )
    )
    if dag_run_state == 'SUCCESS':
        query = query.filter(DagRun.state == DagRunState.SUCCESS)

    dag_runs = query.all()
    ti.log.info(
        f"found {len(dag_runs)} for {dag_id_pattern} for time window where dagrun.end_date > {lower_window} and <= {upper_window}"
    )

    clients = {}

    for dag_run in dag_runs:
        if is_per_shard_dag:
            fleet_customer_id = dag_run.conf["fleet_customer_id"]
            client_code = all_active_fleet_customer_ids[str(fleet_customer_id)][
                "client_code"
            ]
        else:
            regex_pattern = re.escape(dag_id_pattern).replace("%", r"(.*?)")
            client_code = re.fullmatch(regex_pattern, dag_run.dag_id).group(1)

        if task_id_pattern:
            task_instance = (
                session.query(TaskInstance)
                .filter(
                    TaskInstance.dag_id == dag_run.dag_id,
                    TaskInstance.run_id == dag_run.run_id,
                    TaskInstance.task_id.like(task_id_pattern),
                )
                .first()  # If TaskInstance doesn't exist, then add the dag_run (this to avoid dependency in the future)
            )

            if (
                task_instance and task_instance.state != State.SUCCESS
            ):  # The TaskInstance exists, but it is not successful (probably skipped or failed), so do not track the dag_run
                ti.log.warning(
                    f"client {client_code} excluded because {task_instance.task_id} is in state {task_instance.state}"
                )
                continue

        if client_config := all_active_clients.get(client_code):
            if ouput_key == 'client_code':
                # Handle clients appearing more than once during the same window
                if client_code not in clients or (
                    client_code in clients
                    and dag_run.id > clients.get(client_code, {}).get("dag_run_id", -99999)
                ):
                    clients[client_code] = {
                        **client_config,
                        "dag_run_id": dag_run.id,
                        "dag_id": dag_run.dag_id,
                        "dag_execution_date": str(dag_run.execution_date),
                        "dag_end_date": str(dag_run.end_date),
                    }
            elif ouput_key == 'dag_run_id':
                dag_run_id = dag_run.id
                clients[dag_run_id] = {
                    **client_config,
                    "dag_run_id": dag_run.id,
                    "dag_id": dag_run.dag_id,
                    "dag_execution_date": str(dag_run.execution_date),
                    "dag_end_date": str(dag_run.end_date),
                    "client_code": client_code,
                }
        else:
            ti.log.warning(
                f"client {client_code} config not found, maybe not active in ims_active_clients variable"
            )
    return clients


def process_task_runs(
    upper_window: str,
    lower_window: str,
    dag_id_pattern: str,
    session: Session,
    ti: TaskInstance,
    all_active_fleet_customer_ids: dict,
    all_active_clients: dict,
    task_id_pattern: Optional[str],
    is_per_shard_dag: Optional[bool],
    ouput_key: Optional[str] = "dag_run_id",
):
    query = (
        session.query(
            TaskInstance.dag_id,
            TaskInstance.task_id,
            TaskInstance.end_date,
            DagRun.id,
            DagRun.run_id,
            DagRun.state,
            DagRun.execution_date,
            DagRun.conf,
            )
        .join(DagRun, (TaskInstance.dag_id == DagRun.dag_id) & (TaskInstance.run_id == DagRun.run_id))
        .filter(
            DagRun.dag_id.like(dag_id_pattern),
            TaskInstance.end_date > lower_window,
            TaskInstance.end_date <= upper_window,
            TaskInstance.task_id.like(task_id_pattern),
            TaskInstance.state == TaskInstanceState.SUCCESS
        )
    )

    task_instances = query.all()
    ti.log.info(
        f"found {len(task_instances)} for {dag_id_pattern} for time window where taskinstance.end_date > {lower_window} and <= {upper_window}"
    )

    clients = {}

    for task_instance in task_instances:
        if is_per_shard_dag:
            fleet_customer_id = task_instance.conf["fleet_customer_id"]
            client_code = all_active_fleet_customer_ids[str(fleet_customer_id)][
                "client_code"
            ]
        else:
            regex_pattern = re.escape(dag_id_pattern).replace("%", r"(.*?)")
            client_code = re.fullmatch(regex_pattern, task_instance.dag_id).group(1)

        if client_config := all_active_clients.get(client_code):
            if ouput_key == 'dag_run_id':
                dag_run_id = task_instance.id
                clients[dag_run_id] = {
                    **client_config,
                    "dag_run_id": task_instance.id,
                    "dag_id": task_instance.dag_id,
                    "dag_execution_date": str(task_instance.execution_date),
                    "task_instance_date": str(task_instance.end_date),
                    "client_code": client_code,
                }
        else:
            ti.log.warning(
                f"client {client_code} config not found, maybe not active in ims_active_clients variable"
            )
    return clients


@provide_session
def list_task_runs_within_window(
    upper_window: str,
    lower_window: str,
    dag_id_pattern: str,
    session: Session,
    ti: TaskInstance,
    environment: Optional[str] = "dev",
    include_multitenant: Optional[bool] = False,
    task_id_pattern: Optional[str] = "",
    is_per_shard_dag: Optional[bool] = False,
    filter_nightly_clients_only: Optional[bool] = True,
    dag_run_state: Optional[str] = "SUCCESS",
    ouput_key: Optional[str] = "client_code",
    granularity: Optional[str] = "dag_run",
):
    """
    List all task runs that finished within the specified time window.
    Extract client codes from DAG tags matching the pattern 'client-dag-%-v1'.
    """

    if not (filter_nightly_clients_only):
        ALL_ACTIVE_CLIENTS = {
            ras_code: values
            for ras_code, values in ALL_CLIENTS.items()
            if values["sales_pipeline_type"] == "nightly"
            and values["is_ims_active_client"] == True
        }
    else:
        ALL_ACTIVE_CLIENTS = ALL_CLIENTS

    upper_window = datetime.fromisoformat(upper_window)
    if lower_window:
        lower_window = datetime.fromisoformat(lower_window)
    else:
        lower_window = upper_window - timedelta(minutes=2)

    if include_multitenant and environment:
        ALL_ACTIVE_CLIENTS["multi"] = {
            "gcp_project_id": GCP_PROJECT_ID_MULTITENANT[environment],
            "sales_client_id": 10000,
            "fleet_customer_id": 10000,
            "sales_pipeline_type": "nightly",
            "is_ims_active_client": True,
        }

    ALL_ACTIVE_FLEET_CUSTOMER_IDS = {}
    if is_per_shard_dag:
        for client_code, client_config in ALL_ACTIVE_CLIENTS.items():
            client_config["client_code"] = client_code
            ALL_ACTIVE_FLEET_CUSTOMER_IDS[str(client_config["fleet_customer_id"])] = (
                client_config
            )
    if task_id_pattern:
        ti.log.info(f"Tasks filters by {task_id_pattern}")

    if granularity == 'dag_run':
        clients = process_dag_runs(
            upper_window,
            lower_window,
            dag_id_pattern,
            session,
            ti,
            ALL_ACTIVE_FLEET_CUSTOMER_IDS,
            ALL_ACTIVE_CLIENTS,
            task_id_pattern,
            is_per_shard_dag,
            dag_run_state,
            ouput_key,
        )
    else:
        clients = process_task_runs(
            upper_window,
            lower_window,
            dag_id_pattern,
            session,
            ti,
            ALL_ACTIVE_FLEET_CUSTOMER_IDS,
            ALL_ACTIVE_CLIENTS,
            task_id_pattern,
            is_per_shard_dag,
            ouput_key,
        )
    return clients


def chunk_list(data, chunk_size):
    for i in range(0, len(data), chunk_size):
        yield data[i : i + chunk_size]


def union_all_format_upsert_query(
    ti: TaskInstance,
    project_id: str,
    source_task_id: str,
    query_upsert_template: str,
    query_select_template: str,
    variable_dict: dict,
    union_all_chunk_size: Optional[int] = 100,
):

    clients = list(ti.xcom_pull(task_ids=source_task_id).items())
    ti.log.info(
        f"formatting queries for #{len(clients)} clients using chunk size {union_all_chunk_size}"
    )
    upsert_queries_env = []

    # we evict to create a giant query with to many union all operations, so we have an 100 arbitrary number as split
    for chunk in chunk_list(clients, int(union_all_chunk_size)):
        variable_dict_copy = variable_dict.copy()
        client_queries = []
        client_ids = []

        for ras_code, client in chunk:
            variable_dict_copy["PROJECT_ID"] = client["gcp_project_id"]
            variable_dict_copy["RAS_CODE"] = ras_code
            client_queries.append(query_select_template.format(**variable_dict_copy))
            client_ids.append(str(client["sales_client_id"]))

        variable_dict_copy.pop("PROJECT_ID", None)

        variable_dict_copy["CLIENT_IDS"] = ",".join(client_ids)
        variable_dict_copy["SELECT_CLIENTS"] = "\nUNION ALL\n".join(client_queries)

        upsert_queries_env.append(
            {
                "QUERY": query_upsert_template.format(**variable_dict_copy),
                "GCP_PROJECT_ID": project_id,
            }
        )

    return upsert_queries_env


@provide_session
def enrich_task_runs_with_xcom(
    source_task_id: str,
    tasks_config: dict,
    session: Session,
    ti: TaskInstance,
    union_all_chunk_size: Optional[int] = 0,
    ouput_key: Optional[str] = "client_code",
):
    """
    Retrieves XCOM values for the specified DAG IDs.
    xcom_configs should specify the task_id, key (defaults to return_value) and the value.
    """
    clients = ti.xcom_pull(task_ids=source_task_id)
    if ouput_key == 'client_code':
        dag_run_ids = [client["dag_run_id"] for client in clients.values()]
    elif ouput_key == 'dag_run_id':
        dag_run_ids = list(clients.keys())

    filters = []
    for task_id, configs in tasks_config.items():
        for config in configs:
            key = config.get("key", "return_value")
            if not config.get("multiple_matches", False):
                filters.append(and_(XCom.task_id == task_id, XCom.key == key))
            else:
                filters.append(and_(XCom.task_id.like(task_id + "%"), XCom.key == key))

    # Query XComs for the given DAG run IDs
    results = (
        session.query(XCom)
        .filter(
            XCom.dag_run_id.in_(dag_run_ids),
            or_(*filters),
        )
        .all()
    )

    # Prepare a dictionary of results
    xcom_data = {}
    for xcom in results:
        outputs = {}
        if xcom.dag_run_id in xcom_data:
            outputs = xcom_data[xcom.dag_run_id]
        matched_task_id = xcom.task_id
        if matched_task_id not in tasks_config:
            for task_id in tasks_config.keys():
                if task_id in matched_task_id:
                    matched_task_id = task_id
        for config in tasks_config[matched_task_id]:
            if config["value"] in xcom.value:
                xcom_value = xcom.value[config["value"]]
                if config.get("multiple_matches", False):
                    if outputs.get(config["output_as"]):
                        outputs.get(config["output_as"]).append(xcom_value)
                    else:
                        outputs[config["output_as"]] = [xcom_value]
                else:
                    outputs[config["output_as"]] = xcom_value
        if xcom.dag_run_id not in xcom_data:
            xcom_data[xcom.dag_run_id] = outputs

    for key, client_config in clients.items():
        clients[key]["xcom"] = xcom_data[client_config["dag_run_id"]]

    if int(union_all_chunk_size) > 0:
        return [
            dict(d) for d in chunk_list(list(clients.items()), int(union_all_chunk_size))
        ]
    else:
        return clients
