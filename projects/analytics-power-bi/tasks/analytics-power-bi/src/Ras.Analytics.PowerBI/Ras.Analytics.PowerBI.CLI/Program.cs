﻿using CommandLine;
using Ras.Analytics.PowerBi.CLI;
using Serilog;
using System.Text.Json;
using System.Net.Http.Headers;
using Ras.Analytics.PowerBI.CLI;
using Google.Cloud.Storage.V1;
using Ras.Analytics.PowerBI.CLI.Processors;

class Program
{
    private static StorageClient _storageClient;

    static async Task<int> Main(string[] args)
    {
        await Init();

        var configPath = $"{AppDomain.CurrentDomain.BaseDirectory}configuration.json";
        
        Console.WriteLine($"Reading configuration file: {configPath}");

        var configuration = JsonSerializer.Deserialize<Configuration>(File.ReadAllText(configPath),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        var exitCode = ExitCodes.Success;

        Parser.Default.ParseArguments<CommandOptions>(args)
            .WithParsed(options =>
            {
                try
                {
                    exitCode = Task.Run(async () => await Run(options, configuration)).Result;
                }
                catch (Exception ex)
                {
                    exitCode = ExitCodes.Error;
                }
            })
            .WithNotParsed(errors =>
            {
                exitCode = ExitCodes.Error;
            });

        Log.CloseAndFlush();

        Thread.Sleep(1500);

        return (int)exitCode;
    }

    static async Task Init()
    {
        _storageClient = StorageClient.Create();
    }

    static async Task<ExitCodes> Run(CommandOptions commandOptions, Configuration configuration)
    {
        if (configuration == null)
        {
            Console.WriteLine("Configuration not found.");
            return ExitCodes.Error;
        }

        var fileType = commandOptions.FileType;
        var appConfig = configuration.AppSettings;
        var clientConfig = configuration.Clients.Single(x => x.ClientCode.Equals(commandOptions.ClientCode, StringComparison.InvariantCultureIgnoreCase));
        var clientQueries = configuration.ClientQueries.Single(x => x.ClientCode.Equals(commandOptions.ClientCode, StringComparison.InvariantCultureIgnoreCase));

        var accessToken = await GetAccessTokenAsync(clientConfig);

        Console.WriteLine($"Processing File type. {fileType}");

        if (!clientQueries.Queries.TryGetValue(fileType, out var query))
        {
            Console.WriteLine($"File Type query not implemented. {fileType}");

            return ExitCodes.Error;
        }

        if (!string.IsNullOrEmpty(accessToken))
        {
            Console.WriteLine("Successfully authenticated. Bearer token received.");

            var gcpPath = $"{appConfig.OutputPath}/{clientConfig.ClientName}";
            var backupPath = $"{appConfig.BackupPath}/{clientConfig.ClientName}";
            var deltaEnabled = clientQueries.DeltaEnabled;

            await QueryPowerBIAsync(clientConfig.ClientCode, fileType, accessToken, clientConfig.DatasetId, query, gcpPath, backupPath, deltaEnabled);
        }
        else
        {
            Console.WriteLine("Failed to authenticate.");
            return ExitCodes.Error;
        }

        return ExitCodes.Success;
    }

    static async Task<string?> GetAccessTokenAsync(Client client)
    {
        var tokenUrl = $"https://login.microsoftonline.com/{client.TenantId}/oauth2/v2.0/token";
        var scope = "https://analysis.windows.net/powerbi/api/.default";

        using var httpClient = new HttpClient();
        var formData = new MultipartFormDataContent
        {
            { new StringContent("password"), "grant_type" },
            { new StringContent(scope), "scope" },
            { new StringContent(client.ClientId), "client_id" },
            { new StringContent(client.UserName), "username" },
            { new StringContent(client.Password), "password" },
            { new StringContent(client.Secret), "client_secret" }
        };

        var response = await httpClient.PostAsync(tokenUrl, formData);

        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(content);
            return tokenResponse?.access_token;
        }
        else
        {
            Console.WriteLine($"Request failed with status code: {response.StatusCode}");
            string errorContent = await response.Content.ReadAsStringAsync();
            Console.WriteLine(errorContent);
            return null;
        }
    }

    static async Task QueryPowerBIAsync(string clientCode, string fileType, string token, string datasetId, string query, string gcpPath, string backupPath, bool deltaEnabled)
    {
        if (string.IsNullOrEmpty(token))
        {
            Console.WriteLine("Access token is null or empty.");
            return;
        }

        using (var httpClient = new HttpClient())
        {
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            string url = $"https://api.powerbi.com/v1.0/myorg/datasets/{datasetId}/executeQueries";

            var payload = new
            {
                queries = new[]
                {
                    new
                    {
                        query
                    }
                }
            };

            var jsonPayload = JsonSerializer.Serialize(payload);
            var content = new StringContent(jsonPayload, System.Text.Encoding.UTF8, "application/json");

            try
            {
                var response = await httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine("Response from Power BI success!");                    

                    var parsedStream = FileProcessor.Parse(responseContent);

                    Console.WriteLine($"Uploading file {fileType} to GCP");

                    

                    var fileGcpPath = $"{gcpPath}/{clientCode}{BuildFileName(fileType, deltaEnabled)}";
                    var backupGcpPath = $"{backupPath}/{clientCode}{BuildFileName(fileType, deltaEnabled)}";

                    await UploadMemoryStreamAsync(backupGcpPath, parsedStream);
                    await UploadMemoryStreamAsync(fileGcpPath, parsedStream);
                }
                else
                {
                    Console.WriteLine($"Request failed with status code {response.StatusCode}");
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine(errorContent);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error querying Power BI: {ex.Message}");
                Console.WriteLine($"Error querying Power BI: {ex.StackTrace}");
            }
        }
    }

    public static string BuildFileName(string fileType, bool deltaEnabled)
    {
        var currentDate = DateTime.Now;

        var shouldAddDeltaSuffix = deltaEnabled && currentDate.Day != 1;
        var deltaFileMask = 
            $"{fileType}{currentDate:yyyyMMdd}{(shouldAddDeltaSuffix ? "Delta" : "")}.txt";

        return fileType switch
        {
            "EQP" => $"{fileType}{currentDate:yyyyMMdd}.txt",
            _ => deltaFileMask,
        };
    }

    public static async Task UploadMemoryStreamAsync(string objectName, MemoryStream memoryStream)
    {
        try
        {
            var bucketName = objectName.Split("/")[0];
            var filePath = objectName.Substring(objectName.IndexOf("/") + 1);
            
            // Upload the stream to the bucket
            await _storageClient.UploadObjectAsync(bucketName, filePath, null, memoryStream);
            Console.WriteLine($"File uploaded to {objectName}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error uploading file: {ex.Message}");
            throw;
        }
    }
}
