﻿namespace Ras.Analytics.PowerBi.CLI;

public record AppSettings
{
    public bool Verbose { get; init; }
    public bool Overwrite { get; init; }
    public string OutputPath { get; init; }
    public string BackupPath { get; init; }
    public string Loggly<PERSON><PERSON><PERSON>ey { get; init; }
}

public record Client
{
    public string ClientName { get; init; }
    public string ClientCode { get; init; }
    public string FolderName { get; init; }
    public string TenantId { get; init; }
    public string ClientId { get; init; }
    public string DatasetId { get; init; }
    public string UserName { get; init; }
    public string Password { get; init; }
    public string Secret { get; init; }
}

public record ClientQueries
{    
    public string ClientCode { get; init; }
    
    public Dictionary<string, string> Queries { get; init; }

    public bool DeltaEnabled { get; init; }
}

public record Configuration
{
    public AppSettings AppSettings { get; init; }

    public List<Client> Clients { get; init; }

    public List<ClientQueries> ClientQueries { get; init; }
}