{"AppSettings": {"Verbose": true, "Overwrite": true, "OutputPath": "@BUCKET_ID@/analytics-powerbi/analytics/inbound", "BackupPath": "@BUCKET_ID@/analytics-powerbi/archive"}, "Clients": "@API_KEYS@", "ClientQueries": [{"ClientCode": "FRCD", "DeltaEnabled": true, "Queries": {"BRF": "DEFINE VAR __DS0FilterTable = TREATAS({\"Rental Office\"}, 'Department'[Department Type]) VAR __DS0Core = CALCULATETABLE( SUMMARIZE( 'Department', 'Department'[Name], 'Department'[Number], 'Department'[AddressCode], 'Department'[DepartmentCity], 'Department'[DepartmentState], 'Department'[Department Type] ), KEEPFILTERS(__DS0FilterTable) ) VAR __DS0BodyLimited = TOPN( 500000, __DS0Core, 'Department'[AddressCode], 1, 'Department'[Name], 1, 'Department'[Number], 1, 'Department'[DepartmentCity], 1, 'Department'[DepartmentState], 1, 'Department'[Department Type], 1 ) EVALUATE __DS0BodyLimited ORDER BY 'Department'[AddressCode], 'Department'[Name], 'Department'[Number], 'Department'[DepartmentCity], 'Department'[DepartmentState], 'Department'[Department Type]", "CUS": "DEFINE VAR __DS0Core = SUMMARIZE( 'Dim Customer', 'Dim Customer'[CustomerCode], 'Dim Customer'[CustomerName], 'Dim Customer'[CustomerStatus], 'Dim Customer'[CustomerParent], 'Dim Customer'[CustomerParentName], 'Dim Customer'[Telephone], 'Dim Customer'[CustomerAddress1], 'Dim Customer'[CustomerCity], 'Dim Customer'[CustomerCountry], 'Dim Customer'[CustomerStateProvince], 'Dim Customer'[CustomerZipCode], 'Dim Customer'[CustomerExternalEquipRep], 'Dim Customer'[OldCustomerCode] ) VAR __DS0BodyLimited = TOPN( 500000, __DS0Core, 'Dim Customer'[CustomerCode], 1, 'Dim Customer'[CustomerName], 1, 'Dim Customer'[CustomerStatus], 1, 'Dim Customer'[CustomerParent], 1, 'Dim Customer'[CustomerParentName], 1, 'Dim Customer'[Telephone], 1, 'Dim Customer'[CustomerAddress1], 1, 'Dim Customer'[CustomerCity], 1, 'Dim Customer'[CustomerCountry], 1, 'Dim Customer'[CustomerStateProvince], 1, 'Dim Customer'[CustomerZipCode], 1, 'Dim Customer'[CustomerExternalEquipRep], 1, 'Dim Customer'[OldCustomerCode], 1 ) EVALUATE __DS0BodyLimited ORDER BY 'Dim Customer'[CustomerCode], 'Dim Customer'[CustomerName], 'Dim Customer'[CustomerStatus], 'Dim Customer'[CustomerParent], 'Dim Customer'[CustomerParentName], 'Dim Customer'[Telephone], 'Dim Customer'[CustomerAddress1], 'Dim Customer'[CustomerCity], 'Dim Customer'[CustomerCountry], 'Dim Customer'[CustomerStateProvince], 'Dim Customer'[CustomerZipCode], 'Dim Customer'[CustomerExternalEquipRep], 'Dim Customer'[OldCustomerCode]", "EPT": "DEFINE VAR __DS0FilterTable = TREATAS({\"Rental\"}, 'Equipment'[Fleet Status]) VAR __DS0FilterTable2 = FILTER( KEEPFILTERS(VALUES('Equipment'[Rental Category])), NOT('Equipment'[Rental Category] IN {\"ALLIED\"}) ) VAR __DS0Core = SELECTCOLUMNS( KEEPFILTERS( FILTER( KEEPFILTERS( SUMMARIZECOLUMNS( 'Equipment'[Rental Category], 'Equipment'[Rental Category Name], 'Equipment'[Model], 'Equipment'[Manufacturer], 'Dim Model'[RentalPriceCategory], __DS0FilterTable, __DS0FilterTable2, \"CountRowsFact_Parts_Work_Order\", COUNTROWS('Fact Parts Work Order') ) ), OR( OR( OR( OR( NOT(ISBLANK('Equipment'[Rental Category])), NOT(ISBLANK('Equipment'[Rental Category Name])) ), NOT(ISBLANK('Equipment'[Model])) ), NOT(ISBLANK('Equipment'[Manufacturer])) ), NOT(ISBLANK('Dim Model'[RentalPriceCategory])) ) ) ), \"'Equipment'[Rental Category]\", 'Equipment'[Rental Category], \"'Equipment'[Rental Category Name]\", 'Equipment'[Rental Category Name], \"'Equipment'[Model]\", 'Equipment'[Model], \"'Equipment'[Manufacturer]\", 'Equipment'[Manufacturer], \"'Dim Model'[RentalPriceCategory]\", 'Dim Model'[RentalPriceCategory] ) VAR __DS0BodyLimited = TOPN( 500000, __DS0Core, 'Equipment'[Rental Category], 1, 'Equipment'[Rental Category Name], 1, 'Equipment'[Model], 1, 'Equipment'[Manufacturer], 1, 'Dim Model'[RentalPriceCategory], 1 ) EVALUATE __DS0BodyLimited ORDER BY 'Equipment'[Rental Category], 'Equipment'[Rental Category Name], 'Equipment'[Model], 'Equipment'[Manufacturer], 'Dim Model'[RentalPriceCategory]", "EQP": "DEFINE VAR __DS0FilterTable = FILTER( KEEPFILTERS(VALUES('Equipment'[Ownership Status])), NOT('Equipment'[Ownership Status] IN {\"Customer Stock\"}) ) VAR __DS0Core = SUMMARIZECOLUMNS( 'Equipment'[Equipment Type], 'Equipment'[Owner Rental Office], 'Equipment'[Physical Status], 'Equipment'[Unit Description], 'Equipment'[Under Prep/Repair], 'Equipment'[Rental Category], 'Equipment'[Model], 'Equipment'[Fleet Status], 'Equipment'[Current Rental Office], 'Equipment'[Serial Number], 'Equipment'[Transfer To Rental Fleet Date], 'Equipment'[Unit Number], 'Equipment'[Status], 'Equipment'[Rental Category Name], 'Equipment'[Manufacturer Name], 'Equipment'[Meter1Type], 'Equipment'[Product Category], 'Equipment'[Product Category Name], 'Equipment'[Ownership Status], 'Equipment'[SalesInvoiceDate], __DS0FilterTable, \"SumModel_Year\", CALCULATE(SUM('Equipment'[Model Year])), \"SumPurchase_Price\", CALCULATE(SUM('Equipment'[Purchase Price])), \"Book_Value\", 'Fact Equipment Summary'[Book Value], \"Acqusition_Cost\", 'Fact Equipment Summary'[Acqusition Cost], \"Accumulated Depreciation\", 'Fact Equipment Summary'[Accumulated Depreciation], \"SumQuantity\", CALCULATE(SUM('Fact Equipment Rental'[Quantity])), \"SumMeter1Reading\", CALCULATE(SUM('Equipment'[Meter1Reading])) ) VAR __DS0BodyLimited = TOPN( 500000, __DS0Core, 'Equipment'[Current Rental Office], 1, 'Equipment'[Equipment Type], 1, 'Equipment'[Owner Rental Office], 1, 'Equipment'[Physical Status], 1, 'Equipment'[Unit Description], 1, 'Equipment'[Under Prep/Repair], 1, 'Equipment'[Rental Category], 1, 'Equipment'[Model], 1, 'Equipment'[Fleet Status], 1, 'Equipment'[Serial Number], 1, 'Equipment'[Transfer To Rental Fleet Date], 1, 'Equipment'[Unit Number], 1, 'Equipment'[Status], 1, 'Equipment'[Rental Category Name], 1, 'Equipment'[Manufacturer Name], 1, 'Equipment'[Meter1Type], 1, 'Equipment'[Product Category], 1, 'Equipment'[Product Category Name], 1, 'Equipment'[Ownership Status], 1, 'Equipment'[SalesInvoiceDate], 1 ) EVALUATE __DS0BodyLimited ORDER BY 'Equipment'[Current Rental Office], 'Equipment'[Equipment Type], 'Equipment'[Owner Rental Office], 'Equipment'[Physical Status], 'Equipment'[Unit Description], 'Equipment'[Under Prep/Repair], 'Equipment'[Rental Category], 'Equipment'[Model], 'Equipment'[Fleet Status], 'Equipment'[Serial Number], 'Equipment'[Transfer To Rental Fleet Date], 'Equipment'[Unit Number], 'Equipment'[Status], 'Equipment'[Rental Category Name], 'Equipment'[Manufacturer Name], 'Equipment'[Meter1Type], 'Equipment'[Product Category], 'Equipment'[Product Category Name], 'Equipment'[Ownership Status], 'Equipment'[SalesInvoiceDate]", "IDF": "DEFINE VAR __DS0FilterTable = FILTER( KEEPFILTERS(VALUES('Invoice Date'[Invoice Date])), AND( 'Invoice Date'[Invoice Date] >= DATE(YEAR(TODAY()) - 1, MONTH(TODAY()), DAY(TODAY())), 'Invoice Date'[Invoice Date] < TODAY() ) ) VAR __DS0Core = SUMMARIZECOLUMNS( 'Fact Equipment Rental'[Invoice Number], 'Fact Equipment Rental'[Invoiced], 'Fact Equipment Rental'[Contract Number], 'Fact Equipment Rental'[DimEquipRentalKey], 'Fact Equipment Rental'[JobSiteAddressID], 'Fact Equipment Rental'[RateBasis], 'Fact Equipment Rental'[ActualDeliveryDate], 'Fact Equipment Rental'[ActualReturnDate], 'Fact Equipment Rental'[ContractType], 'Fact Equipment Rental'[ContractStatus], 'Fact Equipment Rental'[TransactionType], 'Invoice Date'[Invoice Date], 'Equipment'[Unit Number], 'Fact Equipment Rental'[RateType], 'Fact Equipment Rental'[Shift], 'Fact Equipment Rental'[Description], 'Fact Equipment Rental'[BranchID], 'Equipment'[Current Department], 'Rental End Date'[Rental End Date], 'Return Date'[Return Date], 'Fact Equipment Rental'[ReturnStatus], 'Invoice From Date'[Invoice From Date], 'Invoice To Date'[Invoice To Date], 'Invoice To Customer'[Customer Code], 'Invoice To Customer'[Customer Name], 'Fact Equipment Rental'[IntSalesRepID], 'Fact Equipment Rental'[ExtSalesRepID], 'Internal Sales Rep'[EmployeeNumber], 'External Sales Rep'[EmployeeName], 'Internal Sales Rep'[EmployeeName], 'External Sales Rep'[EmployeeNumber], 'Fact Equipment Rental'[LineStatus], 'Job Site Address'[PostalZipCode], 'Job Site Address'[Address1], 'Job Site Address'[City], 'Job Site Address'[Country], 'Job Site Address'[State], 'Job Site Address'[StateDescription], 'Equipment'[Product Category], 'Rental Start Date'[Rental Start Date], 'Fact Equipment Rental'[ItemType], 'Fact Equipment Rental'[RentalCategory], __DS0FilterTable, \"SumOrderInvoiceSequence\", CALCULATE(SUM('Fact Equipment Rental'[OrderInvoiceSequence])), \"Invoice_Amount\", 'Fact Equipment Rental'[Invoice Amount], \"SumDayRate\", CALCULATE(SUM('Fact Equipment Rental'[DayRate])), \"SumDiscountAmt\", CALCULATE(SUM('Fact Equipment Rental'[DiscountAmt])), \"SumQuantity\", CALCULATE(SUM('Fact Equipment Rental'[Quantity])), \"Rental_Hours\", 'Fact Equipment Rental'[Rental Hours], \"Rental_Rate\", 'Fact Equipment Rental'[Rental Rate], \"SumRentalAmount\", CALCULATE(SUM('Fact Equipment Rental'[RentalAmount])), \"SumRentalDays\", CALCULATE(SUM('Fact Equipment Rental'[RentalDays])), \"SumMonthRate\", CALCULATE(SUM('Fact Equipment Rental'[MonthRate])), \"SumSequence\", CALCULATE(SUM('Fact Equipment Rental'[Sequence])), \"SumWeekRate\", CALCULATE(SUM('Fact Equipment Rental'[WeekRate])), \"SumOffRentMeterIn\", CALCULATE(SUM('Fact Equipment Rental'[OffRentMeterIn])), \"SumMeterOut\", CALCULATE(SUM('Fact Equipment Rental'[MeterOut])), \"SumBookRate\", CALCULATE(SUM('Fact Equipment Rental'[BookRate])), \"SumOver_Usage_Charges\", CALCULATE(SUM('Fact Equipment Rental'[Over Usage Charges])) ) VAR __DS0BodyLimited = TOPN( 500000, __DS0Core, [SumOrderInvoiceSequence], 1, 'Fact Equipment Rental'[Invoice Number], 1, 'Fact Equipment Rental'[Invoiced], 1, 'Fact Equipment Rental'[Contract Number], 1, 'Fact Equipment Rental'[DimEquipRentalKey], 1, 'Fact Equipment Rental'[JobSiteAddressID], 1, 'Fact Equipment Rental'[RateBasis], 1, 'Fact Equipment Rental'[ActualDeliveryDate], 1, 'Fact Equipment Rental'[ActualReturnDate], 1, 'Fact Equipment Rental'[ContractType], 1, 'Fact Equipment Rental'[ContractStatus], 1, 'Fact Equipment Rental'[TransactionType], 1, 'Invoice Date'[Invoice Date], 1, 'Equipment'[Unit Number], 1, 'Fact Equipment Rental'[RateType], 1, 'Fact Equipment Rental'[Shift], 1, 'Fact Equipment Rental'[Description], 1, 'Fact Equipment Rental'[BranchID], 1, 'Equipment'[Current Department], 1, 'Rental End Date'[Rental End Date], 1, 'Return Date'[Return Date], 1, 'Fact Equipment Rental'[ReturnStatus], 1, 'Invoice From Date'[Invoice From Date], 1, 'Invoice To Date'[Invoice To Date], 1, 'Invoice To Customer'[Customer Code], 1, 'Invoice To Customer'[Customer Name], 1, 'Fact Equipment Rental'[IntSalesRepID], 1, 'Fact Equipment Rental'[ExtSalesRepID], 1, 'Internal Sales Rep'[EmployeeNumber], 1, 'External Sales Rep'[EmployeeName], 1, 'Internal Sales Rep'[EmployeeName], 1, 'External Sales Rep'[EmployeeNumber], 1, 'Fact Equipment Rental'[LineStatus], 1, 'Job Site Address'[PostalZipCode], 1, 'Job Site Address'[Address1], 1, 'Job Site Address'[City], 1, 'Job Site Address'[Country], 1, 'Job Site Address'[State], 1, 'Job Site Address'[StateDescription], 1, 'Equipment'[Product Category], 1, 'Rental Start Date'[Rental Start Date], 1, 'Fact Equipment Rental'[ItemType], 1, 'Fact Equipment Rental'[RentalCategory], 1 ) EVALUATE __DS0BodyLimited ORDER BY [SumOrderInvoiceSequence], 'Fact Equipment Rental'[Invoice Number], 'Fact Equipment Rental'[Invoiced], 'Fact Equipment Rental'[Contract Number], 'Fact Equipment Rental'[DimEquipRentalKey], 'Fact Equipment Rental'[JobSiteAddressID], 'Fact Equipment Rental'[RateBasis], 'Fact Equipment Rental'[ActualDeliveryDate], 'Fact Equipment Rental'[ActualReturnDate], 'Fact Equipment Rental'[ContractType], 'Fact Equipment Rental'[ContractStatus], 'Fact Equipment Rental'[TransactionType], 'Invoice Date'[Invoice Date], 'Equipment'[Unit Number], 'Fact Equipment Rental'[RateType], 'Fact Equipment Rental'[Shift], 'Fact Equipment Rental'[Description], 'Fact Equipment Rental'[BranchID], 'Equipment'[Current Department], 'Rental End Date'[Rental End Date], 'Return Date'[Return Date], 'Fact Equipment Rental'[ReturnStatus], 'Invoice From Date'[Invoice From Date], 'Invoice To Date'[Invoice To Date], 'Invoice To Customer'[Customer Code], 'Invoice To Customer'[Customer Name], 'Fact Equipment Rental'[IntSalesRepID], 'Fact Equipment Rental'[ExtSalesRepID], 'Internal Sales Rep'[EmployeeNumber], 'External Sales Rep'[EmployeeName], 'Internal Sales Rep'[EmployeeName], 'External Sales Rep'[EmployeeNumber], 'Fact Equipment Rental'[LineStatus], 'Job Site Address'[PostalZipCode], 'Job Site Address'[Address1], 'Job Site Address'[City], 'Job Site Address'[Country], 'Job Site Address'[State], 'Job Site Address'[StateDescription], 'Equipment'[Product Category], 'Rental Start Date'[Rental Start Date], 'Fact Equipment Rental'[ItemType], 'Fact Equipment Rental'[RentalCategory]", "SRP": "DEFINE VAR __DS0Core = SUMMARIZE( 'External Sales Rep', 'External Sales Rep'[EmployeeNumber], 'External Sales Rep'[EmployeeName] ) VAR __DS0BodyLimited = TOPN( 500000, __DS0Core, 'External Sales Rep'[EmployeeNumber], 1, 'External Sales Rep'[EmployeeName], 1 ) EVALUATE __DS0BodyLimited ORDER BY 'External Sales Rep'[EmployeeNumber], 'External Sales Rep'[EmployeeName]"}}, {"ClientCode": "NEWM", "DeltaEnabled": true, "Queries": {"BRF": "DEFINE  VAR __DS0FilterTable =   TREATAS({\"Rental Office\"}, 'Department'[Department Type])  VAR __DS0Core =   CALCULATETABLE(  SUMMARIZE(  'Department',  'Department'[Name],  'Department'[Number],  'Department'[AddressCode],  'Department'[DepartmentCity],  'Department'[DepartmentState],  'Department'[Department Type]  ),  KEEPFILTERS(__DS0FilterTable)  )  VAR __DS0BodyLimited =   TOPN(  500000,  __DS0Core,  'Department'[AddressCode],  1,  'Department'[Name],  1,  'Department'[Number],  1,  'Department'[DepartmentCity],  1,  'Department'[DepartmentState],  1,  'Department'[Department Type],  1  ) EVALUATE  __DS0BodyLimited ORDER BY  'Department'[AddressCode],  'Department'[Name],  'Department'[Number],  'Department'[DepartmentCity],  'Department'[DepartmentState],  'Department'[Department Type]", "CUS": "DEFINE  VAR __DS0Core =   SUMMARIZE(  'Invoice To Customer',  'Invoice To Customer'[Customer Code],  'Invoice To Customer'[Customer Name],  'Invoice To Customer'[Customer Class],  'Invoice To Customer'[Customer Class Name],  'Invoice To Customer'[Customer Parent],  'Invoice To Customer'[Customer Parent Name],  'Invoice To Customer'[Customer City],  'Invoice To Customer'[Customer Zip Code],  'Invoice To Customer'[Customer State Province]  )  VAR __DS0BodyLimited =   TOPN(  500000,  __DS0Core,  'Invoice To Customer'[Customer Code],  1,  'Invoice To Customer'[Customer Name],  1,  'Invoice To Customer'[Customer Class],  1,  'Invoice To Customer'[Customer Class Name],  1,  'Invoice To Customer'[Customer Parent],  1,  'Invoice To Customer'[Customer Parent Name],  1,  'Invoice To Customer'[Customer City],  1,  'Invoice To Customer'[Customer Zip Code],  1,  'Invoice To Customer'[Customer State Province],  1  ) EVALUATE  __DS0BodyLimited ORDER BY  'Invoice To Customer'[Customer Code],  'Invoice To Customer'[Customer Name],  'Invoice To Customer'[Customer Class],  'Invoice To Customer'[Customer Class Name],  'Invoice To Customer'[Customer Parent],  'Invoice To Customer'[Customer Parent Name],  'Invoice To Customer'[Customer City],  'Invoice To Customer'[Customer Zip Code],  'Invoice To Customer'[Customer State Province]", "EPT": "DEFINE  VAR __DS0Core =   SUMMARIZE(  'Equipment',  'Equipment'[Rental Category],  'Equipment'[Rental Category Name],  'Equipment'[Product Category],  'Equipment'[Product Category Name]  )  VAR __DS0BodyLimited =   TOPN(  500000,  __DS0Core,  'Equipment'[Rental Category],  1,  'Equipment'[Rental Category Name],  1,  'Equipment'[Product Category],  1,  'Equipment'[Product Category Name],  1  ) EVALUATE  __DS0BodyLimited ORDER BY  'Equipment'[Rental Category],  'Equipment'[Rental Category Name],  'Equipment'[Product Category],  'Equipment'[Product Category Name]", "EQP": "DEFINE VAR __DS0Core = SUMMARIZECOLUMNS( 'Equipment'[Unit Number], 'Equipment'[Fleet Status], 'Equipment'[Physical Status], 'Equipment'[Status], 'Equipment'[Ownership Status], 'Equipment'[Rental Category], 'Equipment'[Rental Category Name], 'Equipment'[Product Category], 'Equipment'[Product Category Name], 'Equipment'[Model], 'Equipment'[Manufacturer Name], 'Equipment'[Rental Contract], 'Equipment'[RentalCustomerCode], 'Equipment'[SalesInvoiceDate], 'Equipment'[Transfer To Rental Fleet Date], 'Equipment'[Rental Customer Name], 'Equipment'[Current Rental Office], 'Equipment'[Unit Description], 'Equipment'[Serial Number], 'Equipment'[Meter1Type], 'Equipment'[Equipment Type], 'Equipment'[Owner Rental Office], 'Equipment'[Under Prep/Repair], \"SumModel_Year\", CALCULATE(SUM('Equipment'[Model Year])), \"Acqusition_Cost\", 'Fact Equipment Summary'[Acqusition Cost], \"SumPurchase_Price\", CALCULATE(SUM('Equipment'[Purchase Price])), \"SumMeter1Reading\", CALCULATE(SUM('Equipment'[Meter1Reading])), \"SumBook\", CALCULATE(SUM('Equipment'[Book])) ) VAR __DS0BodyLimited = TOPN( 500000, __DS0Core, 'Equipment'[Unit Number], 1, 'Equipment'[Fleet Status], 1, 'Equipment'[Physical Status], 1, 'Equipment'[Status], 1, 'Equipment'[Ownership Status], 1, 'Equipment'[Rental Category], 1, 'Equipment'[Rental Category Name], 1, 'Equipment'[Product Category], 1, 'Equipment'[Product Category Name], 1, 'Equipment'[Model], 1, 'Equipment'[Manufacturer Name], 1, 'Equipment'[Rental Contract], 1, 'Equipment'[RentalCustomerCode], 1, 'Equipment'[SalesInvoiceDate], 1, 'Equipment'[Transfer To Rental Fleet Date], 1, 'Equipment'[Rental Customer Name], 1, 'Equipment'[Current Rental Office], 1, 'Equipment'[Unit Description], 1, 'Equipment'[Serial Number], 1, 'Equipment'[Meter1Type], 1, 'Equipment'[Equipment Type], 1, 'Equipment'[Owner Rental Office], 1, 'Equipment'[Under Prep/Repair], 1 ) EVALUATE __DS0BodyLimited ORDER BY 'Equipment'[Unit Number], 'Equipment'[Fleet Status], 'Equipment'[Physical Status], 'Equipment'[Status], 'Equipment'[Ownership Status], 'Equipment'[Rental Category], 'Equipment'[Rental Category Name], 'Equipment'[Product Category], 'Equipment'[Product Category Name], 'Equipment'[Model], 'Equipment'[Manufacturer Name], 'Equipment'[Rental Contract], 'Equipment'[RentalCustomerCode], 'Equipment'[SalesInvoiceDate], 'Equipment'[Transfer To Rental Fleet Date], 'Equipment'[Rental Customer Name], 'Equipment'[Current Rental Office], 'Equipment'[Unit Description], 'Equipment'[Serial Number], 'Equipment'[Meter1Type], 'Equipment'[Equipment Type], 'Equipment'[Owner Rental Office], 'Equipment'[Under Prep/Repair]", "IDF": "DEFINE  VAR __DS0FilterTable =   FILTER(  KEEPFILTERS(VALUES('Invoice Date'[Invoice Date])),  AND(                 'Invoice Date'[Invoice Date] >= DATE(YEAR(TODAY()) - 1, MONTH(TODAY()), DAY(TODAY())),                 'Invoice Date'[Invoice Date] <= TODAY()  )  )  VAR __DS0Core =   SUMMARIZECOLUMNS(  'Fact Equipment Rental'[Contract Number],  'Fact Equipment Rental'[Invoice Number],  'Fact Equipment Rental'[ContractType],  'Fact Equipment Rental'[ContractStatus],  'Fact Equipment Rental'[TransactionType],  'Invoice Date'[Invoice Date],  'Equipment'[Unit Number],  'Fact Equipment Rental'[RateBasis],  'Fact Equipment Rental'[RateType],  'Fact Equipment Rental'[Shift],  'Fact Equipment Rental'[ItemType],  'Fact Equipment Rental'[Description],  'Equipment'[Current Department],  'Fact Equipment Rental'[Rental Start Date],  'Fact Equipment Rental'[Rental End Date],  'Return Date'[Return Date],  'Fact Equipment Rental'[ReturnStatus],  'Equipment'[Current Branch Name],  'Invoice From Date'[Invoice From Date],  'Invoice To Date'[Invoice To Date],  'Invoice To Customer'[Customer Name],  'Invoice To Customer'[Customer Code],  'External Sales Rep'[EmployeeNumber],  'External Sales Rep'[EmployeeName],  'Internal Sales Rep'[EmployeeNumber],  'Internal Sales Rep'[EmployeeName],  'Equipment'[Product Category],  'Equipment'[Rental Category],  'Job Site Address'[PostalZipCode],  'Job Site Address'[Address1],  'Job Site Address'[City],  'Job Site Address'[Country],  'Job Site Address'[State],  __DS0FilterTable,  \"SumSequence\", CALCULATE(SUM('Fact Equipment Rental'[Sequence])),  \"SumRentalDays\", CALCULATE(SUM('Fact Equipment Rental'[RentalDays])),  \"SumRentalHours\", CALCULATE(SUM('Fact Equipment Rental'[RentalHours])),  \"Rental_Rate\", 'Fact Equipment Rental'[Rental Rate],  \"SumWeekRate\", CALCULATE(SUM('Fact Equipment Rental'[WeekRate])),  \"SumMonthRate\", CALCULATE(SUM('Fact Equipment Rental'[MonthRate])),  \"SumDayRate\", CALCULATE(SUM('Fact Equipment Rental'[DayRate])),  \"SumMeterOut\", CALCULATE(SUM('Fact Equipment Rental'[MeterOut])),  \"SumOffRentMeterIn\", CALCULATE(SUM('Fact Equipment Rental'[OffRentMeterIn])),  \"Invoice_Amount\", 'Fact Equipment Rental'[Invoice Amount],  \"SumQuantity\", CALCULATE(SUM('Fact Equipment Rental'[Quantity])),  \"SumBookRate\", CALCULATE(SUM('Fact Equipment Rental'[BookRate])),  \"SumDiscountAmt\", CALCULATE(SUM('Fact Equipment Rental'[DiscountAmt])),  \"RenttoSellEquipCount\", 'Fact Equipment Rental'[RenttoSellEquipCount]  )  VAR __DS0BodyLimited =   TOPN(  500000,  __DS0Core,  'Fact Equipment Rental'[Contract Number],  1,  'Fact Equipment Rental'[Invoice Number],  1,  'Fact Equipment Rental'[ContractType],  1,  'Fact Equipment Rental'[ContractStatus],  1,  'Fact Equipment Rental'[TransactionType],  1,  'Invoice Date'[Invoice Date],  1,  'Equipment'[Unit Number],  1,  'Fact Equipment Rental'[RateBasis],  1,  'Fact Equipment Rental'[RateType],  1,  'Fact Equipment Rental'[Shift],  1,  'Fact Equipment Rental'[ItemType],  1,  'Fact Equipment Rental'[Description],  1,  'Equipment'[Current Department],  1,  'Fact Equipment Rental'[Rental Start Date],  1,  'Fact Equipment Rental'[Rental End Date],  1,  'Return Date'[Return Date],  1,  'Fact Equipment Rental'[ReturnStatus],  1,  'Equipment'[Current Branch Name],  1,  'Invoice From Date'[Invoice From Date],  1,  'Invoice To Date'[Invoice To Date],  1,  'Invoice To Customer'[Customer Name],  1,  'Invoice To Customer'[Customer Code],  1,  'External Sales Rep'[EmployeeNumber],  1,  'External Sales Rep'[EmployeeName],  1,  'Internal Sales Rep'[EmployeeNumber],  1,  'Internal Sales Rep'[EmployeeName],  1,  'Equipment'[Product Category],  1,  'Equipment'[Rental Category],  1,  'Job Site Address'[PostalZipCode],  1,  'Job Site Address'[Address1],  1,  'Job Site Address'[City],  1,  'Job Site Address'[Country],  1,  'Job Site Address'[State],  1  ) EVALUATE  __DS0BodyLimited ORDER BY  'Fact Equipment Rental'[Contract Number],  'Fact Equipment Rental'[Invoice Number],  'Fact Equipment Rental'[ContractType],  'Fact Equipment Rental'[ContractStatus],  'Fact Equipment Rental'[TransactionType],  'Invoice Date'[Invoice Date],  'Equipment'[Unit Number],  'Fact Equipment Rental'[RateBasis],  'Fact Equipment Rental'[RateType],  'Fact Equipment Rental'[Shift],  'Fact Equipment Rental'[ItemType],  'Fact Equipment Rental'[Description],  'Equipment'[Current Department],  'Fact Equipment Rental'[Rental Start Date],  'Fact Equipment Rental'[Rental End Date],  'Return Date'[Return Date],  'Fact Equipment Rental'[ReturnStatus],  'Equipment'[Current Branch Name],  'Invoice From Date'[Invoice From Date],  'Invoice To Date'[Invoice To Date],  'Invoice To Customer'[Customer Name],  'Invoice To Customer'[Customer Code],  'External Sales Rep'[EmployeeNumber],  'External Sales Rep'[EmployeeName],  'Internal Sales Rep'[EmployeeNumber],  'Internal Sales Rep'[EmployeeName],  'Equipment'[Product Category],  'Equipment'[Rental Category],  'Job Site Address'[PostalZipCode],  'Job Site Address'[Address1],  'Job Site Address'[City],  'Job Site Address'[Country],  'Job Site Address'[State]", "SRP": "DEFINE  VAR __DS0Core =   SUMMARIZE(  'External Sales Rep',  'External Sales Rep'[EmployeeNumber],  'External Sales Rep'[EmployeeName],  'External Sales Rep'[EmployeeDept],  'External Sales Rep'[EmployeeFirstDate],  'External Sales Rep'[EmployeeLastDate]  )  VAR __DS0BodyLimited =   TOPN(  500000,  __DS0Core,  'External Sales Rep'[EmployeeNumber],  1,  'External Sales Rep'[EmployeeName],  1,  'External Sales Rep'[EmployeeDept],  1,  'External Sales Rep'[EmployeeFirstDate],  1,  'External Sales Rep'[EmployeeLastDate],  1  ) EVALUATE  __DS0BodyLimited ORDER BY  'External Sales Rep'[EmployeeNumber],  'External Sales Rep'[EmployeeName],  'External Sales Rep'[EmployeeDept],  'External Sales Rep'[EmployeeFirstDate],  'External Sales Rep'[EmployeeLastDate]"}}]}