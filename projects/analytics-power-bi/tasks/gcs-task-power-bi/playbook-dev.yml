- name: Copy files from GCS to GFS04
  hosts: gfs04_server
  connection: winrm
  tasks:
    - name: Copy Archive files to GFS04-DEV
      win_shell: |
        gsutil -m rsync -u -r gs://{{BUCKET}}/analytics-powerbi/analytics/inbound D:\FTPArchive\Clients\Inbound;
        gsutil -m rsync -u -r gs://{{BUCKET}}/analytics-powerbi/analytics/inbound D:\Processing\Sales\Inbound;
        gsutil -m rsync -u -r gs://{{BUCKET}}/analytics-powerbi/analytics/inbound D:\IETL\Inbound;
    - name: Copy Inbound files to GFS04-DEV
      win_shell: |
        gsutil -m rsync -u -r gs://{{BUCKET}}/analytics-powerbi/analytics/inbound D:\Processing\Analytics\Inbound;
        gsutil -m rsync -u -r gs://{{BUCKET}}/analytics-powerbi/analytics/inbound D:\FTPArchive\Clients\Inbound\Sales;
    - name: Delete Inbound files from GCS
      win_shell: |
        gsutil -m rm -r gs://{{BUCKET}}/analytics-powerbi/analytics/inbound;
