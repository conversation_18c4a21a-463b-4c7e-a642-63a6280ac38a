set -eux

# Determine playbook based on $ENV
if [ "$ENV" = "prod" ]; then
  PLAYBOOK="playbook.yml"
else
  PLAYBOOK="playbook-dev.yml"
fi

gcloud config set project $PROJECT
export HOST_IP=$(gcloud compute instances describe $INSTANCE --zone us-central1-b --format='get(networkInterfaces[0].networkIP)')
echo IP:$HOST_IP

cat << EOF > inventory.yml
---
all:
  children:
    gfs04_server:
      hosts:
        $HOST_IP
  vars:
    ansible_user: $WINDOWS_USERNAME
    ansible_password: "$WINDOWS_PASSWORD"
    ansible_winrm_transport: ntlm
    ansible_winrm_server_cert_validation: ignore
    BUCKET: $BUCKET
EOF

ansible-playbook -v -i inventory.yml "$PLAYBOOK"
