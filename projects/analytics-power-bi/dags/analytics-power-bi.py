import os
from datetime import timedelta, datetime

from airflow import DAG
from airflow.kubernetes.secret import Secret
from airflow.models import Variable
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from pager_duty_plugin import PagerDutyOperator
from shared_libs.default_args import get_default_args
from shared_libs.image_versions import get_full_image_name, get_gcr_registry 
from shared_libs.kubernetes import (
    standard_tolerations,
    get_image_pull_policy,
)
from shared_libs.slack_callback import task_fail_slack_alert
from kubernetes.client import models as k8s

environment = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
gcr_registry = get_gcr_registry(environment)
image_pull_policy = get_image_pull_policy(environment)
pagerduty_key = Variable.get(f"analytics_eemphasys_pagerduty_api_key")
namespace = "analytics-powerbi"
service_account_name = namespace
resources = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1Gi"},
)

cluster_zone = "us-central1"
if environment != "prod":
    project_name = "management-dev-d6ba4d"
    secret_name = "analytics-powerbi-service-dev"
    schedule_interval = None
    start_date = datetime(2019, 7, 24)
    bucket_id = "management-dev-d6ba4d-staging"
    cluster_name = "airflow-jobs-private-dev"
    gcp_project = "vms-dev-39fe7a"
    gfs04_instance = "gfs04-dev"
    windows_secret = "windows-account-vms-dev"
    api_keys_secret = "analytics-powerbi-api-keys"
else:
    project_name = "management-prod-837a97"
    secret_name = "analytics-powerbi-service-prod"
    schedule_interval = "0 22 * * *"
    start_date = datetime(2019, 7, 24)
    bucket_id = "management-prod-837a97-staging"
    cluster_name = "airflow-jobs-private-prod"
    gcp_project = "rouse-gcp-migration-prod"
    gfs04_instance = "gfs04"
    windows_secret = "platform-winrm-prod"
    api_keys_secret = "analytics-powerbi-api-keys"


custom_args = {
    "start_date": start_date,
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": task_fail_slack_alert,
    "startup_timeout_seconds": 300,
    "namespace": namespace,
    "service_account_name": service_account_name,
    "tolerations": standard_tolerations,
    "affinity": {},
    "owner": "<EMAIL>,<EMAIL>",
}

default_args = get_default_args(custom_args)

dag = DAG(
    "analytics_powerbi_dag",
    default_args=default_args,
    description="Analytics PowerBI Scheduled Tasks",
    schedule_interval=schedule_interval,
    catchup=False,
    dagrun_timeout=timedelta(minutes=180),
)

sharded_instance_username = Secret(
    deploy_type="env",
    deploy_target="WINDOWS_USERNAME",
    secret=windows_secret,
    key="username",
)

sharded_instance_password = Secret(
    deploy_type="env",
    deploy_target="WINDOWS_PASSWORD",
    secret=windows_secret,
    key="password",
)

api_keys_json = Secret(
    deploy_type="env",
    deploy_target="API_KEYS",
    secret=api_keys_secret,
    key="api_keys",
)

client_list = ["FRCD", "NEWM"]
task_list = []

for client_name in client_list:
    t1 = GKEStartPodOperator(
        task_id=f"analytics-powerbi-{client_name}",
        name=f"analytics-powerbi-{client_name}",
        location=cluster_zone,
        project_id=project_name,
        cluster_name=cluster_name,
        image=get_full_image_name("analytics-power-bi", gcr_registry),
        dag=dag,
        resources=resources,
        secrets=[api_keys_json],
        arguments=["bin/sh", "task.sh"],
        env_vars={
            "BUCKET_ID": bucket_id,
            "CLIENT_NAME": client_name
        },
        depends_on_past=False,
    )
    task_list.append(t1)

t2 = GKEStartPodOperator(
    task_id="gcs-to-gfs04-powerbi",
    name="gcs-to-gfs04-powerbi",
    location=cluster_zone,
    project_id=project_name,
    cluster_name=cluster_name,
    image=get_full_image_name("gcs-task-power-bi", gcr_registry),
    dag=dag,
    resources=resources,
    trigger_rule="all_done",
    secrets=[sharded_instance_username, sharded_instance_password],
    arguments=["bash", "gcs_task.sh"],
    env_vars={
        "BUCKET": bucket_id,
        "PROJECT": gcp_project,
        "INSTANCE": gfs04_instance,
        "ENV": environment
    },
    depends_on_past=False,
)

if environment == "prod1":
    notify_pager_duty = PagerDutyOperator(
        task_id=f"notify-pager-duty-analytics_powerbi",
        dag=dag,
        resources=resources,
        retries=3,
        api_key=pagerduty_key,
        event_action="trigger",
        payload={
            "summary": ("Data transfer: PowerBI failed."),
            "source": "analytics_powerbi_dag",
            "severity": "critical",
        },
        trigger_rule="one_failed",
    )
    t2 >> notify_pager_duty
    task_list >> notify_pager_duty


task_list >> t2
