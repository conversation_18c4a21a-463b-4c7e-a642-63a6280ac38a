# BigQuery QA

bq_qa_unique_request_id_query = """
WITH
  get_dupes AS (
    SELECT
      lookup_values_activity_id,
      COUNT(*) _count
    FROM
      `{CLIENT_PROJECT_ID}.sales_values_lookup_reporting_version{DATASET_ENV_SUFFIX}.lookup_values_activity_fm_{VERSION_ID}`
    GROUP BY
      lookup_values_activity_id
)
SELECT
  COUNT(*) count
FROM
  get_dupes
WHERE
  _count > 1
"""

bq_qa_unique_request_per_day_query = """
WITH
  get_dupes AS (
    SELECT
      ROW_NUMBER() OVER (
        PARTITION BY
          user_id,
          DATE(lookup_date),
          category_id,
          subcategory_id,
          make_id,
          model_id,
          model_year,
          country
        ORDER BY
          lookup_date DESC) AS row_num
    FROM
      `{CLIENT_PROJECT_ID}.sales_values_lookup_reporting_version{DATASET_ENV_SUFFIX}.lookup_values_activity_fm_{VERSION_ID}`
    WHERE
      user_id IS NOT NULL
      AND category_id IS NOT NULL
      AND subcategory_id IS NOT NULL
      AND make_id IS NOT NULL
      AND model_id IS NOT NULL
    QUALIFY
      row_num > 1
)
SELECT
  COUNT(*) count
FROM
  get_dupes
"""

bq_qa_unique_user_id_query = """
WITH
  get_dupes AS (
    SELECT
      user_id,
      COUNT(DISTINCT display_user_name) count_distinct
    FROM
      `{CLIENT_PROJECT_ID}.sales_values_lookup_reporting_version{DATASET_ENV_SUFFIX}.lookup_values_activity_fm_{VERSION_ID}`
    WHERE
      user_id IS NOT NULL
    GROUP BY
      user_id
)
SELECT
  COUNT(*) count
FROM
  get_dupes
WHERE
  count_distinct > 1
"""

bq_qa_unique_client_code_query = """
WITH
  get_dupes AS (
    SELECT
      fleet_customer_id,
      COUNT(DISTINCT client_code) count_distinct
    FROM
      `{CLIENT_PROJECT_ID}.sales_values_lookup_reporting_version{DATASET_ENV_SUFFIX}.lookup_values_activity_fm_{VERSION_ID}`
    WHERE
      fleet_customer_id IS NOT NULL
    GROUP BY
      fleet_customer_id
)
SELECT
  COUNT(*) count
FROM
  get_dupes
WHERE
  count_distinct > 1
"""

# Fail if true
bq_qa_queries = [
    (
        bq_qa_unique_request_id_query,
        "gt",
        1,
        "Version table contains duplicated request_ids",
    ),
    (
        bq_qa_unique_request_per_day_query,
        "gt",
        0,
        "Duplicated transactions have been found. Review unique transaction per day logic.",
    ),
    (
        bq_qa_unique_user_id_query,
        "gt",
        0,
        "At least one duplicated user_id has been found with multiple display_user_name",
    ),
    (
        bq_qa_unique_client_code_query,
        "gt",
        0,
        "At least one duplicated fleet_customer_id has been found with multiple client_code",
    ),
]
