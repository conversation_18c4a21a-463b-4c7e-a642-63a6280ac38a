DO
$$$$
BEGIN
IF
  EXISTS(
    SELECT
      schema_name
    FROM
      information_schema.schemata
    WHERE
      schema_name = 'rfm_{CLIENT_CODE}'
    ) THEN
  -- Update wrong previous_request_id
  IF (
    SELECT
      COUNT(*)
    FROM
      rfm_{CLIENT_CODE}.values_lookup_transactions a
    LEFT OUTER JOIN
      rfm_{CLIENT_CODE}.values_lookup_transactions b
    ON
      a.previous_request_id = b.request_id
    WHERE
      a.valuation_scid!=b.valuation_scid
      OR a.valuation_model_year!=b.valuation_model_year
      OR a.valuation_country != b.valuation_country
      ) > 0 THEN
    WITH
      bad_requests AS(
      SELECT
        a.id,
        a.user_name,
        a.request_id,
        a.previous_request_id,
        a.valuation_scid,
        a.valuation_model_year,
        a.valuation_country,
        a.lookup_date_utc
      FROM
        rfm_{CLIENT_CODE}.values_lookup_transactions a
      LEFT OUTER JOIN
        rfm_{CLIENT_CODE}.values_lookup_transactions b
      ON
        a.previous_request_id = b.request_id
      WHERE
        a.valuation_scid!=b.valuation_scid
        OR a.valuation_model_year!=b.valuation_model_year
        OR a.valuation_country != b.valuation_country
        ),
      cte AS (
      SELECT
        a.id cte_id,
        CASE
          WHEN a.request_id=b.request_id THEN 'bad'
        ELSE
          NULL
        END
          bad,
        a.user_name,
        a.request_id,
        a.previous_request_id cte_previous_request_id,
        LAG(a.request_id) OVER(PARTITION BY a.user_name, a.valuation_scid, a.valuation_model_year, a.valuation_country, a.lookup_date_utc::DATE ORDER BY a.id) correct_id,
        a.valuation_scid,
        a.valuation_model_year,
        a.valuation_country,
        a.lookup_date_utc
      FROM
        rfm_{CLIENT_CODE}.values_lookup_transactions a
      JOIN
        bad_requests b
      ON
        a.valuation_scid =b.valuation_scid
        AND a.valuation_model_year =b.valuation_model_year
        AND a.user_name=b.user_name
        AND a.valuation_country =b.valuation_country
      ORDER BY
        a.lookup_date_utc ASC,
        a.id ASC )
    UPDATE
      rfm_{CLIENT_CODE}.values_lookup_transactions
    SET
      previous_request_id = cte.correct_id
    FROM
      cte
    WHERE
      id = cte.cte_id
      AND COALESCE(cte.cte_previous_request_id,'')!=COALESCE(cte.correct_id,'') ;
  END IF;
  -- Update distinct values for valuation_country and valuation_market
  UPDATE
    rfm_{CLIENT_CODE}.values_lookup_transactions
  SET
    valuation_country = CASE
      WHEN LOWER(valuation_country) IN ('canada', 'ca') THEN 'CAN'
      WHEN LOWER(valuation_country) IN ('united states',
      'us') THEN 'USA'
      WHEN LOWER(valuation_country) IN ('united kingdom', 'uk') THEN 'GBR'
      WHEN LOWER(valuation_country) IN ('france') THEN 'FRA'
      ELSE valuation_country
    END,
    valuation_market = CASE
      WHEN LOWER(valuation_market) IN ('canada', 'ca') THEN 'CAN'
      WHEN LOWER(valuation_market) IN ('united states',
      'us') THEN 'USA'
      WHEN LOWER(valuation_market) IN ('united kingdom', 'uk') THEN 'GBR'
      WHEN LOWER(valuation_market) IN ('france') THEN 'FRA'
      ELSE valuation_market
    END
  WHERE
    valuation_country != CASE
      WHEN LOWER(valuation_country) IN ('canada', 'ca') THEN 'CAN'
      WHEN LOWER(valuation_country) IN ('united states',
      'us') THEN 'USA'
      WHEN LOWER(valuation_country) IN ('united kingdom', 'uk') THEN 'GBR'
      WHEN LOWER(valuation_country) IN ('france') THEN 'FRA'
      ELSE valuation_country
    END
    OR valuation_market != CASE
      WHEN LOWER(valuation_market) IN ('canada', 'ca') THEN 'CAN'
      WHEN LOWER(valuation_market) IN ('united states',
      'us') THEN 'USA'
      WHEN LOWER(valuation_market) IN ('united kingdom', 'uk') THEN 'GBR'
      WHEN LOWER(valuation_market) IN ('france') THEN 'FRA'
      ELSE valuation_market
    END
  ;
END IF;
END;
$$$$