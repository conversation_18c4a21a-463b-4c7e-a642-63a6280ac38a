SET ROLE ROOT;
CREATE OR REPLACE VIEW
  rfm_{CLIENT_CODE}.lookup_values_activity_fm
AS
WITH
  group_lookups AS (
  SELECT
    a.request_id lookup_values_activity_id,
    a.client_id,
    a.fleet_customer_id,
    a.client_code client_code,
    a.lookup_date_utc lookup_date,
    DATE(DATE_TRUNC('month', a.lookup_date_utc)) billing_month,
    a.valuation_make_id make_id,
    a.valuation_make make_name,
    a.valuation_model_id model_id,
    a.valuation_model model_name,
    a.valuation_category_id category_id,
    a.valuation_category category_name,
    a.valuation_subcategory_id subcategory_id,
    a.valuation_subcategory subcategory_name,
    a.valuation_model_year model_year,
    COALESCE(c.country_code_ext, a.valuation_country) country,
    c.country_name,
    a.is_single_subcategory,
    a.user_name,
    COALESCE(a.display_user_name, a.full_name) display_user_name,
    a.user_id,
    a.lookup_values_tier,
    a.meter_adjustment_status,
    ROW_NUMBER() OVER (PARTITION BY COALESCE(a.user_id::VARCHAR, a.user_name), DATE(a.lookup_date_utc),
      a.valuation_scid,
      a.valuation_model_year,
      COALESCE(c.country_code_ext, a.valuation_country)
    ORDER BY
      a.lookup_date_utc DESC) AS row_num
  FROM
    rfm_{CLIENT_CODE}.values_lookup_transactions a
  LEFT OUTER JOIN
    rfm_global.country c
  ON
    LOWER(CASE
      WHEN LOWER(a.valuation_country) IN ('canada', 'ca') THEN 'CAN'
      WHEN LOWER(a.valuation_country) IN ('united states', 'us') THEN 'USA'
      WHEN LOWER(a.valuation_country) IN ('united kingdom', 'uk') THEN 'GBR'
      WHEN LOWER(a.valuation_country) IN ('france') THEN 'FRA'
      ELSE
        a.valuation_country
    END) = LOWER(c.country_code_ext)
  WHERE
    lookup_date_utc >= '{VERSION_ID_DATE}'
  )
SELECT
  a.lookup_values_activity_id,
  a.client_id,
  a.fleet_customer_id,
  a.client_code,
  a.lookup_date,
  a.billing_month,
  a.make_id,
  a.make_name,
  a.model_id,
  a.model_name,
  a.category_id,
  a.category_name,
  a.subcategory_id,
  a.subcategory_name,
  a.model_year,
  a.make_name || ' ' || a.model_name make_model_name,
  a.country,
  a.country_name,
  a.is_single_subcategory,
  TRIM(COALESCE(a.display_user_name, REPLACE(a.user_name, 'auth0|', ''))) display_user_name,
  a.user_id,
  a.lookup_values_tier,
  a.meter_adjustment_status,
  it.insights_type,
  it.insights_type_id,
  it.insights_category,
  it.insights_category_id,
  it.insights_subcategory,
  it.insights_subcategory_id,
  it.insights_localization
FROM
  group_lookups a
LEFT OUTER JOIN
  rfm_global.insights_taxonomy it
ON
  a.subcategory_id = it.insights_subcategory_id
WHERE
  row_num = 1
  AND a.category_name IS NOT NULL
  AND a.subcategory_name IS NOT NULL
  AND a.make_name IS NOT NULL
  AND a.model_name IS NOT NULL
  AND LOWER(COALESCE(a.display_user_name, a.user_name)) NOT LIKE '%@rouse%'
  AND LOWER(COALESCE(a.display_user_name, a.user_name)) NOT LIKE '%|test%'
  AND LOWER(COALESCE(a.display_user_name, a.user_name)) NOT LIKE 'test%'
  AND NOT (
    a.display_user_name IS NULL
    AND LOWER(a.user_name) LIKE 'waad|%'
  )
  AND LOWER(REPLACE(a.user_name, 'auth0|', '')) NOT LIKE '%@rouse%'
  AND LOWER(REPLACE(a.user_name, 'auth0|', '')) NOT LIKE 'test%'
  AND NOT (
    LOWER(a.user_name) LIKE 'auth0|%'
    AND LOWER(a.user_name) NOT LIKE '%@%'
  )
UNION
SELECT
  *
FROM
  rfm_{CLIENT_CODE}_version.lookup_values_activity_fm_{VERSION_ID}
;
--views
GRANT SELECT ON TABLE rfm_{CLIENT_CODE}.lookup_values_activity_fm TO sales_values_lookup_reporting_r;
GRANT SELECT,INSERT,UPDATE,DELETE ON TABLE rfm_{CLIENT_CODE}.lookup_values_activity_fm TO sales_values_lookup_reporting_rw;
-- tables
GRANT SELECT ON TABLE rfm_{CLIENT_CODE}_version.lookup_values_activity_fm_{VERSION_ID} TO sales_values_lookup_reporting_r;
GRANT SELECT,INSERT,UPDATE,DELETE ON TABLE rfm_{CLIENT_CODE}_version.lookup_values_activity_fm_{VERSION_ID} TO sales_values_lookup_reporting_rw;

CREATE INDEX IF NOT EXISTS
  rfm_{CLIENT_CODE}_lookup_activity_fm_{VERSION_ID}_billing_month
ON
  rfm_{CLIENT_CODE}_version.lookup_values_activity_fm_{VERSION_ID}
USING BTREE (client_id, billing_month)
;