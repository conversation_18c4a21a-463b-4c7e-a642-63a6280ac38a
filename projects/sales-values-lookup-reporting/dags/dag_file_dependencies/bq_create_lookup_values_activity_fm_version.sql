CREATE OR REPLACE TABLE
  `{CLIENT_PROJECT_ID}.sales_values_lookup_reporting_version{DATASET_ENV_SUFFIX}.lookup_values_activity_fm_{VERSION_ID}`
OPTIONS(expiration_timestamp=TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL 30 DAY))
AS
WITH
  fleet_users AS (
    SELECT
      fleet_customer_id,
      identity_user_id,
      user_name,
      email,
      first_name,
      last_name
    FROM
      `{SALES_PROJECT_ID}.sales_rfm_user_config{DATASET_ENV_SUFFIX}.fleet_users`
    WHERE
      identity_user_id IS NOT NULL
    QUALIFY
      ROW_NUMBER() OVER (PARTITION BY identity_user_id ORDER BY fleet_user_id DESC) = 1
  ), group_lookups_single_tenant AS (
  SELECT
    a.request_id lookup_values_activity_id,
    a.client_id,
    a.fleet_customer_id,
    a.client_code client_code,
    a.lookup_date_utc lookup_date,
    DATE(DATE_TRUNC(a.lookup_date_utc, MONTH)) billing_month,
    a.valuation_make_id make_id,
    a.valuation_make make_name,
    a.valuation_model_id model_id,
    a.valuation_model model_name,
    a.valuation_category_id category_id,
    a.valuation_category category_name,
    a.valuation_subcategory_id subcategory_id,
    a.valuation_subcategory subcategory_name,
    a.valuation_model_year model_year,
    COALESCE(c.country_code_ext, a.valuation_country) country,
    c.country country_name,
    a.is_single_subcategory,
    a.user_name,
    COALESCE(a.display_user_name, a.full_name) display_user_name,
    a.user_id,
    a.lookup_values_tier,
    a.meter_adjustment_status,
    ROW_NUMBER() OVER (PARTITION BY COALESCE(CAST(a.user_id AS STRING), a.user_name), DATE(a.lookup_date_utc),
      a.valuation_scid,
      a.valuation_model_year,
      COALESCE(c.country_code_ext, a.valuation_country)
    ORDER BY
      a.lookup_date_utc DESC) AS row_num
  FROM
    `{CLIENT_PROJECT_ID}.sales_values_lookup_reporting{DATASET_ENV_SUFFIX}.values_lookup` a
  LEFT OUTER JOIN
    `{APPRAISALS_PROJECT_ID}.ss_export{DATASET_ENV_SUFFIX}.ras_sas_country` c
  ON
    LOWER(CASE
      WHEN LOWER(a.valuation_country) IN ('canada', 'ca') THEN 'CAN'
      WHEN LOWER(a.valuation_country) IN ('united states', 'us') THEN 'USA'
      WHEN LOWER(a.valuation_country) IN ('united kingdom', 'uk') THEN 'GBR'
      WHEN LOWER(a.valuation_country) IN ('france') THEN 'FRA'
      ELSE
        a.valuation_country
    END) = LOWER(c.country_code_ext)
  WHERE
    a.lookup_date_utc < '{VERSION_ID_DATE}'
  ),
  group_lookups_multi_tenant AS (
  SELECT
    a.request_id lookup_values_activity_id,
    a.client_id,
    a.fleet_customer_id,
    a.client_code client_code,
    a.lookup_date_utc lookup_date,
    DATE(DATE_TRUNC(a.lookup_date_utc, MONTH)) billing_month,
    a.valuation_make_id make_id,
    a.valuation_make make_name,
    a.valuation_model_id model_id,
    a.valuation_model model_name,
    a.valuation_category_id category_id,
    a.valuation_category category_name,
    a.valuation_subcategory_id subcategory_id,
    a.valuation_subcategory subcategory_name,
    a.valuation_model_year model_year,
    COALESCE(c.country_code_ext, a.valuation_country) country,
    c.country country_name,
    a.is_single_subcategory,
    a.user_name,
    COALESCE(a.display_user_name, a.full_name) display_user_name,
    a.user_id,
    a.lookup_values_tier,
    a.meter_adjustment_status,
    ROW_NUMBER() OVER (PARTITION BY COALESCE(CAST(a.user_id AS STRING), a.user_name), DATE(a.lookup_date_utc),
      a.valuation_scid,
      a.valuation_model_year,
      COALESCE(c.country_code_ext, a.valuation_country)
    ORDER BY
      a.lookup_date_utc DESC) AS row_num
  FROM
    `{RFM_PROJECT_ID}.sales_values_lookup_reporting{DATASET_ENV_SUFFIX}.values_lookup` a
  LEFT OUTER JOIN
    `{APPRAISALS_PROJECT_ID}.ss_export{DATASET_ENV_SUFFIX}.ras_sas_country` c
  ON
    LOWER(CASE
      WHEN LOWER(a.valuation_country) IN ('canada', 'ca') THEN 'CAN'
      WHEN LOWER(a.valuation_country) IN ('united states', 'us') THEN 'USA'
      WHEN LOWER(a.valuation_country) IN ('united kingdom', 'uk') THEN 'GBR'
      WHEN LOWER(a.valuation_country) IN ('france') THEN 'FRA'
      ELSE
        a.valuation_country
    END) = LOWER(c.country_code_ext)
  WHERE
    a.lookup_date_utc < '{VERSION_ID_DATE}'
    AND a.fleet_customer_id = {FLEET_CUSTOMER_ID} 
    AND a.client_code = '{CLIENT_CODE}'
  ),
group_lookups as (
  SELECT * FROM group_lookups_single_tenant
  UNION ALL
  SELECT * FROM group_lookups_multi_tenant
)
SELECT
  a.lookup_values_activity_id,
  a.client_id,
  a.fleet_customer_id,
  a.client_code,
  a.lookup_date,
  a.billing_month,
  a.make_id,
  a.make_name,
  a.model_id,
  a.model_name,
  a.category_id,
  a.category_name,
  a.subcategory_id,
  a.subcategory_name,
  a.model_year,
  a.make_name || ' ' || a.model_name make_model_name,
  a.country,
  a.country_name,
  a.is_single_subcategory,
  TRIM(COALESCE(NULLIF(up.user_profile_name,''), NULLIF(NULLIF(TRIM(fu.first_name), 'None') || ' ' || NULLIF(TRIM(fu.last_name),'None'), ' '), up.identity_user_name, NULLIF(up.user_profile_email,''), fu.email, a.display_user_name, REPLACE(a.user_name, 'auth0|', ''))) display_user_name,
  a.user_id,
  a.lookup_values_tier,
  a.meter_adjustment_status,
  it.insights_type,
  it.insights_type_id,
  it.insights_category,
  it.insights_category_id,
  it.insights_subcategory,
  it.insights_subcategory_id,
  it.insights_localization
FROM
  group_lookups a
LEFT OUTER JOIN
  fleet_users fu
ON
  a.user_id = fu.identity_user_id
  AND a.fleet_customer_id = fu.fleet_customer_id
LEFT OUTER JOIN
  `{SALES_PROJECT_ID}.sales_rfm_user_config{DATASET_ENV_SUFFIX}.user_profiles` up
ON
  a.user_id = up.identity_user_id
LEFT OUTER JOIN
  `{SALES_PROJECT_ID}.fleet_manager_insights_taxonomy_and_localization{DATASET_ENV_SUFFIX}.insights_taxonomy` it
ON
  a.subcategory_id = it.insights_subcategory_id
WHERE
  row_num = 1
  AND a.category_name IS NOT NULL
  AND a.subcategory_name IS NOT NULL
  AND a.make_name IS NOT NULL
  AND a.model_name IS NOT NULL
  AND LOWER(COALESCE(up.identity_user_name, NULLIF(up.user_profile_email,''), a.user_name)) NOT LIKE '%@rouse%'
  AND LOWER(COALESCE(up.identity_user_name, NULLIF(up.user_profile_email,''), a.user_name)) NOT LIKE '%|test%'
  AND LOWER(COALESCE(up.identity_user_name, NULLIF(up.user_profile_email,''), a.user_name)) NOT LIKE 'test%'
  AND COALESCE(LOWER(fu.user_name), '') NOT LIKE '%@rouse%'
  AND COALESCE(LOWER(fu.user_name), '') NOT LIKE 'test%'
  AND COALESCE(LOWER(fu.email), '') NOT LIKE '%@rouse%'
  AND COALESCE(LOWER(fu.email), '') NOT LIKE 'test%'
  AND COALESCE(LOWER(a.display_user_name), '') NOT LIKE '%@rouse%'
  AND COALESCE(LOWER(a.display_user_name), '') NOT LIKE 'test%'
  AND LOWER(REPLACE(a.user_name, 'auth0|', '')) NOT LIKE '%@rouse%'
  AND LOWER(REPLACE(a.user_name, 'auth0|', '')) NOT LIKE 'test%'
;