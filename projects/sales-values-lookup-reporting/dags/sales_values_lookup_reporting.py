import ast
import collections
import datetime as dt
import json
import os
import pendulum
import importlib

from airflow import DAG
from airflow.kubernetes import secret
from airflow.models import Variable

from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.sensors.external_task_sensor import ExternalTaskSensor
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from sharding_constants import sharded_clients
from client_projects_constants import client_mappings
from shared_libs.bigquery_dataset import refresh_bq_dataset_list, CreateBigQueryDataset

from shared_libs.default_args import get_default_args, get_ssl_pgbouncer_secrets
from shared_libs.image_versions import get_full_image_name, get_gcr_registry
from shared_libs.kubernetes import get_image_pull_policy, get_resource_size
from shared_libs.slack_callback import task_fail_slack_alert
from shared_libs.schedule import generate_cron_schedule
from postgres_config import pg_db_shard_servers
from fleet_manager_sharding_constants import sharded_clients_by_fleet_customer_id
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert
from pathlib import Path

environment = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
gcr_registry = get_gcr_registry(environment)
image_pull_policy = get_image_pull_policy(environment)

# global environment settings
cluster_zone = "us-central1-b"

version = "{{ logical_date.strftime('%Y%m%d%H%M%S') }}"
version_date = "{{ logical_date.strftime('%Y-%m-%d') }}"
local_tz = pendulum.timezone("America/Los_Angeles")
table_retention_days = "{{ var.value.rfm01_pg_retention_days }}"

namespace = "sales-values-lookup"
dag_prefix = "sales-values-lookup"
service_account_name = namespace

default_server_name = "pg_bouncer"
engine_config = Variable.get("postgres_engine_configuration", {}, deserialize_json=True)
server_name = engine_config.get("server", default_server_name)


if environment != "prod":
    project_name = "management-dev-d6ba4d"
    cluster_name = "composer-jobs-v3-dev"
    db_secret_name = "de-sales-values-lookup-dev"
    pool_name = "sales-values-lookup"
    pool_name_pg = "sales-values-lookup-pg"
    pool_name_bq = "sales-values-lookup-bq"
    start_date = dt.datetime(2021, 9, 15, tzinfo=local_tz)
    env_sufix = "_dev"
    appraisals_project_id = "appraisals-data-dev-c55fa4"
    sales_data_project_id = "sales-data-dev-9ffb6c"
    rfm_project_id = "rfm-dev-1e0721"

else:  # PRODUCTION
    project_name = "management-prod-837a97"
    cluster_name = "composer-jobs-v3-prod"
    db_secret_name = "de-sales-values-lookup-prod"
    pool_name = "sales-values-lookup"
    pool_name_pg = "sales-values-lookup-pg"
    pool_name_bq = "sales-values-lookup-bq"
    start_date = dt.datetime(2021, 9, 15, tzinfo=local_tz)
    env_sufix = ""
    appraisals_project_id = "appraisals-data-prod-707493"
    sales_data_project_id = "sales-data-prod-2b1264"
    rfm_project_id = "rfm-prod-5f68c8"


bq_qa_queries = importlib.import_module(
    ("sales-values-lookup-reporting." if environment != "local" else "")
    + "dag_file_dependencies.bq_qa_queries"
).bq_qa_queries

try:
    clients_to_exclude = ast.literal_eval(
        Variable.get("sales_values_lookup_reporting_clients_to_exclude", "[]")
    )
except ValueError as err:
    print("Clients were not set, skipped", err)
    clients_to_exclude = []


try:
    clients_to_ignore_previous_request_validation = ast.literal_eval(
        Variable.get(
            "sales_values_lookup_reporting_clients_to_ignore_previous_request_validation",
            "[]",
        )
    )
except ValueError as err:
    print("Clients were not set, skipped", err)
    clients_to_ignore_previous_request_validation = []

# client list
Client = collections.namedtuple(
    "Client",
    [
        "sales_code",
        "sales_client_id",
        "fleet_customer_id",
        "client_version",
        "ras_code",
        "host_name",
        "ims_conversion_status",
        "pg_db_server_name",
    ],
)

dict_shard_servers = {
    item["client_code"]: item["shard_server"] for item in sharded_clients
}

all_sales_active_clients = [
    Client(
        sales_code=value["sales_client_code"],
        sales_client_id=value["sales_client_id"],
        fleet_customer_id=str(value.get("fleet_customer_id", "null")),
        client_version=f'{value.get("client_version", "1")}',
        ras_code=ras_code,
        host_name=dict_shard_servers.get(ras_code, ""),
        ims_conversion_status=value.get("ims_conversion_status", "legacy"),
        pg_db_server_name=(
            sharded_clients_by_fleet_customer_id.get(
                str(value.get("fleet_customer_id", "null")), {}
            ).get("shard", "rfm02")
            if server_name == default_server_name
            else server_name
        ),
    )
    for ras_code, value in client_mappings.items()
    if "sales_client_code" in value
    and "sales_client_id" in value
    and ras_code not in clients_to_exclude
]

# default dag args
custom_args = {
    "start_date": start_date,
    "retries": 3,
    "retry_delay": dt.timedelta(minutes=5),
    "project_id": project_name,
    "startup_timeout_seconds": 600,
    "namespace": namespace,
    "service_account_name": service_account_name,
    "image": get_full_image_name("sales-values-lookup-reporting", gcr_registry),
    "image_pull_policy": image_pull_policy,
    "owner": "<EMAIL>",
}
default_args = get_default_args(custom_args)


# secrets/passwords
secret_pg_db_username = secret.Secret(
    deploy_type="env", deploy_target="PG_USER", secret=db_secret_name, key="username"
)

secret_pg_db_password = secret.Secret(
    deploy_type="env",
    deploy_target="PG_PASSWORD",
    secret=db_secret_name,
    key="password",
)

secrets_pg_db = [
    secret_pg_db_username,
    secret_pg_db_password,
] + get_ssl_pgbouncer_secrets(environment)

PWD = Path(os.path.dirname(os.path.realpath(__file__)))
SQL_FOLDER = os.path.join(PWD, "dag_file_dependencies")


def read_file(dependency_path):
    with open(os.path.join(SQL_FOLDER, dependency_path)) as f:
        local_file = f.read()
    return local_file


def get_query(query_file, **kwargs):
    query = read_file(query_file)
    query = query.format(**kwargs)
    return query


delete_rbme_test_account_records = get_query("delete_rbme_test_account_records.sql")
column_list = get_query("columns.sql", ALIAS="")


def trigger(context, dag_run_obj):
    return dag_run_obj


def create_dag(
    dag_id,
    ras_code,
    client_version,
    client_id,
    fleet_customer_id,
    host_name,
    ims_conversion_status,
    pg_db_server,
    use_cert,
    pg_db_server_main,
    pg_db_name,
):
    project_id = f"rs-client-{ras_code}-{client_version}"
    activity_version_table_name = f"lookup_values_activity_fm_{version}"
    variable_dict = {
        "CLIENT_CODE": ras_code,
        "CLIENT_VERSION": client_version,
        "VERSION_ID": version,
        "PG_RETENTION_DAYS": table_retention_days,
        "VERSION_ID_DATE": version_date,
        "DATASET_ENV_SUFFIX": env_sufix,
        "APPRAISALS_PROJECT_ID": appraisals_project_id,
        "CLIENT_PROJECT_ID": f"rs-client-{ras_code}-{client_version}",
        "RFM_PROJECT_ID": rfm_project_id,
        "PROJECT_ID": f"rs-client-{ras_code}-{client_version}",
        "DATASET_STAGE_ID": f"sales_values_lookup_reporting_stage{env_sufix}",
        "REPORT_TABLE_NAME": "values_lookup",
        "BQ_CLUSTER_SCRIPT": "",  # no cluster for client's project
        "SALES_PROJECT_ID": sales_data_project_id,
        "FLEET_CUSTOMER_ID": fleet_customer_id,
    }

    bq_create_empty_lookup_table_script = get_query(
        "bq_create_empty_lookup_table.sql", **variable_dict
    ) + get_query("bq_create_lookup_view.sql", **variable_dict)
    update_wrong_values_script = get_query("update_wrong_values.sql", **variable_dict)

    bq_create_lookup_values_activity_fm_version_script = get_query(
        "bq_create_lookup_values_activity_fm_version.sql", **variable_dict
    )
    pg_create_lookup_values_activity_fm_script = get_query(
        "pg_create_lookup_values_activity_fm.sql", **variable_dict
    )
    pg_create_lookup_values_activity_fm_script = get_query(
        "view_error_handling.sql",
        **{
            "TYPE": "VIEW",
            "ENTITY_TO_DROP": "rfm_{CLIENT_CODE}.lookup_values_activity_fm".format(
                **variable_dict
            ),
            "SUBQUERY": pg_create_lookup_values_activity_fm_script,
        },
    )
    bq_create_lookup_values_activity_fm_view_script = get_query(
        "bq_create_lookup_values_activity_fm_view.sql", **variable_dict
    )

    bq_qa_lookup_values_activity_fm_script = get_query(
        "bq_qa_lookup_values_activity_fm.sql", **variable_dict
    )

    pg_remove_version_tables = get_query(
        "pg_remove_version_tables.sql", **variable_dict
    )

    schedule_interval = (
        generate_cron_schedule(seed=ras_code, min_hour=6, max_hour=23, times_per_day=2)
        if environment == "prod"
        else generate_cron_schedule(seed=ras_code, min_hour=8, max_hour=19)
    )

    with DAG(
        dag_id,
        default_args=default_args,
        description="Export from PostgreSQL Server to BigQuery",
        schedule_interval=schedule_interval,
        catchup=False,
        dagrun_timeout=dt.timedelta(minutes=480),
        max_active_runs=1,
        on_failure_callback=dag_fail_pagerduty_alert(
            "sales_values_lookup_reporting_pagerduty_api_key"
        ),
    ) as dag:
        no_data_found_id = "sales-values-no-data-found"
        no_data_found = DummyOperator(task_id=no_data_found_id, dag=dag)
        update_wrong_values = f"{dag_prefix}-{ras_code}-update-wrong-values"
        update_wrong_values_task = GKEStartPodOperator(
            task_id=update_wrong_values,
            name=update_wrong_values,
            image=get_full_image_name("execute-pg-task", gcr_registry),
            arguments=["python3", "-m" "src.pgsql_cmd_task", "execute-task"],
            resources=get_resource_size("Small"),
            secrets=secrets_pg_db,
            env_vars={
                "DB_NAME": pg_db_name,
                "ENV": environment,
                "PG_QUERY_TASK_SCRIPT": update_wrong_values_script,
                "DB_SERVER": pg_db_server_main,
                "USE_PG_CERT": str(use_cert),
            },
            get_logs=True,
            pool=pool_name_pg,
        )

        pg_to_gcs_id = f"{dag_prefix}-pg2gcs-{ras_code}"
        pg_to_gcs = GKEStartPodOperator(
            task_id=pg_to_gcs_id,
            name=pg_to_gcs_id,
            resources=get_resource_size("Medium"),
            execution_timeout=dt.timedelta(minutes=100),
            do_xcom_push=True,
            arguments=[
                "python3.8",
                "-m",
                "sales_values_lookup_reporting.cli",
                "pg2gcs",
            ],
            secrets=secrets_pg_db,
            env_vars={
                "VERSION": version,
                "ENV": environment,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "DB_SERVER": pg_db_server,
                "USE_PG_CERT": str(use_cert),
                "DB_NAME": pg_db_name,
            },
            get_logs=True,
            pool=pool_name_pg,
            on_failure_callback=task_fail_slack_alert,
        )

        create_refresh_datasets = refresh_bq_dataset_list(
            dag,
            project_id,
            datasets=[
                CreateBigQueryDataset(
                    dataset=f"sales_values_lookup_reporting{env_sufix}",
                    project_id=project_id,
                    table_expiration_days=None,
                ),
                CreateBigQueryDataset(
                    dataset=f"sales_values_lookup_reporting_stage{env_sufix}",
                    project_id=project_id,
                    table_expiration_days=None,
                ),
                CreateBigQueryDataset(
                    dataset=f"sales_values_lookup_reporting_version{env_sufix}",
                    project_id=project_id,
                    table_expiration_days=None,
                ),
            ],
            skip_none_table_expiration_days=True,
        )

        create_bq_table_id = f"{dag_prefix}-{ras_code}-create-bq-table"
        create_bq_table_task = GKEStartPodOperator(
            task_id=create_bq_table_id,
            name=create_bq_table_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            execution_timeout=dt.timedelta(minutes=5),
            get_logs=True,
            arguments=[
                "python3",
                "main.py",
                "execute-bigquery",
                f"--gcp_project_id={project_id}",
            ],
            env_vars={
                "GCP_PROJECT": project_id,
                "CREATE_DATASETS": str(True),
                "QUERY": bq_create_empty_lookup_table_script,
            },
        )

        create_lookup_values_activity_version_id = (
            f"{dag_prefix}-{ras_code}-create-lookup-values-activity-version"
        )
        create_lookup_values_activity_version_task = GKEStartPodOperator(
            task_id=create_lookup_values_activity_version_id,
            name=create_lookup_values_activity_version_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            execution_timeout=dt.timedelta(minutes=5),
            get_logs=True,
            arguments=[
                "python3",
                "main.py",
                "execute-bigquery",
                f"--gcp_project_id={project_id}",
            ],
            env_vars={
                "GCP_PROJECT": project_id,
                "CREATE_DATASETS": str(True),
                "QUERY": bq_create_lookup_values_activity_fm_version_script,
            },
            trigger_rule="none_failed_min_one_success",
        )
        bq_to_pg_lookup_activity_version_id = (
            f"{dag_prefix}-{ras_code}-bq-to-pg-lookup-activity-version"
        )
        bq_to_pg_lookup_activity_version = GKEStartPodOperator(
            task_id=bq_to_pg_lookup_activity_version_id,
            name=bq_to_pg_lookup_activity_version_id,
            dag=dag,
            image=get_full_image_name("bq-to-pg-task", gcr_registry),
            arguments=[
                "python3",
                "-m",
                "src.data_flow_task",
                "bq2pg-wizard",
            ],
            resources=get_resource_size("Medium"),
            secrets=secrets_pg_db,
            do_xcom_push=True,
            get_logs=True,
            env_vars={
                "ENV": environment,
                "VERSION": version,
                "GCP_PROJECT": project_id,
                "DB_SERVER": pg_db_server_main,
                "DB_NAME": pg_db_name,
                "PG_SCHEMA": f"rfm_{ras_code}_version",
                "PG_TABLE": activity_version_table_name,
                "BQ_DATASET": f"sales_values_lookup_reporting_version{env_sufix}",
                "BQ_TABLE": activity_version_table_name,
                "USE_PG_CERT": str(use_cert),
                "PG_FIELD_TYPES": "{'insights_localization': 'JSONB','meter_adjustment_status': 'JSONB'}",
            },
        )

        bq_qa_check_id = f"{dag_prefix}-lookup-activity-bq-qa"
        bq_qa_check = GKEStartPodOperator(
            task_id=bq_qa_check_id,
            name=bq_qa_check_id,
            dag=dag,
            resources=get_resource_size("Small"),
            execution_timeout=dt.timedelta(minutes=30),
            image=get_full_image_name("bq-qa", gcr_registry),
            arguments=["python3", "-m", "src.cli", "qa"],
            env_vars={
                "PROJECT_ID": project_id,
                "BQ_QA_QUERIES": str(bq_qa_queries).format(**variable_dict),
            },
            get_logs=True,
        )

        create_lookup_values_activity_view_id = (
            f"{dag_prefix}-{ras_code}-create-lookup-values-activity-view"
        )
        create_lookup_values_activity_view_task = GKEStartPodOperator(
            task_id=create_lookup_values_activity_view_id,
            name=create_lookup_values_activity_view_id,
            image=get_full_image_name("execute-pg-task", gcr_registry),
            arguments=["python3", "-m" "src.pgsql_cmd_task", "execute-task"],
            resources=get_resource_size("Small"),
            secrets=secrets_pg_db,
            env_vars={
                "DB_NAME": pg_db_name,
                "ENV": environment,
                "PG_QUERY_TASK_SCRIPT": pg_create_lookup_values_activity_fm_script,
                "DB_SERVER": pg_db_server_main,
                "USE_PG_CERT": str(use_cert),
                "POST_LOAD_SCRIPT": pg_remove_version_tables,
            },
            get_logs=True,
            pool=pool_name_pg,
        )

        gcs_to_bq_id = f"{dag_prefix}-gcs2bq-{ras_code}"
        gcs_to_bq = GKEStartPodOperator(
            task_id=gcs_to_bq_id,
            name=gcs_to_bq_id,
            resources=get_resource_size("Medium"),
            execution_timeout=dt.timedelta(minutes=100),
            do_xcom_push=True,
            arguments=[
                "python3.8",
                "-m",
                "sales_values_lookup_reporting.cli",
                "gcs2bq",
            ],
            secrets=secrets_pg_db,
            env_vars={
                "VERSION": version,
                "ENV": environment,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "DB_SERVER": pg_db_server,
                "FLEET_CUSTOMER_ID": fleet_customer_id,
                "APPRAISALS_PROJECT_ID": appraisals_project_id,
                "COLUMN_LIST": column_list,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        def branch_func_num_rows(**kwargs):
            ti = kwargs["ti"]
            pg_to_gcs_id = kwargs["pg_to_gcs_id"]
            no_data_found_id = kwargs["no_data_found_id"]
            gcs_to_bq_id = kwargs["gcs_to_bq_id"]
            num_rows_modified = int(
                json.loads(
                    (ti.xcom_pull(task_ids=pg_to_gcs_id)["metrics"]).replace("'", '"')
                )["num_rows"]
            )
            if num_rows_modified > 0:
                return gcs_to_bq_id
            else:
                return no_data_found_id

        check_number_of_rows = BranchPythonOperator(
            task_id=f"{dag_prefix}-branch-number-of-rows-{ras_code}",
            provide_context=True,
            python_callable=branch_func_num_rows,
            op_kwargs={
                "pg_to_gcs_id": pg_to_gcs_id,
                "no_data_found_id": no_data_found_id,
                "gcs_to_bq_id": gcs_to_bq_id,
            },
        )

        metrics_list = [pg_to_gcs_id]

        qa_check_id = f"{dag_prefix}-qa-{ras_code}"
        qa_check = GKEStartPodOperator(
            task_id=qa_check_id,
            name=qa_check_id,
            resources=get_resource_size("Medium"),
            execution_timeout=dt.timedelta(minutes=30),
            arguments=[
                "python3.8",
                "-m",
                "sales_values_lookup_reporting.cli",
                "qa-source-bq",
            ],
            env_vars={
                "ENV": environment,
                "VERSION": version,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "METRICS_PG": f"{{{{ task_instance.xcom_pull("
                f"task_ids={metrics_list}"
                f") }}}}",
                "METRICS_BQ": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{gcs_to_bq_id}"'
                f')["metrics"] }}}}',
                "IGNORE_PREVIOUS_REQUEST_VALIDATION": str(
                    ras_code in clients_to_ignore_previous_request_validation
                ),
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        publish_bq_id = f"{dag_prefix}-publish-bq-{ras_code}"
        publish_bq = GKEStartPodOperator(
            task_id=publish_bq_id,
            name=publish_bq_id,
            resources=get_resource_size("Medium"),
            execution_timeout=dt.timedelta(minutes=100),
            arguments=[
                "python3.8",
                "-m",
                "sales_values_lookup_reporting.cli",
                "publish-bq",
            ],
            secrets=secrets_pg_db,
            env_vars={
                "VERSION": version,
                "ENV": environment,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "DB_SERVER": pg_db_server,
                "FLEET_CUSTOMER_ID": fleet_customer_id,
            },
            get_logs=True,
            pool=pool_name_bq,
            on_failure_callback=task_fail_slack_alert,
        )

        publish_qa_check_id = f"{dag_prefix}-publish-qa-{ras_code}"
        publish_qa_check = GKEStartPodOperator(
            task_id=publish_qa_check_id,
            name=publish_qa_check_id,
            resources=get_resource_size("Medium"),
            execution_timeout=dt.timedelta(minutes=30),
            arguments=[
                "python3.8",
                "-m",
                "sales_values_lookup_reporting.cli",
                "qa-bq-publish",
            ],
            env_vars={
                "ENV": environment,
                "VERSION": version,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        datafootprint_reduction_task_id = (
            f"{dag_prefix}-{ras_code}-datafootprint-reduction"
        )
        datafootprint_reduction_task = GKEStartPodOperator(
            task_id=datafootprint_reduction_task_id,
            name=datafootprint_reduction_task_id,
            image=get_full_image_name("execute-pg-task", gcr_registry),
            arguments=["python3", "-m" "src.pgsql_cmd_task", "execute-task"],
            resources=get_resource_size("Small"),
            secrets=secrets_pg_db,
            env_vars={
                "DB_NAME": pg_db_name,
                "ENV": environment,
                "PG_QUERY_TASK_SCRIPT": get_query(
                    "datafootprint_reduction.sql", **variable_dict
                ),
                "DB_SERVER": pg_db_server_main,
                "USE_PG_CERT": str(use_cert),
            },
        )

        bq_qa_check_day_over_day_id = (
            f"{dag_prefix}-{ras_code}-lookup-activity-bq-qa-day-over-day"
        )
        bq_qa_check_day_over_day = GKEStartPodOperator(
            task_id=bq_qa_check_day_over_day_id,
            name=bq_qa_check_day_over_day_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            execution_timeout=dt.timedelta(minutes=5),
            get_logs=True,
            dag=dag,
            arguments=[
                "python3",
                "main.py",
                "execute-bigquery",
                f"--gcp_project_id={project_id}",
            ],
            env_vars={
                "GCP_PROJECT": project_id,
                "CREATE_DATASETS": str(True),
                "QUERY": bq_qa_lookup_values_activity_fm_script,
            },
        )

        create_bq_activity_view_id = (
            f"{dag_prefix}-{ras_code}-bq-create-lookup-values-activity-view"
        )
        create_bq_activity_view = GKEStartPodOperator(
            task_id=create_bq_activity_view_id,
            name=create_bq_activity_view_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            execution_timeout=dt.timedelta(minutes=5),
            get_logs=True,
            dag=dag,
            arguments=[
                "python3",
                "main.py",
                "execute-bigquery",
                f"--gcp_project_id={project_id}",
            ],
            env_vars={
                "GCP_PROJECT": project_id,
                "CREATE_DATASETS": str(True),
                "QUERY": bq_create_lookup_values_activity_fm_view_script,
            },
        )

        if ras_code == "rbme":
            delete_test_account_records_id = (
                f"{dag_prefix}-{ras_code}-clear-out-test-account-records"
            )
            delete_test_account_records = GKEStartPodOperator(
                task_id=delete_test_account_records_id,
                name=delete_test_account_records_id,
                image=get_full_image_name("execute-pg-task", gcr_registry),
                arguments=["python3", "-m" "src.pgsql_cmd_task", "execute-task"],
                resources=get_resource_size("Small"),
                secrets=secrets_pg_db,
                env_vars={
                    "DB_NAME": pg_db_name,
                    "ENV": environment,
                    "PG_QUERY_TASK_SCRIPT": delete_rbme_test_account_records,
                    "DB_SERVER": pg_db_server_main,
                    "USE_PG_CERT": str(use_cert),
                },
            )
            delete_test_account_records >> pg_to_gcs

        create_refresh_datasets >> create_bq_table_task >> check_number_of_rows
        (
            gcs_to_bq
            >> qa_check
            >> publish_bq
            >> publish_qa_check
            >> datafootprint_reduction_task
        )
        (
            update_wrong_values_task
            >> pg_to_gcs
            >> check_number_of_rows
            >> [gcs_to_bq, no_data_found]
        )
        (
            [no_data_found, publish_qa_check]
            >> create_lookup_values_activity_version_task
            >> bq_qa_check
            >> bq_to_pg_lookup_activity_version
            >> create_lookup_values_activity_view_task
        )

        (
            create_lookup_values_activity_version_task
            >> bq_qa_check_day_over_day
            >> bq_to_pg_lookup_activity_version
            >> create_bq_activity_view
        )

        return dag


trigger_client_dag = DAG(
    dag_id="sales-values-lookup-reporting-all",
    schedule_interval=None,
    default_args=default_args,
)


def unconditionally_trigger(context, dag_run_obj):
    return dag_run_obj


# render DAGs for all clients
def dag_factory(
    sales_code,
    client_id,
    fleet_customer_id,
    client_version,
    ras_code,
    host_name,
    ims_conversion_status,
    pg_db_server_name,
):
    dag_id = f"{dag_prefix}-reporting-{ras_code}"
    globals()[dag_id] = create_dag(
        dag_id,
        ras_code,
        client_version,
        client_id,
        fleet_customer_id,
        host_name,
        ims_conversion_status,
        pg_db_server=pg_db_shard_servers[pg_db_server_name]["replica"][environment],
        use_cert=str(pg_db_shard_servers[pg_db_server_name]["use_certificates"]),
        pg_db_server_main=pg_db_shard_servers[pg_db_server_name]["main"][environment],
        pg_db_name=pg_db_shard_servers[pg_db_server_name]["db_name"],
    )
    sales_values_lookup_trigger = TriggerDagRunOperator(
        task_id="trigger-" + dag_id,
        trigger_dag_id=dag_id,
        execution_date="{{ execution_date }}",
        dag=trigger_client_dag,
        execution_timeout=dt.timedelta(minutes=30),
        retries=3,
        retry_delay=dt.timedelta(seconds=3),
        on_failure_callback=task_fail_slack_alert,
    )

    sales_values_lookup = ExternalTaskSensor(
        task_id=dag_id + "-finished",
        external_dag_id=dag_id,
        external_task_id=None,
        dag=trigger_client_dag,
        mode="reschedule",
        timeout=60 * 60,
        poke_interval=60 * 1,
    )

    sales_values_lookup_trigger >> sales_values_lookup


for client in all_sales_active_clients:
    dag_factory(*client)
