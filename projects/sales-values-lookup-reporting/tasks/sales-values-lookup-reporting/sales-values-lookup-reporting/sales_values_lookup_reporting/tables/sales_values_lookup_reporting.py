import operator

# GCS
output_filename_pg_gcs = (
    "gs://rs-client-{CLIENT_CODE}-{CLIENT_VERSION}-stage{ENV_SUFFIX}/"
    "pipeline/sales-values-lookup-reporting/"
    "{VERSION_ID}/avro/pg_values_lookup_{CLIENT_CODE}_{VERSION_ID}.avro"
)

output_filename_pg_gcs_multi = (
    "gs://{PROJECT_ID}-sales-values-lookup/"
    "etl/{VERSION_ID}/pg-to-gcs/avro/sales_values_lookup_reporting"
    "/pg_values_lookup_{CLIENT_CODE}_{VERSION_ID}.avro"
)

# AVRO file
av_schema = {
    "type": "record",
    "name": "Root",
    "fields": [
        {"name": "request_id", "type": ["null", "string"]},
        {"name": "previous_request_id", "type": ["null", "string"]},
        {"name": "user_platform", "type": ["null", "string"]},
        {"name": "user_id", "type": ["null", "int"]},
        {"name": "user_name", "type": ["null", "string"]},
        {"name": "full_name", "type": ["null", "string"]},
        {"name": "fleet_customer_id", "type": ["null", "int"]},
        {"name": "client_code", "type": ["null", "string"]},
        {"name": "client_id", "type": ["null", "int"]},
        {
            "name": "lookup_date_utc",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {"name": "lookup_date_year_month", "type": ["null", "string"]},
        {"name": "valuation_market", "type": ["null", "string"]},
        {"name": "fmv", "type": ["null", "double"]},
        {"name": "fmv_base", "type": ["null", "double"]},
        {
            "name": "fmv_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {
            "name": "fmv_base_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {"name": "olv", "type": ["null", "double"]},
        {"name": "olv_base", "type": ["null", "double"]},
        {
            "name": "olv_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {
            "name": "olv_base_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {"name": "flv", "type": ["null", "double"]},
        {"name": "flv_base", "type": ["null", "double"]},
        {
            "name": "flv_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {
            "name": "flv_base_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {"name": "wlv", "type": ["null", "double"]},
        {"name": "wlv_base", "type": ["null", "double"]},
        {
            "name": "wlv_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {
            "name": "wlv_base_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {"name": "mpe", "type": ["null", "int"]},
        {"name": "mpe_base", "type": ["null", "int"]},
        {
            "name": "mpe_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {
            "name": "mpe_base_rfx_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {"name": "mpe_method", "type": ["null", "string"]},
        {
            "name": "valuation_effective_date",
            "type": ["null", {"type": "int", "logicalType": "date"}],
        },
        {"name": "meter_adjustment", "type": ["null", "double"]},
        {"name": "configuration_adjustment", "type": ["null", "double"]},
        {"name": "region_adjustment", "type": ["null", "double"]},
        {"name": "condition_adjustment", "type": ["null", "double"]},
        {"name": "recondition_adjustment", "type": ["null", "double"]},
        {"name": "book_adjustment", "type": ["null", "double"]},
        {"name": "average_meter", "type": ["null", "double"]},
        {"name": "actual_meter", "type": ["null", "double"]},
        {"name": "meter_per_year", "type": ["null", "double"]},
        {"name": "meter_code", "type": ["null", "string"]},
        {"name": "meter_adjustment_fail_code", "type": ["null", "int"]},
        {"name": "valuation_country", "type": ["null", "string"]},
        {"name": "valuation_state", "type": ["null", "string"]},
        {"name": "valuation_region", "type": ["null", "string"]},
        {"name": "valuation_supercategory", "type": ["null", "string"]},
        {"name": "valuation_category", "type": ["null", "string"]},
        {"name": "valuation_subcategory", "type": ["null", "string"]},
        {"name": "valuation_subcategory_scid", "type": ["null", "int"]},
        {"name": "valuation_make", "type": ["null", "string"]},
        {"name": "valuation_model", "type": ["null", "string"]},
        {"name": "valuation_model_year", "type": ["null", "int"]},
        {"name": "valuation_scid", "type": ["null", "int"]},
        {"name": "valuation_category_id", "type": ["null", "int"]},
        {"name": "valuation_subcategory_id", "type": ["null", "int"]},
        {"name": "valuation_make_id", "type": ["null", "int"]},
        {"name": "valuation_model_id", "type": ["null", "int"]},
        {"name": "valuation_configurations", "type": ["null", "string"]},
        {
            "name": "valuation_recondition_date",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {"name": "valuation_condition", "type": ["null", "double"]},
        {"name": "user_intent", "type": ["null", "string"]},
        {"name": "valuation_residuals_fmv", "type": ["null", "string"]},
        {"name": "valuation_residuals_flv", "type": ["null", "string"]},
        {"name": "valuation_residuals_wlv", "type": ["null", "string"]},
        {"name": "valuation_residuals_olv", "type": ["null", "string"]},
        {"name": "valuation_residuals_mpe", "type": ["null", "string"]},
        {"name": "valuation_residuals_meter_forecast", "type": ["null", "string"]},
        {"name": "algo_region_adjustment", "type": ["null", "double"]},
        {"name": "algo_condition_adjustment", "type": ["null", "double"]},
        {"name": "algo_meter_adjustment", "type": ["null", "double"]},
        {"name": "algo_flv", "type": ["null", "double"]},
        {"name": "cumulative_percentile_low", "type": ["null", "double"]},
        {"name": "cumulative_percentile_high", "type": ["null", "double"]},
        {"name": "current_residual_age_index", "type": ["null", "int"]},
        {
            "name": "flv_residual_curve",
            "type": ["null", {"type": "array", "items": "int"}],
        },
        {
            "name": "fmv_residual_curve",
            "type": ["null", {"type": "array", "items": "int"}],
        },
        {"name": "mpe_avg_days_to_sell", "type": ["null", "int"]},
        {"name": "rb_flv", "type": ["null", "double"]},
        {"name": "fmv_top_quartile_3_month", "type": ["null", "double"]},
        {"name": "fmv_top_quartile_3_month_base", "type": ["null", "double"]},
        {"name": "rb_flv_bottom_quartile_value", "type": ["null", "double"]},
        {"name": "rb_flv_top_quartile_value", "type": ["null", "double"]},
        {"name": "is_single_subcategory", "type": ["null", "boolean"]},
        {"name": "display_user_name", "type": ["null", "string"]},
        {"name": "lookup_values_tier", "type": ["null", "string"]},
        {"name": "auction_ratio_marketable_life_age", "type": ["null", "double"]},
        {"name": "retail_ratio_marketable_life_age", "type": ["null", "double"]},
        {"name": "midpoint_ratio_marketable_life_age", "type": ["null", "double"]},
        {"name": "auction_ratio_marketable_life_max", "type": ["null", "double"]},
        {"name": "retail_ratio_marketable_life_max", "type": ["null", "double"]},
        {"name": "midpoint_ratio_marketable_life_max", "type": ["null", "double"]},
        {"name": "auction_ratio_marketable_life_meter", "type": ["null", "double"]},
        {"name": "retail_ratio_marketable_life_meter", "type": ["null", "double"]},
        {"name": "midpoint_ratio_marketable_life_meter", "type": ["null", "double"]},
        {"name": "meter_adjustment_status", "type": ["null", "string"]},
        {"name": "insights_category", "type": ["null", "string"]},
        {"name": "insights_category_id", "type": ["null", "int"]},
        {"name": "insights_subcategory", "type": ["null", "string"]},
        {"name": "insights_subcategory_id", "type": ["null", "int"]},
        {
            "name": "_created_at",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {
            "name": "_updated_at",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {"name": "_version", "type": ["null", "long"]},
    ],
}

# PostgreSQL
pg_query = """
SELECT
  request_id,
  previous_request_id,
  user_platform,
  user_id,
  user_name,
  full_name,
  fleet_customer_id,
  client_code,
  client_id,
  lookup_date_utc,
  lookup_date_year_month,
  valuation_market,
  fmv::double precision,
  fmv_base::double precision,
  fmv_rfx_date,
  fmv_base_rfx_date,
  olv::double precision,
  olv_base::double precision,
  olv_rfx_date,
  olv_base_rfx_date,
  flv::double precision,
  flv_base::double precision,
  flv_rfx_date,
  flv_base_rfx_date,
  wlv::double precision,
  wlv_base::double precision,
  wlv_rfx_date,
  wlv_base_rfx_date,
  mpe,
  mpe_base,
  mpe_rfx_date,
  mpe_base_rfx_date,
  mpe_method,
  valuation_effective_date,
  meter_adjustment::double precision,
  configuration_adjustment::double precision,
  region_adjustment::double precision,
  condition_adjustment::double precision,
  recondition_adjustment::double precision,
  book_adjustment::double precision,
  average_meter::double precision,
  actual_meter::double precision,
  meter_per_year::double precision,
  meter_code,
  meter_adjustment_fail_code,
  valuation_country,
  valuation_state,
  valuation_region,
  valuation_supercategory,
  valuation_category,
  valuation_subcategory,
  valuation_subcategory_scid,
  valuation_make,
  valuation_model,
  valuation_model_year,
  valuation_scid,
  valuation_category_id,
  valuation_subcategory_id,
  valuation_make_id,
  valuation_model_id,
  valuation_configurations::TEXT,
  valuation_recondition_date,
  valuation_condition,
  user_intent,
  valuation_residuals_fmv::TEXT,
  valuation_residuals_flv::TEXT,
  valuation_residuals_wlv::TEXT,
  valuation_residuals_olv::TEXT,
  valuation_residuals_mpe::TEXT,
  valuation_residuals_meter_forecast::TEXT,
  algo_region_adjustment::double precision,
  algo_condition_adjustment::double precision,
  algo_meter_adjustment::double precision,
  algo_flv::double precision,
  cumulative_percentile_low::double precision,
  cumulative_percentile_high::double precision,
  current_residual_age_index,
  flv_residual_curve,
  fmv_residual_curve,
  mpe_avg_days_to_sell,
  rb_flv::double precision,
  fmv_top_quartile_3_month::double precision,
  fmv_top_quartile_3_month_base::double precision,
  rb_flv_bottom_quartile_value::double precision,
  rb_flv_top_quartile_value::double precision,
  is_single_subcategory,
  display_user_name,
  lookup_values_tier,
  auction_ratio_marketable_life_age::double precision,
  retail_ratio_marketable_life_age::double precision,
  midpoint_ratio_marketable_life_age::double precision,
  auction_ratio_marketable_life_max::double precision,
  retail_ratio_marketable_life_max::double precision,
  midpoint_ratio_marketable_life_max::double precision,
  auction_ratio_marketable_life_meter::double precision,
  retail_ratio_marketable_life_meter::double precision,
  midpoint_ratio_marketable_life_meter::double precision,
  insights_category,
  insights_category_id,
  insights_subcategory,
  insights_subcategory_id,
  meter_adjustment_status #>> '{{}}' AS meter_adjustment_status,
  _created_at,
  _updated_at,
  {VERSION_ID} _version
FROM
  rfm_{CLIENT_CODE}.values_lookup_transactions
"""

pg_where_filter = """
    WHERE
      lookup_date_utc::DATE >= ((NOW() AT TIME ZONE 'utc') - INTERVAL '3 days')::DATE
  """

# SQL Server

sql_query = """
SELECT
  CAST(NULL AS VARCHAR) request_id,
  CAST(NULL AS VARCHAR) previous_request_id,
  'rbmpe' user_platform,
  up.UserId user_id,
  useremailaddress user_name,
  CAST(NULL AS VARCHAR) full_name,
  {FLEET_CUSTOMER_ID} fleet_customer_id,
  '{CLIENT_CODE}' client_code,
  {CLIENT_ID} client_id,
  CAST(insertedtime AS DATETIME2) lookup_date_utc,
  yearmonthinsertedtime lookup_date_year_month,
  CAST(NULL AS VARCHAR) valuation_market,
  CAST(NULL AS FLOAT) fmv,
  CAST(NULL AS FLOAT) fmv_base,
  CAST(NULL AS DATETIME2) fmv_rfx_date,
  CAST(NULL AS DATETIME2) fmv_base_rfx_date,
  CAST(NULL AS FLOAT) olv,
  CAST(NULL AS FLOAT) olv_base,
  CAST(NULL AS DATETIME2) olv_rfx_date,
  CAST(NULL AS DATETIME2) olv_base_rfx_date,
  CAST(NULL AS FLOAT) flv,
  CAST(NULL AS FLOAT) flv_base,
  CAST(NULL AS DATETIME2) flv_rfx_date,
  CAST(NULL AS DATETIME2) flv_base_rfx_date,
  CAST(NULL AS FLOAT) wlv,
  CAST(NULL AS FLOAT) wlv_base,
  CAST(NULL AS DATETIME2) wlv_rfx_date,
  CAST(NULL AS DATETIME2) wlv_base_rfx_date,
  CAST(NULL AS DATE) valuation_effective_date,
  CAST(NULL AS FLOAT) meter_adjustment,
  CAST(NULL AS FLOAT) configuration_adjustment,
  CAST(NULL AS FLOAT) region_adjustment,
  CAST(NULL AS FLOAT) condition_adjustment,
  CAST(NULL AS FLOAT) recondition_adjustment,
  CAST(NULL AS FLOAT) book_adjustment,
  CAST(NULL AS FLOAT) average_meter,
  CAST(NULL AS FLOAT) actual_meter,
  CAST(NULL AS FLOAT) meter_per_year,
  CAST(NULL AS VARCHAR) meter_code,
  CAST(NULL AS INT) meter_adjustment_fail_code,
  country valuation_country,
  CAST(NULL AS VARCHAR) valuation_state,
  CAST(NULL AS VARCHAR) valuation_region,
  CAST(NULL AS VARCHAR) valuation_supercategory,
  CAST(NULL AS VARCHAR) valuation_category,
  CAST(NULL AS VARCHAR) valuation_subcategory,
  CAST(NULL AS VARCHAR) valuation_subcategory_scid,
  CAST(NULL AS VARCHAR) valuation_make,
  CAST(NULL AS VARCHAR) valuation_model,
  CAST(modelyear AS INT) valuation_model_year,
  CAST(scid AS INT) valuation_scid,
  CAST(NULL AS DATETIME2) valuation_recondition_date,
  CAST(NULL AS FLOAT) valuation_condition,
  userintent user_intent,
  CAST(NULL AS DATETIME2) _created_at,
  CAST(NULL AS DATETIME2) _updated_at,
  CAST({VERSION_ID} AS BIGINT) _version
FROM
  vod.fn_vod_logging(DEFAULT, {{FILTER}}, DEFAULT, DEFAULT) fvl
LEFT OUTER JOIN
  Users.Preferences up
ON
  fvl.useremailaddress = up.UserName
"""

sql_function = {
    "default": "DEFAULT",
    "filter": "CAST(DATEADD(DAY , -3, GETDATE() AT TIME ZONE 'UTC') AS DATE)",
}

# SQL Server shard

query_delete = """
DELETE
  mpe_reporting.rbme_logs
WHERE
  CAST(lookup_date_utc AS DATE) IN ({LOOKUP_DATES})
"""

query_count = """
SELECT
  COUNT(*) count
FROM
  mpe_reporting.rbme_logs
"""

# BQ export
bq_query_distinct_dates = """
SELECT
  DISTINCT CAST(lookup_date_utc AS DATE) lookup_date_utc
FROM
  `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}`
"""

# BigQuery

bq_query_view = """
SELECT
  *
FROM
  `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}`
"""

bq_query_current = """
WITH
  user_ids AS (
  SELECT
    DISTINCT user_id,
    user_name
  FROM
    `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}`
  WHERE
    user_id IS NOT NULL
)
SELECT
  a.request_id,
  a.previous_request_id,
  a.user_platform,
  b.user_id,
  a.user_name,
  a.full_name,
  a.fleet_customer_id,
  a.client_code,
  a.client_id,
  a.lookup_date_utc,
  a.lookup_date_year_month,
  a.valuation_market,
  a.fmv,
  a.fmv_base,
  a.fmv_rfx_date,
  a.fmv_base_rfx_date,
  a.olv,
  a.olv_base,
  a.olv_rfx_date,
  a.olv_base_rfx_date,
  a.flv,
  a.flv_base,
  a.flv_rfx_date,
  a.flv_base_rfx_date,
  a.wlv,
  a.wlv_base,
  a.wlv_rfx_date,
  a.wlv_base_rfx_date,
  a.mpe,
  a.mpe_base,
  a.mpe_rfx_date,
  a.mpe_base_rfx_date,
  a.mpe_method,
  a.valuation_effective_date,
  a.meter_adjustment,
  a.configuration_adjustment,
  a.region_adjustment,
  a.condition_adjustment,
  a.recondition_adjustment,
  a.book_adjustment,
  a.average_meter,
  a.actual_meter,
  a.meter_per_year,
  a.meter_code,
  a.meter_adjustment_fail_code,
  a.valuation_country,
  a.valuation_state,
  a.valuation_region,
  a.valuation_supercategory,
  a.valuation_category,
  a.valuation_subcategory,
  a.valuation_subcategory_scid,
  a.valuation_make,
  a.valuation_model,
  a.valuation_model_year,
  a.valuation_scid,
  a.valuation_category_id,
  a.valuation_subcategory_id,
  a.valuation_make_id,
  a.valuation_model_id,
  a.valuation_configurations,
  a.valuation_recondition_date,
  a.valuation_condition,
  a.user_intent,
  a.valuation_residuals_fmv,
  a.valuation_residuals_flv,
  a.valuation_residuals_wlv,
  a.valuation_residuals_olv,
  a.valuation_residuals_mpe,
  a.valuation_residuals_meter_forecast,
  a.algo_region_adjustment,
  a.algo_condition_adjustment,
  a.algo_meter_adjustment,
  a.algo_flv,
  a.cumulative_percentile_low,
  a.cumulative_percentile_high,
  a.current_residual_age_index,
  a.flv_residual_curve,
  a.fmv_residual_curve,
  a.mpe_avg_days_to_sell,
  a.rb_flv,
  a.fmv_top_quartile_3_month,
  a.fmv_top_quartile_3_month_base,
  a.rb_flv_bottom_quartile_value,
  a.rb_flv_top_quartile_value,
  a.is_single_subcategory,
  a.display_user_name,
  a.lookup_values_tier,
  a.auction_ratio_marketable_life_age,
  a.retail_ratio_marketable_life_age,
  a.midpoint_ratio_marketable_life_age,
  a.auction_ratio_marketable_life_max,
  a.retail_ratio_marketable_life_max,
  a.midpoint_ratio_marketable_life_max,
  a.auction_ratio_marketable_life_meter,
  a.retail_ratio_marketable_life_meter,
  a.midpoint_ratio_marketable_life_meter,
  a.meter_adjustment_status,
  a.insights_category,
  a.insights_category_id,
  a.insights_subcategory,
  a.insights_subcategory_id,
  a._created_at,
  a._updated_at,
  a._version
FROM
  `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}` a
LEFT OUTER JOIN
  user_ids b
ON
  a.user_name = b.user_name
"""


# Only append data if it doesn't exist
query_new_data = """
DECLARE modified_dates ARRAY<DATE>;
SET modified_dates = (
  SELECT
    ARRAY_AGG(DISTINCT DATE(lookup_date_utc))
  FROM
    `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}`
);
DELETE
FROM
  `{PROJECT_ID}.{DATASET_STAGE_ID}.{REPORT_TABLE_NAME}`
WHERE
  DATE(lookup_date_utc) IN UNNEST(modified_dates);

INSERT INTO
  `{PROJECT_ID}.{DATASET_STAGE_ID}.{REPORT_TABLE_NAME}`
({BQ_QUERY_COLUMNS})
{BQ_QUERY_CURRENT};

UPDATE `{PROJECT_ID}.{DATASET_STAGE_ID}.{REPORT_TABLE_NAME}` a
  SET a.fleet_customer_id = COALESCE(b.fleet_customer_id, {FLEET_CUSTOMER_ID})
  FROM (
    SELECT
      client_id,
      MAX(fleet_customer_id) as fleet_customer_id
    FROM
      `{PROJECT_ID}.{DATASET_STAGE_ID}.{REPORT_TABLE_NAME}`
    GROUP BY client_id
  ) b
WHERE
  a.client_id = b.client_id
  AND a.fleet_customer_id IS NULL;

UPDATE
  `{PROJECT_ID}.{DATASET_STAGE_ID}.{REPORT_TABLE_NAME}` a
SET
  a.valuation_country = COALESCE(c.country_code_ext, a.valuation_country)
FROM
  `{APPRAISALS_PROJECT_ID}.ss_export{DATASET_ENV_SUFFIX}.ras_sas_country` c
WHERE
  LOWER(CASE
      WHEN LOWER(a.valuation_country) IN ('canada', 'ca') THEN 'CAN'
      WHEN LOWER(a.valuation_country) IN ('united states',
      'us') THEN 'USA'
      WHEN LOWER(a.valuation_country) IN ('united kingdom', 'uk') THEN 'GBR'
      WHEN LOWER(a.valuation_country) IN ('france') THEN 'FRA'
      ELSE a.valuation_country
  END
    ) = LOWER(c.country_code_ext) ;
  -- update  valuation_market
UPDATE
  `{PROJECT_ID}.{DATASET_STAGE_ID}.{REPORT_TABLE_NAME}` a
SET
  a.valuation_market = COALESCE(cm.country_code_ext, a.valuation_market)
FROM
  `{APPRAISALS_PROJECT_ID}.ss_export{DATASET_ENV_SUFFIX}.ras_sas_country` cm
WHERE
  LOWER(CASE
      WHEN LOWER(a.valuation_market) IN ('canada', 'ca') THEN 'CAN'
      WHEN LOWER(a.valuation_market) IN ('united states',
      'us') THEN 'USA'
      WHEN LOWER(a.valuation_market) IN ('united kingdom', 'uk') THEN 'GBR'
      WHEN LOWER(a.valuation_market) IN ('france') THEN 'FRA'
      ELSE a.valuation_market
  END
    ) = LOWER(cm.country_code_ext);
"""

sets = [([query_new_data], None, False)]

bq_query_check_missing_row_count = """
SELECT
  count(*)
FROM
  `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}` version
LEFT OUTER JOIN
  `{PROJECT_ID}.{DATASET_STAGE_ID}.{REPORT_TABLE_NAME}` final
ON
  version.request_id = final.request_id
WHERE
  final.request_id IS NULL
  AND version.user_platform != 'rbmpe'
"""

bq_query_check_incorrect_previous_request = """
SELECT
  count(*) count
FROM
  `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}` a
LEFT OUTER JOIN
  `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}` b
ON
  a.previous_request_id = b.request_id
WHERE
  a.valuation_scid!=b.valuation_scid
  OR a.valuation_model_year!=b.valuation_model_year
  OR a.valuation_country != b.valuation_country
"""

# QA
# Update bq_qa_version_table_query if columns change
qa_columns = {
    "num_rows": [],
    "sum": [
        "fmv",
        "olv",
        "flv",
        "meter_adjustment",
        "configuration_adjustment",
        "region_adjustment",
        "book_adjustment",
        "average_meter",
        "actual_meter",
        "meter_adjustment_fail_code",
        "valuation_condition",
        "valuation_model_year",
    ],
    "hash_sum": [
        "valuation_country",
        "valuation_state",
        "valuation_region",
        "valuation_supercategory",
        "valuation_make",
        "valuation_model",
    ],
}

bq_qa_version_table_query = """
SELECT
  fmv,
  olv,
  flv,
  meter_adjustment,
  configuration_adjustment,
  region_adjustment,
  book_adjustment,
  average_meter,
  actual_meter,
  meter_adjustment_fail_code,
  valuation_condition,
  valuation_model_year,
  valuation_country,
  valuation_state,
  valuation_region,
  valuation_supercategory,
  valuation_make,
  valuation_model
FROM
  `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}`
"""

bq_qa_queries = [
    (
        operator.eq,
        bq_query_check_missing_row_count,
        0,
    )  # there is not missing data to insert
]
