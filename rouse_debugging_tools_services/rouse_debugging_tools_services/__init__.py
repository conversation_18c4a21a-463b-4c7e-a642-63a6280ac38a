import sys
import logging

from .google_cloud_debugger_service import enable_google_cloud_debugger

__all__ = ('enable_google_cloud_debugger')

logger = logging.getLogger(__name__)
logger.setLevel(logging.WARNING)
handler = logging.StreamHandler(sys.stderr)
formatter = logging.Formatter(
    '%(asctime)s - [%(name)s] %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

logger.warning("This package (rouse_debugging_tools_services) is deprecated. See README.md for more information.")
