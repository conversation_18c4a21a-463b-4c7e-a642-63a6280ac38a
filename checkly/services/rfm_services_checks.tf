data "google_secret_manager_secret_version" "sales_rfm_channels_api_auth_token" {
  secret  = "sales_rfm_channels_api_auth_token"
  project = local.services_prod
}

data "google_secret_manager_secret_version" "sales_catalog_api_static_auth_token" {
  secret  = "sales_catalog_api_static_auth_token"
  project = local.services_prod
}

data "google_secret_manager_secret_version" "mascus_api_auth" {
  secret  = "mascus_api_credentials"
  project = local.services_prod
}

data "google_secret_manager_secret_version" "enterprise_domain_service_api_auth" {
  secret  = "enterprise_domain_service_static_auth_token"
  project = local.services_prod
}

data "google_secret_manager_secret_version" "enterprise_domain_service_api_auth_dev" {
  secret  = "enterprise_domain_service_static_auth_token"
  project = local.services_dev
}

data "google_secret_manager_secret_version" "sales_ingest_api_auth_dev" {
  secret = "sales_ingest_api_static_auth_token"
  project = local.services_dev
}

data "google_secret_manager_secret_version" "sales_ingest_api_auth" {
  secret = "sales_ingest_api_static_auth_token"
  project = local.services_prod
}

resource "checkly_environment_variable" "sales_rfm_channels_api_auth_token" {
  key    = "SALES_RFM_CHANNELS_API_UTIL_AUTH"
  value  = data.google_secret_manager_secret_version.sales_rfm_channels_api_auth_token.secret_data
  locked = true
}

resource "checkly_environment_variable" "enterprise_domain_service_api_auth" {
  key    = "ENTERPRISE_DOMAIN_SERVICE_STATIC_AUTH_TOKEN"
  value  = data.google_secret_manager_secret_version.enterprise_domain_service_api_auth.secret_data
  locked = true
}

resource "checkly_environment_variable" "sales_catalog_api_static_auth_token" {
  key    = "SALES_RFM_CATALOG_API_STATIC_AUTH"
  value  = data.google_secret_manager_secret_version.sales_catalog_api_static_auth_token.secret_data
  locked = true
}

resource "checkly_environment_variable" "mascus_api_auth_username" {
  key    = "MASCUS_API_USERNAME"
  value  = jsondecode(data.google_secret_manager_secret_version.mascus_api_auth.secret_data)["username"]
  locked = true
}

resource "checkly_environment_variable" "mascus_api_auth_password" {
  key    = "MASCUS_API_PASSWORD"
  value  = jsondecode(data.google_secret_manager_secret_version.mascus_api_auth.secret_data)["password"]
  locked = true
}

resource "checkly_environment_variable" "sales_ingest_api_auth" {
  key    = "SALES_INGEST_API_STATIC_AUTH"
  value  = data.google_secret_manager_secret_version.sales_ingest_api_auth.secret_data
  locked = true
}

resource "checkly_check_group" "rfm_services_group" {
  name      = "RFM Services"
  activated = true
  muted     = false
  tags      = local.tags
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.pagerduty_rfm_channels_api_channel.id
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  concurrency = 3
}

resource "checkly_snippet" "getTestAdminReleaseAccessTokenTerraform" {
  name   = "Terraform"
  script = <<EOT
  const axios = require('axios')

  // grab the necessary credentials set up earlier in your environment variables.
  const {
    AUTH_LOGIN_URL,
    SALES_PORTAL_AUTH0_CLIENT_ID,
    SALES_PORTAL_AUTH0_CLIENT_SECRET,
    SALES_PORTAL_AUTH0_AUDIENCE,
    API_TEST_ADMIN_RELEASE_3_5,
    TEST_ADMIN_RELEASE_3_5_PASSWORD
  } = environment

  // fetch an access token
  const { data: { access_token } } = await axios({
    url: AUTH_LOGIN_URL,
    method: 'POST',
    data: {
      grant_type: 'http://auth0.com/oauth/grant-type/password-realm',
      username: API_TEST_ADMIN_RELEASE_3_5,
      password: TEST_ADMIN_RELEASE_3_5_PASSWORD,
      client_id: SALES_PORTAL_AUTH0_CLIENT_ID,
      client_secret: SALES_PORTAL_AUTH0_CLIENT_SECRET,
      audience: SALES_PORTAL_AUTH0_AUDIENCE,
      scope: 'openid',
      realm: 'Rouse-RasIdentity-Auth'
    }
  })


  // set the Authorization header
  request.headers["Authorization"] = `Bearer $${access_token}`
EOT
}

resource "checkly_snippet" "getSalesXYZAccessTokenTerraform" {
  name   = "getSalesXYZAccessTokenTerraform"
  script = <<EOT
  const axios = require('axios')

  // grab the necessary credentials set up earlier in your environment variables.
  const {
    RFM_AUTH_URL, RFM_XYZ_ROUSEADMIN_USER, RFM_XYZ_ROUSEADMIN_PASSWORD, API_RFM_CLIENT_ID, API_RFM_CLIENT_SECRET
  } = environment

  // fetch an access token
  const { data: { access_token }} = await axios({
    url: RFM_AUTH_URL,
    method: 'POST',
    data: {
      grant_type: 'http://auth0.com/oauth/grant-type/password-realm',
      username: RFM_XYZ_ROUSEADMIN_USER,
      password: RFM_XYZ_ROUSEADMIN_PASSWORD,
      client_id: API_RFM_CLIENT_ID,
      client_secret: API_RFM_CLIENT_SECRET,
      auth_provider: 'rasidentity',
      scope: 'offline_access',
      audience: 'https://api.rouseservices.com',
      realm: 'Rouse-RasIdentity-Auth'

    },
  })

  // set the Authorization header
  request.headers["Authorization"] = `Bearer $${access_token}`
EOT
}

resource "checkly_check" "enterprise_domain_service_api_locks_check" {
  name                   = "Enterprise Domain Service API Locks Check"
  type                   = "API"
  activated              = true
  frequency              = 10
  degraded_response_time = 5000
  should_fail            = false
  locations              = ["us-west-1"]

  request {
    method           = "GET"
    url              = "https://domain-registration-api.rouseservices.com/custom_domain/locks"
    follow_redirects = true
    headers = {
      Authorization = "Bearer {{ENTERPRISE_DOMAIN_SERVICE_STATIC_AUTH_TOKEN}}"
    }

    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "TEXT_BODY"
      comparison = "NOT_CONTAINS"
      target     = "\"FAILED\""
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.domain_service_alerts_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "enterprise_domain_service_api_locks_check_dev" {
  name                   = "Enterprise Domain Service API Locks Check Dev"
  type                   = "API"
  activated              = true
  frequency              = 10
  degraded_response_time = 5000
  should_fail            = false
  locations              = ["us-west-1"]

  request {
    method           = "GET"
    url              = "https://domain-registration-api.develop.rouseservices.com/custom_domain/locks"
    follow_redirects = true
    headers = {
      Authorization = "Bearer {{ENTERPRISE_DOMAIN_SERVICE_STATIC_AUTH_TOKEN_DEV}}"
    }

    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "TEXT_BODY"
      comparison = "NOT_CONTAINS"
      target     = "\"FAILED\""
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.domain_service_alerts_dev_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "rfm_channels_api_health_check" {
  name                   = "RFM Channels API Health Check"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  group_order            = 1
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]

  request {
    method           = "GET"
    url              = "https://sales-rfm-channels-api.rouseservices.com/healthcheck"
    follow_redirects = true
    headers = {
      Authorization = "Bearer {{SALES_RFM_CHANNELS_API_UTIL_AUTH}}"
    }

    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "error_count"
      comparison = "EQUALS"
      target     = 0
    }

    assertion {
      source     = "JSON_BODY"
      property   = "errors"
      comparison = "IS_EMPTY"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.pagerduty_rfm_channels_api_channel.id
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "sales_catalog_api_health_check" {
  name                   = "Sales Catalog API Health Check"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  group_order            = 2
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]

  request {
    method           = "GET"
    url              = "https://xyz.webshop.rouseservices.com/api/hz/external"
    follow_redirects = true
    headers = {
      Authorization = "Bearer {{SALES_RFM_CATALOG_API_STATIC_AUTH}}"
    }

    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "error_count"
      comparison = "EQUALS"
      target     = 0
    }

    assertion {
      source     = "JSON_BODY"
      property   = "errors"
      comparison = "IS_EMPTY"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "mascus_auth_check" {
  name        = "Mascus API Auth Check"
  type        = "API"
  activated   = true
  frequency   = 15
  should_fail = false
  group_id    = checkly_check_group.rfm_services_group.id
  group_order = 3
  locations = ["us-west-1"]

  request {
    method = "POST"
    url    = "https://services.mascus.com/api/ritchielistapi.asmx"
    headers = {
      Content-Type = "application/soap+xml; charset=utf-8",
      SOAPAction   = "https://services.mascus.com/api/OpenSession",
    }
    body = <<-EOF
      <?xml version="1.0" encoding="utf-8"?>
      <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
        <soap12:Body>
          <OpenSession xmlns="http://services.mascus.com/api">
            <username>{{MASCUS_API_USERNAME}}</username>
            <password>{{MASCUS_API_PASSWORD}}</password>
          </OpenSession>
        </soap12:Body>
      </soap12:Envelope>
    EOF

    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.pagerduty_rfm_channels_api_channel.id
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "ingest_schema" {
  name             = "RFM Ingest API Schema"
  type             = "API"
  activated        = true
  frequency        = 5
  should_fail      = false
  group_id         = checkly_check_group.rfm_services_group.id
  group_order      = 4
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  setup_snippet_id = checkly_snippet.getTestAdminReleaseAccessTokenTerraform.id
  runtime_id       = "2023.09"
  request {
    method = "GET"
    url    = "https://rfm-ingest-api.rouseservices.com/fleet_ingest/schema"
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results.length"
      comparison = "GREATER_THAN"
      target     = "0"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].schema.length"
      comparison = "GREATER_THAN"
      target     = "0"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].settings"
      comparison = "NOT_NULL"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "ingest_country_list" {
  name             = "RFM Ingest API Country List"
  type             = "API"
  activated        = true
  frequency        = 5
  should_fail      = false
  group_id         = checkly_check_group.rfm_services_group.id
  group_order      = 5
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  setup_snippet_id = checkly_snippet.getTestAdminReleaseAccessTokenTerraform.id
  runtime_id       = "2023.09"

  request {
    method    = "POST"
    url       = "https://rfm-ingest-api.rouseservices.com/fleet_ingest/countries"
    body_type = "JSON"
    body      = "{\"fields\":[\"country_name\",\"country_code\"],\"filters\":{\"or\":[{\"==\":[{\"var\":\"country_code\"},\"%\"]},{\"==\":[{\"var\":\"country_name\"},\"%\"]}]},\"page\":{\"size\":100,\"index\":0,\"sort\":{\"direction\":\"asc\",\"fields\":[\"country_name\"]}}}"

    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results.length"
      comparison = "GREATER_THAN"
      target     = "0"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].country_name"
      comparison = "NOT_NULL"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].country_code"
      comparison = "NOT_NULL"
    }

  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "ingest_classification_suggestions" {
  name             = "RFM Ingest API Classification Suggestions"
  type             = "API"
  activated        = true
  frequency        = 5
  should_fail      = false
  group_id         = checkly_check_group.rfm_services_group.id
  group_order      = 6
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  setup_snippet_id = checkly_snippet.getTestAdminReleaseAccessTokenTerraform.id
  runtime_id       = "2023.09"

  request {
    method = "GET"
    url    = "https://rfm-ingest-api.rouseservices.com/classification/make/suggestions"
    query_parameters = {
      make        = "CATERPILLAR"
      model       = "D6T%20LOWGP"
      description = "D6T%20dozer"
    }
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results.length"
      comparison = "GREATER_THAN"
      target     = "0"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].conf"
      comparison = "NOT_NULL"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].value"
      comparison = "EQUALS"
      target     = "Caterpillar"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "ingest_classification_search" {
  name             = "RFM Ingest API Classification Search"
  type             = "API"
  activated        = true
  frequency        = 5
  should_fail      = false
  group_id         = checkly_check_group.rfm_services_group.id
  group_order      = 7
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  setup_snippet_id = checkly_snippet.getTestAdminReleaseAccessTokenTerraform.id
  runtime_id       = "2023.09"

  request {
    method    = "POST"
    url       = "https://rfm-ingest-api.rouseservices.com/classification/search"
    body_type = "JSON"
    body      = "{\"fields\":[\"model_year_min\",\"model_year_max\"],\"filters\":{\"and\":[{\"===\":[{\"var\":\"category\"},\"Vehicles\"]},{\"===\":[{\"var\":\"make\"},\"Ford\"]},{\"===\":[{\"var\":\"model\"},\"Explorer\"]},{\"===\":[{\"var\":\"subcategory\"},\"Sport Utility Vehicles\"]}]},\"page\":{\"index\":0,\"size\":30,\"sort\":{\"fields\":[\"model_year_min\",\"model_year_max\"],\"direction\":\"asc\"}}}"

    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results.length"
      comparison = "EQUALS"
      target     = "2"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].field"
      comparison = "EQUALS"
      target     = "model_year_min"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[1].field"
      comparison = "EQUALS"
      target     = "model_year_max"
    }

  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "ingest_classification_search_v2" {
  name             = "RFM Ingest API Classification Search V2"
  type             = "API"
  activated        = true
  frequency        = 5
  should_fail      = false
  group_id         = checkly_check_group.rfm_services_group.id
  group_order      = 8
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  setup_snippet_id = checkly_snippet.getTestAdminReleaseAccessTokenTerraform.id
  runtime_id       = "2023.09"

  request {
    method    = "POST"
    url       = "https://rfm-ingest-api.rouseservices.com/classification/search/v2"
    body_type = "JSON"
    body      = "{\"term\": \"ZX270LC\",\"fields\": [\"model\"],\"filters\": {\"and\":[{\"===\": [{\"var\": \"make\"},\"Hitachi\"]}]},\"search_sort\": \"hybrid\",\"search_type\": \"fuzzy\",\"limit_per_field\": 30}"
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results.length"
      comparison = "GREATER_THAN"
      target     = "0"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].field"
      comparison = "NOT_NULL"
    }
    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].value"
      comparison = "NOT_NULL"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].separated_fields"
      comparison = "NOT_NULL"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results[0].value_id"
      comparison = "NOT_NULL"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "fleet_dashboard" {
  name             = "RFM API Fleet Dashboard Health Check"
  type             = "API"
  activated        = true
  frequency        = 5
  should_fail      = false
  group_id         = checkly_check_group.rfm_services_group.id
  group_order      = 9
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  setup_snippet_id = checkly_snippet.getSalesXYZAccessTokenTerraform.id
  runtime_id       = "2023.09"

  request {
    method    = "POST"
    url       = "https://rfm-api.rouseservices.com/fleet_dashboard?currency=usd"
    body_type = "JSON"
    body      = "{\"filters\":{},\"currency\":\"usd\"}"
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results.length"
      comparison = "GREATER_THAN"
      target     = "0"
    }

    dynamic "assertion" {
      for_each = toset([
        "summary",
        "summary.total_fleet_units",
        "summary.total_fleet_cost",
        "summary.total_fleet_nbv",
        "summary.total_units_cost",
        "summary.total_units_nbv",
        "markets",
        "markets.total_mpe_units",
        "markets.total_mpe_cost",
        "markets.total_rb_units",
        "markets.total_rb_cost",
        "marketable_life",
        "premium_insights",
        "photos",
        "classification",
      ])
      content {
        source     = "JSON_BODY"
        property   = "$.results[0].${assertion.key}"
        comparison = "NOT_NULL"
      }
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "listings_dashboard" {
  name             = "RFM API Listings Dashboard Health Check"
  type             = "API"
  activated        = true
  frequency        = 5
  should_fail      = false
  group_id         = checkly_check_group.rfm_services_group.id
  group_order      = 10
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  setup_snippet_id = checkly_snippet.getSalesXYZAccessTokenTerraform.id
  runtime_id       = "2023.09"

  request {
    method    = "POST"
    url       = "https://rfm-api.rouseservices.com/listings_dashboard?currency"
    body_type = "JSON"
    body      = "{\"filters\":{},\"currency\":\"usd\"}"
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "$.results.length"
      comparison = "GREATER_THAN"
      target     = "0"
    }

    dynamic "assertion" {
      for_each = toset([
        "summary",
        "summary.total_units_listed",
        "summary.total_list_price",
        "summary.total_units_webshop",
        "summary.total_list_price_webshop",
        "summary.total_cost_webshop",
        "summary.total_units_ritchie_list",
        "summary.total_list_price_ritchie_list",
        "summary.total_cost_ritchie_list",
        "listings_health",
        "listings_health.priced_within_target_range",
        "listings_health.up_to_date_pricing",
        "listings_health.recently_listed",
        "listings_health.photos",
        "listings_health.description",
        "featured",
        "priced_within_target_range_chart",
        "priced_within_target_range_chart.priced_low_section",
        "priced_within_target_range_chart.priced_mid_section",
        "priced_within_target_range_chart.priced_high_section",
      ])
      content {
        source     = "JSON_BODY"
        property   = "$.results[0].${assertion.key}"
        comparison = "NOT_NULL"
      }
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}


# RFM API Look Up Values Valuation/Value Variables
locals {
  category_id    = 88
  subcategory_id = 2426
  make_id        = 31
  model_id       = 55501
  model_year     = 2021
  meter          = 2100
  condition      = 3
  configuration = {
    "17"  = 68
    "18"  = 72
    "19"  = 76
    "21"  = 84
    "241" = 628
    "282" = -784
  }
  luv_request_bodies = [
    {
      country = "CAN"
      body = jsonencode({
        valuation = {
          category_id                    = local.category_id
          subcategory_id                 = local.subcategory_id
          make_id                        = local.make_id
          model_id                       = local.model_id
          model_year                     = local.model_year
          country                        = "Canada"
          meter                          = local.meter
          condition                      = local.condition
          configuration                  = local.configuration
          region                         = "british columbia"
          expected_annual_meter_increase = null
        }
      })
    },
    {
      country = "USA"
      body = jsonencode({
        valuation = {
          category_id                    = local.category_id
          subcategory_id                 = local.subcategory_id
          make_id                        = local.make_id
          model_id                       = local.model_id
          model_year                     = local.model_year
          country                        = "USA"
          valuation_country              = "USA"
          continental_grouping           = "NA"
          meter                          = local.meter
          is_custom_meter                = false
          condition                      = local.condition
          configuration                  = local.configuration
          region                         = "California"
          forecast_methodology           = "linear"
          expected_annual_meter_increase = null
        }
      })
    },
    {
      country = "UK"
      body = jsonencode({
        valuation = {
          supercategory    = null
          category         = "Excavators - Heavy"
          category_id      = local.category_id
          category_scid    = 26
          subcategory      = "75,000-99,999 Lb Excavators"
          subcategory_id   = local.subcategory_id
          subcategory_scid = 50803
          make             = "Caterpillar"
          make_id          = local.make_id
          make_scid        = 51974
          meter            = 1001
          meter_type       = "H"
          model            = "336F L"
          model_id         = local.model_id
          model_scid       = 67088
          model_year_min   = "2014"
          model_year_max   = "2022"
          model_year       = 2019
          country          = "UK"
          configuration = {
            "16" = "-64"
            "21" = "84"
          }
          currency_code = "gbp"
          condition     = 2
        }
      })
    }
  ]
  luv_assertions = [
    {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }, {
      source     = "JSON_BODY",
      property   = "$.results[0].values.fmv",
      comparison = "GREATER_THAN",
      target     = "1000"
    }, {
      source     = "JSON_BODY",
      property   = "$.results[0].meter.meter_adjustment",
      comparison = "GREATER_THAN",
      target     = "0.001"
    }, {
      source     = "JSON_BODY",
      property   = "$.results[0].configuration.configuration_adjustment",
      comparison = "GREATER_THAN",
      target     = "0.001"
    }
  ]
}

resource "checkly_check" "rfm_luv_valuation" {
  for_each         = {for idx, val in local.luv_request_bodies : idx => val}
  name             = "RFM API Look Up Values /look_up_values/valuation (${each.value.country})"
  type             = "API"
  activated        = true
  frequency        = 5
  should_fail      = false
  group_id         = checkly_check_group.rfm_services_group.id
  group_order      = 11
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  setup_snippet_id = checkly_snippet.getSalesXYZAccessTokenTerraform.id
  runtime_id       = "2023.09"

  request {
    method    = "POST"
    url       = "https://rfm-api.rouseservices.com/look_up_values/valuation"
    body_type = "JSON"
    body      = each.value.body
    headers = {
      Version : "2.0"
    }

    dynamic "assertion" {
      for_each = local.luv_assertions
      content {
        source     = assertion.value.source
        comparison = assertion.value.comparison
        target     = assertion.value.target
        property = lookup(assertion.value, "property", null)
      }
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}


resource "checkly_check" "rfm_luv_value" {
  for_each         = {for idx, val in local.luv_request_bodies : idx => val}
  name             = "RFM API Look Up Values /look_up_values/value (${each.value.country})"
  type             = "API"
  activated        = true
  frequency        = 5
  should_fail      = false
  group_id         = checkly_check_group.rfm_services_group.id
  group_order      = 12
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  setup_snippet_id = checkly_snippet.getSalesXYZAccessTokenTerraform.id
  runtime_id       = "2023.09"

  request {
    method    = "POST"
    url       = "https://rfm-api.rouseservices.com/look_up_values/value"
    body_type = "JSON"
    body      = each.value.body

    dynamic "assertion" {
      for_each = local.luv_assertions
      content {
        source     = assertion.value.source
        comparison = assertion.value.comparison
        target     = assertion.value.target
        property = lookup(assertion.value, "property", null)
      }
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

resource "checkly_check" "sales_ingest_api_health_check" {
  name                   = "RFM Ingest API Health Check"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  group_order            = 13
  locations = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]

  request {
    method           = "GET"
    url              = "https://rfm-ingest-api.rouseservices.com/hz/external"
    follow_redirects = true
    headers = {
      Authorization = "Bearer {{SALES_INGEST_API_STATIC_AUTH}}"
    }

    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }

    assertion {
      source     = "JSON_BODY"
      property   = "error_count"
      comparison = "EQUALS"
      target     = 0
    }

    assertion {
      source     = "JSON_BODY"
      property   = "errors"
      comparison = "IS_EMPTY"
    }
  }

    alert_settings {
    escalation_type = "RUN_BASED"

    reminders {
      amount   = 0
      interval = 5
    }

    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = local.tags
}

# RDO API and Portal Checks

resource "checkly_check" "rdo_api_health_check" {
  name                   = "RDO API"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  locations              = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  
  request {
    method           = "GET"
    url              = "https://rdo-api.rouseanalytics.com/hz"
    follow_redirects = true
    
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = concat(local.tags, ["RFM RDO Dashboard"])
}

resource "checkly_check" "rdo_admin_ui_health_check" {
  name                   = "RDO Admin UI"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  locations              = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  
  request {
    method           = "GET"
    url              = "https://admin.rouseanalytics.com/hz"
    follow_redirects = true
    
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = concat(local.tags, ["RFM RDO Dashboard"])
}

resource "checkly_check" "rdo_homepage_api_health_check" {
  name                   = "RDO Homepage API"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  locations              = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  
  request {
    method           = "GET"
    url              = "https://rdo-api.rouseanalytics.com/hz"
    follow_redirects = true
    
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = concat(local.tags, ["RFM RDO Dashboard"])
}

resource "checkly_check" "rdo_user_service_health_check" {
  name                   = "RDO User Service"
  type                   = "API"
  activated              = false  # Deactivated because this endpoint is internal-only and requires VPN
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  locations              = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  
  request {
    method           = "GET"
    url              = "https://user-service.rouseservices.com/hz"
    follow_redirects = true
    
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = concat(local.tags, ["RFM RDO Dashboard"])
}

resource "checkly_check" "herc_portal_rfm_client_health_check" {
  name                   = "RFM Herc Portal"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  locations              = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  
  request {
    method           = "GET"
    url              = "https://herc.rousesales.com/hz"
    follow_redirects = true
    
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = concat(local.tags, ["RFM RDO Dashboard"])
}

resource "checkly_check" "kiewit_portal_rfm_client_health_check" {
  name                   = "RFM Kiewit Portal"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  locations              = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  
  request {
    method           = "GET"
    url              = "https://kiewit.rousesales.com/hz"
    follow_redirects = true
    
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = concat(local.tags, ["RFM RDO Dashboard"])
}

resource "checkly_check" "rouse_sales_portal_rfm_client_health_check" {
  name                   = "RFM Rouse Sales Portal"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  locations              = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  
  request {
    method           = "GET"
    url              = "https://portal.rousesales.com/hz"
    follow_redirects = true
    
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = concat(local.tags, ["RFM RDO Dashboard"])
}

resource "checkly_check" "sunstate_portal_rfm_client_health_check" {
  name                   = "RFM Sunstate Portal"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  locations              = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  
  request {
    method           = "GET"
    url              = "https://sunstate.rousesales.com/hz"
    follow_redirects = true
    
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = concat(local.tags, ["RFM RDO Dashboard"])
}

resource "checkly_check" "united_rentals_portal_rfm_client_health_check" {
  name                   = "RFM United Rentals Portal"
  type                   = "API"
  activated              = true
  frequency              = 5
  degraded_response_time = 5000
  should_fail            = false
  group_id               = checkly_check_group.rfm_services_group.id
  locations              = ["us-west-1", "us-east-1", "ca-central-1", "eu-west-2"]
  
  request {
    method           = "GET"
    url              = "https://unitedrentals.rousesales.com/hz"
    follow_redirects = true
    
    assertion {
      source     = "STATUS_CODE"
      comparison = "EQUALS"
      target     = "200"
    }
  }

  alert_settings {
    escalation_type = "RUN_BASED"
    run_based_escalation {
      failed_run_threshold = 1
    }
  }

  alert_channel_subscription {
    activated  = true
    channel_id = checkly_alert_channel.incident_alerts_sales_slack_channel.id
  }

  tags = concat(local.tags, ["RFM RDO Dashboard"])
}
