import setuptools

with open("README.md", "r") as fh:
    long_description = fh.read()

setuptools.setup(
    name="rouse_kafka_tools",
    include_package_data=True,
    version="3.0.1",
    author="<PERSON>",
    author_email="<EMAIL>",
    description="Python module for working with Rouse Kafka services and resources.",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/RouseServices/infrastructure-tools",
    packages=setuptools.find_packages(),
    classifiers=[
        #   3 - Alpha
        #   4 - Beta
        #   5 - Production/Stable
        "Development Status :: 5",
        "Programming Language :: Python :: 3",
        "Operating System :: OS Independent",
    ],
    install_requires=["confluent-kafka>=2.2.0", "redis>=5.0.1", "simplejson>=3.19.2"],
    python_requires=">=3.6",
)
