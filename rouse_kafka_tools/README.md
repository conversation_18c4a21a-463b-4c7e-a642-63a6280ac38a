# Rouse Kafka Tools

This module is used to work with Rouse Kafka resources.

## Install

Use pip to install the module from the private Rouse PyPI
```shell
PIP_EXTRA_INDEX_URL="https://pypi.rouseservices.io/repo/simple/" pip install rouse_kafka_tools
```

## Usage

### Kafka Retries

#### Generic Retry Observer Client

For details on the Generic Retry Observer please view the docs in the repository where it is maintained: 
https://github.com/RouseServices/kafka-retry-service/tree/main/workers/generic_retry_observer

This module contains a client to simplify working with the retry service;

```python
from confluent_kafka import Message
from rouse_kafka_tools.generic_retry_client import GenericRetryClient
from rouse_kafka_tools.dtos.exceptions import MaxRetryException

msg: Message = <<consumer logic to get message>>
client = GenericRetryClient(environment_name="local")
try:
  retry_client.upsert_generic_retry_cache_entry_from_message(message=msg, raise_max_retry_time_exception=True)
except MaxRetryException:
  pass
```

The client can be configured by environment name;
* `local`: uses a local redis instance
* `prod`: uses the Production Kafka Retry Service
* `develop` and all others: uses the Dev Kafka Retry Service

The client can also be configured using explicit redis values: `redis_host`, `redis_port`, `redis_db`

### Consumer Lag Calculator

The consumer lag calculator is used to calculate the lag of a consumer group. It uses the Kafka Admin API to 
get the current offsets and the consumer group offsets. It then calculates the lag for each partition and 
returns a dictionary with the lag for each topic.

```python
from rouse_kafka_tools.confluent import ConsumerConfig, ConsumerLagCalculator

consumer_config = ConsumerConfig(
    bootstrap_servers="BOOTSTRAP_SERVER",
    group_id="GROUP_ID",
    sasl_username="KAFKA_USERNAME",
    sasl_password="KAFKA_PASSWORD",
)
consumer_lag_calculator = ConsumerLagCalculator(consumer_config)
consumer_lag_calculator.get_consumer_lag_for_all_partitions("topic.name")
```

Sample response, partition as key and lag as value:
```python
{0: 1730, 1: 1828, 2: 1762, 3: 1751}
```
