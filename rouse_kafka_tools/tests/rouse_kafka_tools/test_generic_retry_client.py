from datetime import datetime, timed<PERSON>ta
from unittest.mock import <PERSON><PERSON>ock

import pytest
import simple<PERSON><PERSON>
from fakeredis import FakeRedis
from freezegun import freeze_time

from rouse_kafka_tools.rouse_kafka_tools.config import LISTING_RETRY_HASH_NAME
from rouse_kafka_tools.rouse_kafka_tools.dtos.exceptions import MaxRetryException
from rouse_kafka_tools.rouse_kafka_tools.dtos.kafka_retry_cache_entry import KafkaRetryCacheEntry
from rouse_kafka_tools.rouse_kafka_tools.generic_retry_client import GenericRetryClient


class TestGenericRetryClient:
    @pytest.fixture
    def valid_cache_entry_data(self):
        return {
            "retry_count": 1,
            "retry_in_progress": True,
            "key": "sample_key",
            "value": "sample_value",
            "topic": "sample_topic",
            "partition": "sample_partition",
            "offset": "sample_offset",
            "first_process_time": "2023-09-05 12:00:00.000001+00:00",
            "last_process_time": "2023-09-05 12:00:00.000001+00:00",
            "retry_interval": 10,
            "headers": {"header1": "value1", "header2": "value2"},
        }

    def test_build_retry_cache_key(self):
        assert "local_listing-retry-cache_test-key" == GenericRetryClient(
            environment_name="local"
        ).build_retry_cache_key("test-key")

    def test_test_retry_cache_availability(self):
        mock_redis = MagicMock()
        mock_redis.ping = MagicMock(return_value=True)
        client = GenericRetryClient(environment_name="local")

        client.test_retry_cache_availability(retry_redis_client=mock_redis)

        mock_redis.ping.assert_called_once()

    @freeze_time("2023-09-05 12:00:00.000001+00:00")
    def test_get_existing_retry_cache_entry_from_message(self, valid_cache_entry_data):
        mock_redis = FakeRedis()
        test_key = "test-key"
        mock_redis.set(f"local_listing-retry-cache_{test_key}", simplejson.dumps(valid_cache_entry_data))
        client = GenericRetryClient(environment_name="local")

        result = client.get_existing_retry_cache_entry_from_message(test_key, retry_redis_client=mock_redis)

        assert result == KafkaRetryCacheEntry(**valid_cache_entry_data)

    @freeze_time("2023-09-06 12:00:00.000001+00:00")
    @pytest.mark.parametrize("raise_exception_flag", [False, True])
    def test_get_existing_retry_cache_entry_from_message_exception_flag(
        self, raise_exception_flag, valid_cache_entry_data
    ):
        mock_redis = FakeRedis()
        test_key = "test-key"
        mock_redis.set(f"local_listing-retry-cache_{test_key}", simplejson.dumps(valid_cache_entry_data))
        client = GenericRetryClient(environment_name="local")

        if raise_exception_flag:
            with pytest.raises(MaxRetryException):
                result = client.get_existing_retry_cache_entry_from_message(
                    test_key, retry_redis_client=mock_redis, raise_max_retry_time_exception=raise_exception_flag
                )
                assert result is None
        else:
            result = client.get_existing_retry_cache_entry_from_message(
                test_key, retry_redis_client=mock_redis, raise_max_retry_time_exception=raise_exception_flag
            )
            assert result == KafkaRetryCacheEntry(**valid_cache_entry_data)

    @freeze_time("2023-09-06 12:00:00.000001+00:00")
    @pytest.mark.parametrize("cache_exists", [False, True])
    @pytest.mark.parametrize(
        "action, expected_action",
        [(None, "unknown"), ("", "unknown"), ("test-metric", "test-metric"), (" test-metric ", "test-metric")],
    )
    def test_upsert_generic_retry_cache_entry_from_message(
        self, action, expected_action, cache_exists, valid_cache_entry_data
    ):
        mock_redis = FakeRedis()
        test_key = valid_cache_entry_data["key"]
        if cache_exists:
            mock_redis.set(f"local_listing-retry-cache_{test_key}", simplejson.dumps(valid_cache_entry_data))
        mock_message = MagicMock()
        mock_message.key = lambda: test_key
        mock_message.value = lambda: valid_cache_entry_data["value"]
        mock_message.topic = lambda: valid_cache_entry_data["topic"]
        mock_message.partition = lambda: valid_cache_entry_data["partition"]
        mock_message.offset = lambda: valid_cache_entry_data["offset"]
        mock_message.headers = lambda: [(k, v) for k, v in valid_cache_entry_data["headers"].items()]
        mock_message.first_process_time = lambda: valid_cache_entry_data["first_process_time"]
        mock_message.last_process_time = lambda: valid_cache_entry_data["last_process_time"]
        mock_message.retry_interval = lambda: valid_cache_entry_data["retry_interval"]
        client = GenericRetryClient(environment_name="local")

        client.upsert_generic_retry_cache_entry_from_message(
            mock_message,
            retry_redis_client=mock_redis,
            raise_max_retry_time_exception=False,
            additional_header_values={"additional_test": "sample"},
            metric_action=action,
            retry_interval=valid_cache_entry_data["retry_interval"]
        )

        assert mock_redis.get(f"local_listing-retry-cache_{test_key}") == bytes(
            simplejson.dumps(
                {
                    "retry_count": valid_cache_entry_data["retry_count"] + 1 if cache_exists else 0,
                    "retry_in_progress": False,
                    "key": "sample_key",
                    "value": "sample_value",
                    "topic": "sample_topic",
                    "partition": "sample_partition",
                    "offset": "sample_offset",
                    "first_process_time": valid_cache_entry_data["first_process_time"]
                    if cache_exists
                    else "2023-09-06 12:00:00.000001+00:00",
                    "last_process_time": "2023-09-06 12:00:00.000001+00:00",
                    "retry_interval": valid_cache_entry_data["retry_interval"],
                    "headers": {"header1": "value1", "header2": "value2", "additional_test": "sample"},
                    "action": expected_action,
                }
            ),
            "utf-8",
        )

    @freeze_time("2023-09-06 12:00:00.000001+00:00")
    @pytest.mark.parametrize("cache_exists", [False, True])
    @pytest.mark.parametrize(
        "action, expected_action",
        [(None, "unknown"), ("", "unknown"), ("test-metric", "test-metric"), (" test-metric ", "test-metric")],
    )
    def test_upsert_generic_retry_cache_entry_from_rouse_kafka_message(
        self, action, expected_action, cache_exists, valid_cache_entry_data
    ):
        mock_redis = FakeRedis()
        test_key = valid_cache_entry_data["key"]
        if cache_exists:
            mock_redis.set(f"local_listing-retry-cache_{test_key}", simplejson.dumps(valid_cache_entry_data))
        mock_message = MagicMock()
        mock_message.key = test_key
        mock_message.value = '{"sample": "value"}'
        mock_message.topic = valid_cache_entry_data["topic"]
        mock_message.partition = valid_cache_entry_data["partition"]
        mock_message.offset = valid_cache_entry_data["offset"]
        mock_message.headers = valid_cache_entry_data["headers"]
        client = GenericRetryClient(environment_name="local")

        client.upsert_generic_retry_cache_entry_from_rouse_kafka_message(
            mock_message,
            retry_redis_client=mock_redis,
            raise_max_retry_time_exception=False,
            additional_header_values={"additional_test": "sample"},
            metric_action=action,
            retry_interval=valid_cache_entry_data["retry_interval"],
        )

        assert mock_redis.get(f"local_listing-retry-cache_{test_key}") == bytes(
            simplejson.dumps(
                {
                    "retry_count": valid_cache_entry_data["retry_count"] + 1 if cache_exists else 0,
                    "retry_in_progress": False,
                    "key": "sample_key",
                    "value": simplejson.dumps('{"sample": "value"}'),
                    "topic": "sample_topic",
                    "partition": "sample_partition",
                    "offset": "sample_offset",
                    "first_process_time": valid_cache_entry_data["first_process_time"]
                    if cache_exists
                    else "2023-09-06 12:00:00.000001+00:00",
                    "last_process_time": "2023-09-06 12:00:00.000001+00:00",
                    "retry_interval": valid_cache_entry_data["retry_interval"],
                    "headers": {"header1": "value1", "header2": "value2", "additional_test": "sample"},
                    "action": expected_action,
                }
            ),
            "utf-8",
        )

    @freeze_time("2023-09-06 12:00:00.000001+00:00")
    def test_delete_generic_retry_cache_entry_from_message(self, valid_cache_entry_data):
        mock_redis = FakeRedis()
        test_key = valid_cache_entry_data["key"]
        test_redis_key = f"local_{LISTING_RETRY_HASH_NAME}_{test_key}"
        mock_redis.set(test_redis_key, simplejson.dumps(valid_cache_entry_data))
        mock_message = MagicMock()
        mock_message.key = lambda: test_key
        mock_message.value = lambda: valid_cache_entry_data["value"]
        mock_message.topic = lambda: valid_cache_entry_data["topic"]
        mock_message.partition = lambda: valid_cache_entry_data["partition"]
        mock_message.offset = lambda: valid_cache_entry_data["offset"]
        mock_message.headers = lambda: [(k, v) for k, v in valid_cache_entry_data["headers"].items()]
        mock_message.first_process_time = lambda: valid_cache_entry_data["first_process_time"]
        mock_message.last_process_time = lambda: valid_cache_entry_data["last_process_time"]
        client = GenericRetryClient(environment_name="local")

        client.delete_generic_retry_cache_entry_from_message(mock_message, mock_redis)

        assert not mock_redis.get(test_redis_key)

    @freeze_time("2023-09-06 12:00:00.000001+00:00")
    def test_delete_generic_retry_cache_entry_from_rouse_kafka_message(self, valid_cache_entry_data):
        mock_redis = FakeRedis()
        test_key = valid_cache_entry_data["key"]
        test_redis_key = f"local_{LISTING_RETRY_HASH_NAME}_{test_key}"
        mock_redis.set(test_redis_key, simplejson.dumps(valid_cache_entry_data))
        mock_message = MagicMock()
        mock_message.key = test_key
        client = GenericRetryClient(environment_name="local")

        client.delete_generic_retry_cache_entry_from_rouse_kafka_message(mock_message, mock_redis)

        assert not mock_redis.get(test_redis_key)

    @freeze_time("2023-09-06 12:00:00.000001+00:00")
    @pytest.mark.parametrize(
        "last_process_time, now, expected_result",
        [
            (datetime.now() - timedelta(seconds=4), datetime.now(), False),
            (datetime.now() - timedelta(seconds=5), datetime.now(), True),
            (datetime.now() - timedelta(seconds=6), datetime.now(), True),
        ],
    )
    def test_is_max_retry_time_reached(self, last_process_time, now, expected_result):
        client = GenericRetryClient(environment_name="local", max_retry_time_seconds=5)
        result = client.is_max_retry_time_reached(last_process_time, now)

        assert result is expected_result
