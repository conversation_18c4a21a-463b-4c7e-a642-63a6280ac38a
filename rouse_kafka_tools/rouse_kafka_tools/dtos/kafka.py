from dataclasses import dataclass, field
from typing import Optional, Union

import simplejson
from confluent_kafka import Message

@dataclass
class RouseKafkaMessage:
    topic: str
    key: str
    partition: int
    offset: int
    error: str
    value: Union[dict, str]
    # tuple of message timestamp type, and timestamp
    # 0: TIMESTAMP_NOT_AVAILABLE - Timestamps not supported by broker.
    # 1: TIMESTAMP_CREATE_TIME - Message creation time (or source / producer time).
    # 2: TIMESTAMP_LOG_APPEND_TIME - Broker receive time.
    timestamp: tuple
    is_value_dict: bool = True
    headers: Optional[dict] = field(default_factory=lambda: {})


class RouseKafkaMessageFactory:
    @staticmethod
    def build_rouse_kafka_message_from_cimpl_message(message: Message, set_value_string: bool=False):
        """
        Build a RouseKafkaMessage from a confluent_kafka Message object.

        Consumers using string serialization for confluent_kafka Message objects can use this function to
        convert the message to a RouseKafkaMessage object. By default the value will be deserialized as JSON, to
        set the value as a string, you may use the set_value_string parameter.

        :param message: The confluent_kafka Message object.
        :param set_value_string: Boolean to designate if message deserialization should be skipped,

        .. warning:: The set_value_string parameter is used internally for building retry cache entries,
                     in most cases, you should not set this to True unless you intend handling message value
                     deserialization yourself.

        :return: A RouseKafkaMessage object.
        """
        return RouseKafkaMessage(
            topic=message.topic(),
            key=message.key(),
            partition=message.partition(),
            offset=message.offset(),
            error=message.error(),
            value=message.value() if set_value_string else simplejson.loads(message.value()),
            is_value_dict=not set_value_string,
            timestamp=message.timestamp(),
            headers={t[0]: t[1] for t in message.headers()} if message.headers() else {},
        )
