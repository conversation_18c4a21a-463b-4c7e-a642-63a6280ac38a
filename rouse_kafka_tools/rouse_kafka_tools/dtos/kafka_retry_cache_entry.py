from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class KafkaRetryCacheEntry:
    retry_count: int
    retry_in_progress: bool
    key: str
    value: str
    topic: str
    partition: str
    offset: str
    first_process_time: datetime
    last_process_time: datetime
    retry_interval: int = None
    headers: Optional[dict] = None
    action: Optional[str] = "unknown"

    def __post_init__(self):
        if type(self.first_process_time) == str:
            self.first_process_time = datetime.strptime(self.first_process_time, "%Y-%m-%d %H:%M:%S.%f%z")
        if type(self.last_process_time) == str:
            self.last_process_time = datetime.strptime(self.last_process_time, "%Y-%m-%d %H:%M:%S.%f%z")
