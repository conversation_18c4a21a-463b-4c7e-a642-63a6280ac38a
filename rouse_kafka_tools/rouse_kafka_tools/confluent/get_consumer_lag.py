import logging
from dataclasses import dataclass

from confluent_kafka import TopicPartition, Consumer

@dataclass
class ConsumerConfig:
    """
    Configuration for the Kafka consumer.
    """
    group_id: str
    bootstrap_servers: str
    sasl_username: str
    sasl_password: str

class ConsumerLagCalculator:
    """
    A class to calculate the consumer lag for a given topic and partition.
    """

    def __init__(self, config: ConsumerConfig):
        self.config = config
        self.consumer = Consumer({
            "group.id": self.config.group_id,
            "bootstrap.servers": self.config.bootstrap_servers,
            "security.protocol": "SASL_SSL",
            "sasl.mechanisms": "PLAIN",
            "sasl.username": self.config.sasl_username,
            "sasl.password": self.config.sasl_password,
            "enable.metrics.push": False,
            "enable.auto.commit": False,
        })

    def __del__(self):
        """
        Ensures the consumer is closed when the object is destroyed.
        """
        self.close_consumer()

    def close_consumer(self):
        """
        Closes the Kafka consumer.
        """
        if self.consumer:
            try:
                self.consumer.close()
            except Exception as e:
                logging.error(f"Error closing consumer: {e}")
            finally:
                self.consumer = None

    def get_consumer_lag(self, topic: str, partition: int) -> int:
        """
        Calculates the consumer lag for a specific topic and partition.
        """
        topic_partition = TopicPartition(topic, partition)
        committed_offset = self.consumer.committed([topic_partition])[0].offset

        if committed_offset is None:
            return 0

        end_offset = self.consumer.get_watermark_offsets(topic_partition)[1]
        return end_offset - committed_offset

    def get_all_partitions(self, topic_name: str):
        """
        Fetches all partition IDs for a given topic.
        """
        cluster_metadata = self.consumer.list_topics(topic_name)
        topic_metadata = cluster_metadata.topics[topic_name]
        return list(topic_metadata.partitions.keys())

    def get_consumer_lag_for_all_partitions(self, topic_name: str):
        """
        Fetches consumer lag for all partitions of a given topic.
        """
        try:
            partitions = self.get_all_partitions(topic_name)
            lags = {}
            for partition in partitions:
                lag = self.get_consumer_lag(topic_name, partition)
                lags[partition] = lag
            return lags
        except Exception as e:
            logging.error(f"Error fetching consumer lag: {e}")
