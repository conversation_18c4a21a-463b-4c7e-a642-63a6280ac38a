from dataclasses import asdict
from datetime import datetime, timezone
from logging import Logger, get<PERSON><PERSON>ger
from typing import Optional

import redis
import simple<PERSON><PERSON>
from confluent_kafka import Message

from .dtos.kafka import RouseKafkaMessage, RouseKafkaMessageFactory
from .config import (
    ENVIRONMENT_CONFIG_MAP,
    LISTING_RETRY_HASH_NAME,
    GenericRetryClientConfig,
    RETRY_CACHE_TTL,
)
from .dtos.exceptions import MaxRetryException
from .dtos.kafka_retry_cache_entry import KafkaRetryCacheEntry
from .utils import DataClassUnpack


class GenericRetryClient:
    """
    The `GenericRetryClient` class provides a mechanism for handling message retries in a Kafka-based system.
    It is designed to manage the retry state of messages by storing relevant information in a Redis cache.

    Parameters:
        - environment_name (str): The name of the environment for which the retry client is instantiated.
        - redis_host (Optional[str]): The host address of the Redis server. If not provided, the environment mapping is
        used.
        - redis_port (Optional[int]): The port number of the Redis server. If not provided, the environment mapping is
        used.
        - redis_db (Optional[int]): The Redis database number. If not provided, the environment mapping is used.
        - max_retry_time_seconds (Optional[int]): The maximum time in seconds allowed for message retries. If not
        provided, a default value is used.

    Attributes:
        - config (GenericRetryClientConfig): A configuration object that stores the retry client's settings.
        - logger (Logger): A logging instance for capturing client-related information.
    """

    DEFAULT_METRIC_ACTION = "unknown"

    config: GenericRetryClientConfig
    logger: Logger

    def __init__(
        self,
        environment_name: str,
        redis_host: Optional[str] = None,
        redis_port: Optional[int] = None,
        redis_db: Optional[int] = None,
        max_retry_time_seconds: Optional[int] = None,
    ):
        """
        Initialize the GenericRetryClient.

        Parameters:
            - environment_name (str): The name of the environment for which the retry client is instantiated.
            - redis_host (Optional[str]): The host address of the Redis server.
            - redis_port (Optional[int]): The port number of the Redis server.
            - redis_db (Optional[int]): The Redis database number.
            - max_retry_time_seconds (Optional[int]): The maximum time in seconds allowed for message retries.
        """
        self.logger = getLogger()

        config_dict = ENVIRONMENT_CONFIG_MAP.get(environment_name, ENVIRONMENT_CONFIG_MAP["develop"]).copy()
        if redis_host:
            config_dict["redis_host"] = redis_host
        if redis_port:
            config_dict["redis_port"] = redis_port
        if redis_db:
            config_dict["redis_db"] = redis_db
        if max_retry_time_seconds:
            config_dict["max_retry_time_seconds"] = max_retry_time_seconds

        self.config = GenericRetryClientConfig(environment_name=environment_name, **config_dict)

    def build_retry_cache_key(self, message_key):
        """
        Construct a retry cache key for a given message key.

        Parameters:
            - message_key (str): The key associated with the Kafka message.

        Returns:
            - str: The constructed retry cache key.
        """
        return f"{self.config.environment_name}_{LISTING_RETRY_HASH_NAME}_{message_key}"

    def test_retry_cache_availability(self, retry_redis_client: Optional[redis.StrictRedis] = None):
        retry_redis_client: redis.StrictRedis = (
            retry_redis_client
            if retry_redis_client
            else redis.StrictRedis(host=self.config.redis_host, port=self.config.redis_port, db=self.config.redis_db)
        )
        retry_redis_client.ping()

    def get_existing_retry_cache_entry_from_message(
        self,
        message_key: str,
        raise_max_retry_time_exception: bool = True,
        retry_redis_client: Optional[redis.StrictRedis] = None,
    ):
        """
        Retrieve an existing retry cache entry for a message and validate the maximum retry time.

        Parameters:
            - message_key (str): The Kafka message key for which to retrieve the retry cache entry.
            - raise_max_retry_time_exception (bool): If True, raises a MaxRetryException if the maximum retry time is
            exceeded.
            - retry_redis_client (Optional[redis.StrictRedis]): The Redis client. If not provided, a new client is
            created.

        Returns:
            - Optional[KafkaRetryCacheEntry]: The existing retry cache entry if found, None otherwise.
        """
        retry_redis_client: redis.StrictRedis = (
            retry_redis_client
            if retry_redis_client
            else redis.StrictRedis(host=self.config.redis_host, port=self.config.redis_port, db=self.config.redis_db)
        )
        retry_cache_key = self.build_retry_cache_key(message_key=message_key)
        now = datetime.now(timezone.utc)

        if existing_retry_cache_str := retry_redis_client.get(retry_cache_key):
            retry_cache_dict = simplejson.loads(existing_retry_cache_str)
            first_process_time = datetime.strptime(
                retry_cache_dict.get("first_process_time", str(now)), "%Y-%m-%d %H:%M:%S.%f%z"
            )
            if raise_max_retry_time_exception and self.is_max_retry_time_reached(first_process_time, now):
                self.logger.error(
                    "Max message retry time reached for listing create/update/delete consumer",
                    extra={
                        "message_key": message_key,
                        "retry_cache": retry_cache_dict,
                        "max_retry_time_seconds": self.config.max_retry_time_seconds,
                    },
                )
                retry_redis_client.delete(retry_cache_key)
                raise MaxRetryException(f"Max retry time exceeded")
            return DataClassUnpack.instantiate(KafkaRetryCacheEntry, retry_cache_dict)
        return None

    def upsert_generic_retry_cache_entry_from_message(
        self,
        message: Message,
        raise_max_retry_time_exception: bool = True,
        retry_redis_client: Optional[redis.StrictRedis] = None,
        additional_header_values: Optional[dict] = None,
        metric_action: Optional[str] = DEFAULT_METRIC_ACTION,
        retry_interval: int = None,
    ):
        """
        Update or create a retry cache entry for a message, including retry count and timestamps.

        Parameters:
            - message (Message): The Kafka message for which to update or insert the retry cache entry.
            - raise_max_retry_time_exception (bool): If True, raises a MaxRetryException if the maximum retry time is
            exceeded.
            - retry_redis_client (Optional[redis.StrictRedis]): The Redis client. If not provided, a new client is
            created.
        """
        rouse_kafka_message = RouseKafkaMessageFactory.build_rouse_kafka_message_from_cimpl_message(message, set_value_string=True)
        self.upsert_generic_retry_cache_entry_from_rouse_kafka_message(
            rouse_kafka_message,
            raise_max_retry_time_exception,
            retry_redis_client,
            additional_header_values,
            metric_action,
            retry_interval
        )

    def upsert_generic_retry_cache_entry_from_rouse_kafka_message(
        self,
        message: RouseKafkaMessage,
        raise_max_retry_time_exception: bool = True,
        retry_redis_client: Optional[redis.StrictRedis] = None,
        additional_header_values: Optional[dict] = None,
        metric_action: Optional[str] = DEFAULT_METRIC_ACTION,
        retry_interval: int = None,
    ):
        """
        Update or create a retry cache entry for a RouseKafkaMessage, including retry count and timestamps.

        Parameters:
            - message (RouseKafkaMessage): The RouseKafkaMessage  for which to update or insert the retry cache entry.
            - raise_max_retry_time_exception (bool): If True, raises a MaxRetryException if the maximum retry time is
            exceeded.
            - retry_redis_client (Optional[redis.StrictRedis]): The Redis client. If not provided, a new client is
            created.
        """
        retry_redis_client: redis.StrictRedis = (
            retry_redis_client
            if retry_redis_client
            else redis.StrictRedis(host=self.config.redis_host, port=self.config.redis_port, db=self.config.redis_db)
        )
        retry_cache_key = self.build_retry_cache_key(message_key=message.key)
        now = datetime.now(timezone.utc)

        if existing_retry_cache := self.get_existing_retry_cache_entry_from_message(
            message_key=message.key,
            retry_redis_client=retry_redis_client,
            raise_max_retry_time_exception=raise_max_retry_time_exception,
        ):
            self.logger.debug(
                "Found existing cache entry", extra={"key": retry_cache_key, "content": asdict(existing_retry_cache)}
            )
            first_process_time = existing_retry_cache.first_process_time
            retry_count = existing_retry_cache.retry_count + 1
        else:
            first_process_time = now
            retry_count = 0

        headers = {k: v for k, v in message.headers.items()} if message.headers else {}
        if additional_header_values:
            headers.update(additional_header_values)

        retry_cache_entry: KafkaRetryCacheEntry = KafkaRetryCacheEntry(
            retry_count=retry_count,
            retry_in_progress=False,
            key=message.key,
            value=simplejson.dumps(message.value) if message.is_value_dict else message.value,
            topic=message.topic,
            partition=str(message.partition),
            offset=str(message.offset),
            first_process_time=first_process_time,
            last_process_time=now,
            retry_interval=retry_interval,
            action=metric_action.strip()
            if metric_action and metric_action.strip() != ""
            else self.DEFAULT_METRIC_ACTION,
            headers=headers,
        )
        self.logger.debug(
            "Set retry cache entry", extra={"dataclass": asdict(retry_cache_entry), "key": retry_cache_key}
        )
        retry_redis_client.set(
            name=retry_cache_key, value=simplejson.dumps(asdict(retry_cache_entry), default=str), ex=RETRY_CACHE_TTL
        )

    def delete_generic_retry_cache_entry_from_message(
        self, message: Message, retry_redis_client: Optional[redis.StrictRedis] = None
    ):
        rouse_kafka_message = RouseKafkaMessageFactory.build_rouse_kafka_message_from_cimpl_message(message, set_value_string=True)
        self.delete_generic_retry_cache_entry_from_rouse_kafka_message(rouse_kafka_message, retry_redis_client)

    def delete_generic_retry_cache_entry_from_rouse_kafka_message(
        self, message: RouseKafkaMessage, retry_redis_client: Optional[redis.StrictRedis] = None
    ):
        retry_redis_client: redis.StrictRedis = (
            retry_redis_client
            if retry_redis_client
            else redis.StrictRedis(host=self.config.redis_host, port=self.config.redis_port, db=self.config.redis_db)
        )
        retry_cache_key = self.build_retry_cache_key(message_key=message.key)
        retry_redis_client.delete(retry_cache_key)

    def is_max_retry_time_reached(
        self,
        first_process_time: datetime,
        now: datetime,
    ):
        return (now - first_process_time).total_seconds() > self.config.max_retry_time_seconds
