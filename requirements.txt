Django==4.2.21
django-environ==0.8.1
django-reversion==5.0.0
djangorestframework==3.15.2
django-filter==21.1
django-storages[google]==1.12.3
django-admin-thumbnails==0.2.6
django-widget-tweaks==1.4.12

pytz==2020.1
sqlparse==0.5.0
asgiref==3.7.*

#algo requirements
tensorflow==2.13.0

numpy==1.24.0
pandas==1.5.1
db-dtypes  #vs not installed
xgboost==1.7.6 # vs 1.7.6
lightgbm==4.6.0 #vs not installed
google-api-python-client
google-cloud-storage==2.5.0
google-cloud-bigquery==3.30.0
pyarrow==14.0.1 # NOTE: required by bigquery can caused issues with numpy
google-cloud-compute== 1.5.1
psutil
pandarallel
Flask==2.3.3
flask-restplus==0.13.0
flask-restx==1.0.6
werkzeug==3.0.6  # NOTE: usually cause problems when gets updated. Breaks Flask packages
distro
imbalanced-learn==0.12.3
sendgrid==6.11.0
python-dotenv==0.21.1
scikit-learn==1.5.0



dataclasses-json==0.5.5
python-dateutil==2.8.2
requests==2.32.0

#deployment
gunicorn==23.0.0
google-cloud-sqlcommenter==1.1.0

#import-export
django-import-export==2.5.0
tablib==3.0.0
werkzeug>=3.0.6
cryptography==44.0.1
grpcio==1.68.1

#tests
pytest==7.2.0
pytest-django==3.10.0

# Rouse's
rouse-platform-logging~=1.0.2.dev526
rouse-observability-tools-services==0.0.11
rouse-values-library==1.1.248

# Algo DAG integration
fastavro==1.8.2
python-snappy==0.6.1
