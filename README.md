# infrastructure-v2

## Environment setup

In order to run external sources on some projects, you need to have virtual
environment set up and activated

```shell
virtualenv .environment --python=python3
source .environment/bin/activate
pip install -r requirements.txt
```

## Reserved IPs

We are reserving certain IP spaces for future and current use

- **********/18 were reserved for Rackspace IP space, but no longer in use
- ********/16 - reserved for Rouse Office network
  - ************/29 - filestore-peer-640431405483
- ********/28 - Backups Prod VPC connector
- **********/24 - used on the Cogent VPN tunnel
- **********/24 - used on the Cogent VPN tunnel
- *************/29 - filestore-peer
- **********/16 - reserved for Redis Memory Store instances
  - **********/29 - gcacheiqa01
  - **********/29 - gcachepreprod01
  - ***********/29 - gweb04catalogs
  - ***********/29 - services dev sales portal on GKE
  - ***********/29 - gcacheprod01
  - ***********/29 - services prod sales portal on GKE HA
  - ***********/29 - services dev rdo api on GKE
  - ***********/29 - services prod rdo api on GKE
  - ***********/29 - services dev valuations-service-cache on GKE
  - ***********/29 - services prod valuations-service-cache on GKE
  - **********0/29 - services dev sales-rfm-channels-api cache
  - **********8/29 - services prod  sales-rfm-channels-api cache
  - ***********/29 - services dev valuation-classification cache
  - 10.127.0.104/29 - services prod valuation-classification cache
  - 10.127.0.112/29 - services prod sales-fleet-manager-cache-prod
  - 10.127.0.120/29 - services dev sales-fleet-manager-cache-dev
  - 10.127.0.128/29 - service dev sales-ingest-cache
  - 10.127.0.136/29 - service prod sales-ingest-cache
  - 10.127.0.144/29 - service dev fleet-manager-integrations-cache
  - 10.127.0.152/29 - services prod rdo-dataservice std cache
  - ***********0/29 - service dev sales-catalog-cache
  - ***********8/29 - services prod sales-portal-stdcache
  - 10.127.0.176/29 - service dev sales-rfm-cache
  - 10.127.0.184/29 - service prod honeycomb-refinery-std
  - 10.127.0.192/29 - service dev honeycomb-refinery
  - 10.127.0.200/29 - services prod kafka-retry-service-std-cache
  - 10.127.0.208/29 - service dev kafka-retry-service-cache
  - 10.127.0.216/29 - service prod rdo-data-service-std-cache
  - 10.127.0.224/29 - service dev enterprise-media-service
  - 10.127.0.232/29 - service prod enterprise-media-service
  - ***********0/29 - service dev enterprise-domain-service-cache
  - ***********8/29 - service prod enterprise-domain-service-cache
  - 10.127.1.40/29 - services prod sales-catalog-stdcache
  - 10.127.1.32/29 - service dev sales-fmx-api-cache
  - 10.127.1.64/29 - DR-Prod valuations-service-cache on GKE
  - 10.127.1.72/29 - DR-Prod valuation-classification-cache on GKE
  - 10.127.1.80/29 - DR-Prod rfm-channels-api cache on GKE
  - 10.127.1.88/29 - DR-Prod sales-ingest cache on GKE
  - 10.127.1.96/29 - services-dev rdo-dataservice cache
  - 10.127.1.104/29 - services-prod sales-rfm-stdcache
  - 10.127.1.112/29 - DR-Prod enterprise-domain-service cache
  - 10.127.1.120/29 - DR-Prod domain-service cache
  - 10.127.1.128/29 - DR-Prod fleet-manager-integrations cache
  - 10.127.1.136/29 - DR-Prod sales-rfm-api cache
  - 10.127.1.160/29 - services dev sales-portal-cache
  - 10.127.1.184/29 - services prod fleet-manager-integrations-std-cache
  - 10.127.1.200/29 - services prod sales-fmx-api-stdcache
  - 10.127.1.232/29 - services prod sales-notification-service-stdcache
  - 10.127.1.192/29 - services dev sales-notification-service-cache
  - 10.127.4.0/29 - services ppe sales-rfm-api-std-cache
  - 10.127.4.8/29 - services ppe fleet-manager-integrations-std-cache
  - 10.127.4.16/29 - services ppe rfm-channels-api-std-cache

- 10.128.0.0/16 is reserved for VPN subnet in Migration infrastructure
  - 10.128.0.0/20 - datalab-network
  - 10.128.0.0/22 - shared-net-private-do-not-use

- 10.129.0.0/16 are reserved for subnets in shared production and development networks in Migration infrastructure
  - **********/22 - project-382399521286-129
  - ***********/20
  - ***********/19

- **********/16 are reserved for subnets in shared production and development networks in Migration infrastructure
  - **********/22 - project-834467151834-130
  - ***********/20
  - ***********/19

- **********/16 is reserved for Platform Engineering kubernetes network

- *********/16 is reserved for regular project networks in Dev
  - 0 - CloudSQL Private IPs Rouse Shared network
  - 1 - *********/24 - IT Dev
  - 2 - *********/24 - Reserved - (Previous Devops Tools Dev)
  - 3 - *********/24 - VMS-Dev
  - 4 - *********/24 - CloudSQL Private IPs Platform services Dev
  - 5 - *********/24 - CloudSQL Private IPs Platform services Dev Postgres
  - 6 - *********/24 - Data VMs Dev
  - 7 - *********/24 - CloudSQL Private IPs Platform services v2 Dev
  - 8 - Reserved - CloudSQL Private IPs Platform services v2 Dev Postgres
  - 9 - *********/24 - Internal LB subnet Platform services v2 Dev
  - 10 - **********/24 - Finance VMS Dev
  - 11 - **********/24 - Internal LB Subnet Platform Services Dev
  - 12 - **********/24 - Algo VMS Dev
  - 13 - Reserved
  - 14 - **********/24 - CloudSQL Private IPs Platform Management Dev
  - 15 - Reserved - RS Backups Network
  - 16 - **********/24 - Dev Dataflow Jobs
  - 17 - **********/24 - Algo AI Dev
  - 18 - **********/28 - RDO Dev
  - 19 - **********/24 - Stage public api subnet for apigee
  - 20 - **********/24 - DCS api subnet for apigee
  - 21 - **********/24 - Shared VPC subnet for Appraisals Data Dev
  - 22 - **********/24 - Datastream for Appraisals Data Dev
  - 23 - **********/24 - User Service subnet for apigee
  - 24 - **********/24 - develop-fmx-api for apigee
  - 25 - **********/24 - europe-west2 subnet in shared network
  - 26 - **********/24 - Internal LB Subnet Platform Services Dev for europe-west2 region

- *********/16 is reserved for regular project networks in Prod
  - 0 - Reserved
  - 1 - *********/24 Images Prod
  - 2 - Reserved - Previous Devops Tools Prod
  - 3 - *********/24 - VMS-Prod
  - 4 - *********/24 - CloudSQL Private IPs Platform services Prod
  - 5 - *********/24 - CloudSQL Private IPs Platform services Prod Postgres
  - 6 - *********/24 - CloudSQL Private IPs Platform services v2 Prod
  - 7 - Reserved - CloudSQL Private IPs Platform services v2 Prod Postgres
  - 8 - *********/24 - Internal LB subnet Platform services v2 Prod
  - 9 - *********/24 - Finance VMS prod network
  - 10 - **********/24 - Internal LB subnet Platform Services Prod
  - 11 - Reserved
  - 12 - **********/24 - Algo VMS Prod
  - 13 - Reserved
  - 14 - **********/24 - CloudSQL Private IPs Platform Management Prod
  - 15 - **********/24 - DR Prod
  - 16 - **********/24 - Backups Prod
  - 17 - **********/24 - Forseti Prod
  - 18 - **********/24 - Internal LB Subnet Platform Services DR
  - 19 - **********/24 - Prod Dataflow Jobs
  - 20 - **********/24 - Algo AI Prod
  - 21 - **********/24 - Algo VMS Prod - us-east1
  - 22 - **********/24 - Algo VMS Prod - us-west1
  - 23 - **********/24 - Algo VMS Prod - us-west4
  - 24 - **********/28 - RDO Prod
  - 25 - **********/24 - Shared VPC subnet for Appraisals Data Prod
  - 26 - **********/24 - Datastream for Appraisals Data Prod
  - 27 - **********/24 - DR Prod service networking Postgres
  - 28 - **********/24 - DR Prod service networking SQL Server
  - 29 - **********/24 - Services Subnet for Platform Engineering for us-central1 region
  - 30 - **********/24 - Internal LB Subnet for Platform Engineering for us-central1 region
  - 31 - **********/24 - DR Prod service networking AlloyDB

- *********/16 is reserved for Kubernetes individual project networks in Dev
  - 10.98.128.0/21 - free
  - 10.98.160.0/20 - free
  - 10.98.176.0/20 - free
  - 10.98.136.0/21 - default-services
  - 10.98.144.0/20 - default-pods
  - 10.98.192.0/18 - workload-identity-pods
  - *********/17 - castai

- 10.99.0.0/16 is reserved for Kubernetes individual project networks in Prod
  - 10.99.0.0/18 - spot-workload-identity platform-valuation node pool
  - 10.99.64.0/18 - spot workload node pool
  - 10.99.192.0/18 - platform-valuation pods
  - 10.99.160.0/19 - workload-identity-v2 node pool
  - 10.99.144.0/20 - default-pods
  - 10.99.136.0/21 default services
  - 10.99.128.0/21 - services-kubernetes-prod-subnet

- 10.100.0.0/16 is reserved for Kubernetes projects in new Shared Dev network
- 10.101.0.0/16 is reserved for regular projects in new Shared Dev network
  - 0 - VMs Dev
  - 10.101.0.0/24 - shared-network-dev-71f46a-vms-dev-39fe7a

- 10.102.0.0/16 is reserved for Kubernetes projects in new Shared Prod network

- 10.103.0.0/16 is reserved for regular projects in new Shared Prod network

- 10.104.0.0/16 is reserved for Kubernetes composer jobs v3 in Platform Managment Dev
  - 10.104.0.0/18 - composer-jobs-v3-subnet
  - 10.104.64.0/18 - composer-jobs-services
  - 10.104.128.0/17 - composer-jobs-pods

- 10.105.0.0/16 is reserved for Kubernetes composer jobs v3 in Platform Managment Prod
  - 10.105.0.0/18 - composer-jobs-v3-subnet
  - 10.105.64.0/18 - composer-jobs-services
  - 10.105.128.0/17 - composer-jobs-pods

- 10.106.0.0/16 is reserved for Kubernetes Services v2 in Platform Services Dev
  - 10.106.0.0/18 - services-kubernetes-v2-dev-subnet
  - 10.106.64.0/18 - default-services
  - 10.106.128.0/17 - default-pods

- 10.107.0.0/16 is reserved for Kubernetes Services v2 in Platform Services Prod
  - 10.107.0.0/18 - services-kubernetes-v2-prod-subnet
  - 10.107.64.0/18 - default-services
  - 10.107.128.0/17 - default-pods

- 10.108.0.0/16 is reserved for Astronomer Kubernetes in Platform Management Dev
  - 10.108.0.0/18 - astronomer-subnet
  - 10.108.64.0/18 - astronomer-services
  - 10.108.128.0/17 - astronomer-pods

- 10.109.0.0/16 is reserved for Astronomer Kubernetes in Platform Management Prod
  - 10.109.0.0/18 - astronomer-subnet
  - 10.109.64.0/18 - astronomer-services
  - 10.109.128.0/17 - astronomer-pods

- 10.110.0.0/16 is reserved for DR Kubernetes in VMS DR Prod
  - 10.110.0.0/18 - dr-services-subnet
  - 10.110.64.0/18 - dr-services-services
  - 10.110.128.0/17 - dr-services-pods

- 10.111.0.0/16 is reserved for Kubernetes in RB Connect Dev
  - 10.111.0.0/18 - jobs-cluster-subnet
  - 10.111.64.0/18 - jobs-cluster-services
  - 10.111.128.0/17 - jobs-cluster-pods

- 10.112.0.0/16 is reserved for Kubernetes in RB Connect Prod
  - 10.112.0.0/18 - jobs-cluster-subnet
  - 10.112.64.0/18 - jobs-cluster-services
  - ************/17 - jobs-cluster-pods

- **********/16 - is reserved for AlloyDB in services dev

- **********/16 is reserved for Kubernetes jobs in DR Prod
  - **********/18 - dr-jobs-subnet
  - ***********/18 - dr-jobs-services
  - ************/17 - dr-jobs-pods

- **********/16 - Vertex AI Algo Endpoints Dev
- **********/16 - Vertex AI Algo Endpoints Prod
- **********/22 - IDS servers
- **********/22 - Testing network for mirroring traffic to IDS.
- **********/16 - Management Dev SmartEquip Private Cluster
- **********/16 - Management Prod SmartEquip Private Cluster

- **********/16 - is reserved for AlloyDB in services dev
- **********/16 - europe-west2-services-dev k8 cluster
- **********/18 - australia-southeast1 services dev
- **********/16 - is reserved for regular project networks in PPE
  - 4 - **********/24 - CloudSQL Private IPs Platform services PPE
  - 5 - **********/24 - CloudSQL Private IPs Platform services PPE Postgres
  - 10 - ***********/24 - Internal LB subnet Platform Services PPE
- **********/16 - is reserved for Kubernetes individual project networks in PPE
  - **********/18 - spot-workload-identity platform-valuation node pool
  - ************/18 - platform-valuation pods

## New project creation

To create and manage new project, you would need to create a GCP project resource in `gcp/global/project-structure`.
It is recommended to use `gcp/modules/structure/project/v1` module. Once you
created a project, add it to respective outputs following existing examples.

Use short project id without random hash, to create a new directory structure.
For example, if you've created "logs-dev" project in "infrastructure/dev"
folders in GCP, run `make infrastructure/dev/logs-dev` from `gcp/` folder in
the repo.

You might need to install `envsubst` utility for make to succeed. To install it
on Mac run

```bash
brew install gettext
brew link --force gettext
```

Once the directory structure is created with basic files, just use normal
terraform commands from the project folder

```bash
terraform init
terraform plan
terraform apply
```

## Workload identity

In order to create and manage Workload Identity and the related permissions, the following documents are available:

- [Data - Workload Identity](gcp/docs/data-workload-identity.md)

- [Services - Workload Identity](gcp/docs/services-workload-identity.md)

## Devops DataStudio Reports

### Vertical Pod Autoscaler (VPA) recommendations

- [services-dev](https://datastudio.google.com/reporting/478dbb96-3eb6-45b8-b5e2-2928a72a79ba)
- [services-prod](https://datastudio.google.com/reporting/c412a4da-be1b-429b-8726-a6040438eea2)

### GKE error event logs

- [services-dev](https://datastudio.google.com/reporting/f0518cd2-b4b9-4fa6-8659-e7557f15988d)
- [services-prod](https://datastudio.google.com/reporting/e9f938bc-77ad-46e0-8a07-33bbd9f164f5)

### GKE costs allocation / usage metering

- [services-dev](https://datastudio.google.com/reporting/5052b92a-88d1-4139-bc2a-2541fea991da)
- [services-prod](https://datastudio.google.com/reporting/cc39b711-852c-4e8c-bf8a-a626bb11297a)
