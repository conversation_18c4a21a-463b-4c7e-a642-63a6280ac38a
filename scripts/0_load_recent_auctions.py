#!/usr/bin/env python

import argparse
import time
import pandas as pd
from google.cloud import bigquery
from typing import List, Optional
import numpy as np

def load_recent_auctions( start_date: str, end_date: str,model_artifact_version: str) -> pd.DataFrame:
    QUERY_STR = f"""
        SELECT * 
        FROM (
            SELECT 
                    *,
                    row_number() over(partition by assetGUID order by date desc) as rn
            FROM (
                SELECT 
                    DISTINCT * 
                FROM `algo-vms-prod-bd7bdf.algo_vms_logs.batchPredict`
                WHERE listingCloseDate between '{start_date}' and '{end_date}'
                    AND runtimeVersion = '{model_artifact_version}'
                    AND saleType IN ('Live', 'Onsite Auction', 'Online Auction', 'Featured Auction')
                    AND usageUnit != 'Not Available'
                    AND LOWER(condition) NOT IN ('not viewed','not rated')
                    AND (modelName is not null or modelName != '')
                    --AND country = 'DEU'
                    --AND assetCategory IN ('Agricultural Tractors', 'Wheel Loaders', 'Excavators', 'Trucks - Dump')

                )
            ) AS A
        WHERE rn=1
        -- LIMIT 50; -- NOTE: for debugging purposes only
    """

    print()
    print(QUERY_STR)
    print()

    client = bigquery.Client()
    query_job = client.query(QUERY_STR)

    data = query_job.result().to_dataframe()
   
    return data


def main(start_date: str, end_date: str, model_artifact_version: str) -> pd.DataFrame:

    start_time = time.time()

    data = load_recent_auctions(start_date, end_date,model_artifact_version)

    end_time = time.time()
    _duration_in_minute = (end_time - start_time) / 60.0
    print(f"start_time: {start_time} -- end_time: {end_time}; duration in minutes: {_duration_in_minute}; data size: {data.shape}")

    return data

if __name__ == "__main__":

    """
    Example: how to call this script from the command line:
    python 0_load_recent_auctions.py data/recent_auctions.csv --start-date 2021-08-01 --end-date 2021-08-31 --model-artifact-version 1.0.0
    """

    parser = argparse.ArgumentParser(description="Load auction data form bigquery.")
    parser.add_argument("csv_file", type=str, help="Path to the CSV file to SAVE.")
    parser.add_argument("--start-date", type=str, help="Start date for the query. Ex: 2021-08-01")
    parser.add_argument("--end-date", type=str, help="End date for the query. Ex: 2021-08-31")
    parser.add_argument("--model-artifact-version", type=str, help="Model artifact version. Ex: 1.0.0")

    args = parser.parse_args()

    print(f"start_date: {args.start_date} --end_date: {args.end_date} --model_artifact_version: {args.model_artifact_version}")
    print()
    data = main(args.start_date, args.end_date,args.model_artifact_version)

    print(f"shape: {data.shape}")

    if args.csv_file:
        data.to_csv(args.csv_file)
        print(f"Data saved to CSV: {args.csv_file}")
