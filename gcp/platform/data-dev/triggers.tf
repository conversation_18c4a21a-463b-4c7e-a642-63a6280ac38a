module "cloud_functions_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "cloud-function-scripts"
  project_name = local.project
  region       = var.region
}

resource "google_storage_bucket" "data_dev_eft_raw_files" {
  name          = "data-dev-f1d99f-eft-raw-files"
  location      = "us-central1"
  force_destroy = true
  uniform_bucket_level_access = false
  encryption {
    default_kms_key_name = "projects/data-dev-f1d99f/locations/us-central1/keyRings/data-dev-f1d99f-eft-raw-files/cryptoKeys/data-dev-f1d99f-eft-raw-files"
  }
}

import {
  id = "data-dev-f1d99f/data-dev-f1d99f-eft-raw-files"
  to = google_storage_bucket.data_dev_eft_raw_files
}


resource "google_service_account" "cf_runner" {
  account_id   = "cf-gcs-runner"
  display_name = "Cloud Function runner for GCS events"
}

resource "google_project_iam_member" "cf_runner_event_receiver" {
  project = local.project
  role    = "roles/eventarc.eventReceiver"
  member  = "serviceAccount:${google_service_account.cf_runner.email}"
}

data "google_storage_project_service_account" "gcs_agent" {
  project = local.project
}

resource "google_project_iam_member" "gcs_pubsub_publisher" {
  project = local.project
  role    = "roles/pubsub.publisher"
  member  = "serviceAccount:${data.google_storage_project_service_account.gcs_agent.email_address}"
}

resource "google_cloud_run_service_iam_member" "eventarc_invoker" {
  location = var.region
  service  = google_cloudfunctions2_function.data_dev_eft_raw_files_processor.service_config[0].service
  role     = "roles/run.invoker"
  member   = "serviceAccount:${google_service_account.cf_runner.email}"
}

data "google_project" "project" {
  project_id = local.project
}

resource "google_storage_bucket_iam_member" "read_objects" {
  bucket = google_storage_bucket.data_dev_eft_raw_files.name
  role   = "roles/storage.objectViewer"
  member = "serviceAccount:${google_service_account.cf_runner.email}"
}

resource "google_cloudfunctions2_function" "data_dev_eft_raw_files_processor" {
  name        = "data_dev_eft_raw_files"
  location    = var.region
  description = "Triggered when a new file lands in the bucket"

  build_config {
    docker_repository = "projects/data-dev-f1d99f/locations/us-central1/repositories/gcf-artifacts"
    entry_point = "data_dev_handler"
    runtime     = "python312"
    source {
      storage_source {
        bucket = module.cloud_functions_bucket.bucket_name
        object ="data_dev_eft_raw_files.zip"
      }
    }
  }
  

  service_config {
    max_instance_count = 10
    available_memory   = "512M"
    timeout_seconds    = 60
    ingress_settings   = "ALLOW_INTERNAL_ONLY"
    service_account_email = google_service_account.cf_runner.email
    environment_variables = {
      "LOG_EXECUTION_ID" = "true"
    }
  }
}

resource "google_eventarc_trigger" "gcs_object_finalize" {
  name     = "eft-raw-files-bucket-trigger"
  location = var.region 

  matching_criteria {
    attribute = "type"
    value     = "google.cloud.storage.object.v1.finalized"
  }

  matching_criteria {
    attribute = "bucket"
    value     = google_storage_bucket.data_dev_eft_raw_files.name
  }

  destination {
    cloud_run_service {
      service = google_cloudfunctions2_function.data_dev_eft_raw_files_processor.service_config[0].service
      region  = var.region
    }
  }

  service_account = google_service_account.cf_runner.email
}
