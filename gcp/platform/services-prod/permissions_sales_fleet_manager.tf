locals {
  sales_fleet_manager_account_email = google_service_account.workload_identity["sales-fleet-manager"].email
}

resource "google_project_iam_member" "sales_fleet_manager_permissions" {
  for_each = toset([
    "roles/clouddebugger.agent",
    "roles/cloudprofiler.agent",
    "roles/cloudtrace.agent",
    "roles/logging.logWriter",
    "roles/pubsub.publisher",
    "roles/storage.objectAdmin",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${local.sales_fleet_manager_account_email}"
}

resource "google_project_iam_member" "sales_fleet_manager_appraisals_data_permissions" {
  for_each = toset([
    "roles/pubsub.publisher",
  ])
  project = local.appraisals_data
  role    = each.key
  member  = "serviceAccount:${local.sales_fleet_manager_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_fleet_manager_secret_permissions" {
  for_each = toset([
    data.google_secret_manager_secret_version.confluent-kafka-prod-sa-key.secret,
    data.google_secret_manager_secret_version.confluent-kafka-prod-sa-schema-reg-key.secret,
    data.google_secret_manager_secret_version.confluent-kafka-prod-sa-schema-reg-secret.secret,
    data.google_secret_manager_secret_version.confluent-kafka-prod-sa-secret.secret,
    data.google_secret_manager_secret_version.platform_classification_api_auth.secret,
    data.google_secret_manager_secret_version.platform_user_config_api_auth.secret,
    data.google_secret_manager_secret_version.sales_classification_api_auth.secret,
    data.google_secret_manager_secret_version.sales_fleet_manager_integrations_api_auth.secret,
    data.google_secret_manager_secret_version.sales_rfm_api_static_auth_token.secret,
    data.google_secret_manager_secret_version.sales_fleet_manager_api_auth.secret,
  ])
  project   = local.project
  role      = "roles/secretmanager.secretAccessor"
  secret_id = each.value
  member    = "serviceAccount:${local.sales_fleet_manager_account_email}"
}

# needs access to the platform-data project to read canary records for pre-flight deployment testing
resource "google_project_iam_member" "sales_fleet_manager_bigquery_data_prod_project_permissions" {
  for_each = toset([
    "roles/bigquery.dataViewer",
    "roles/bigquery.user",
  ])
  project = data.terraform_remote_state.project_structure.outputs.projects["data-prod"]
  role    = each.key
  member  = "serviceAccount:${local.sales_fleet_manager_account_email}"
}

# needs access to the sales-data project to read canary records for pre-flight deployment testing
resource "google_project_iam_member" "sales_fleet_manager_bigquery_sales_data_prod_project_permissions" {
  for_each = toset([
    "roles/bigquery.dataViewer",
    "roles/bigquery.user",
  ])
  project = data.terraform_remote_state.project_structure.outputs.projects["sales-data-prod"]
  role    = each.key
  member  = "serviceAccount:${local.sales_fleet_manager_account_email}"
}

# needs access to the services project to read canary records, even though no tables there are queried
resource "google_project_iam_member" "sales_fleet_manager_bigquery_services_prod_project_permissions" {
  for_each = toset([
    "roles/bigquery.dataViewer",
    "roles/bigquery.user",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${local.sales_fleet_manager_account_email}"
}

resource "google_folder_iam_member" "sales_fleet_manager_bigquery_client_projects_folder_permissions" {
  for_each = toset([
    "roles/bigquery.dataViewer",
    "roles/bigquery.user",
  ])
  folder = data.terraform_remote_state.client_projects.outputs.client_projects_folder
  role   = each.key
  member = "serviceAccount:${local.sales_fleet_manager_account_email}"

}

resource "google_secret_manager_secret_iam_member" "sfmc_ims_shard_map_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.ims-shard-map-gcp.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_fleet_manager_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sfmc_alloy_shard_map_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.alloy-shard-map.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_fleet_manager_account_email}"
}
