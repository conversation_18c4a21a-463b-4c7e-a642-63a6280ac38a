locals {
  enterprise_pgbouncer_rfm1001_email = google_service_account.workload_identity["enterprise-pgbouncer-rfm1001"].email
}

resource "google_project_iam_member" "enterprise_pgbouncer_rfm1001_services" {
  for_each = toset([
    "roles/alloydb.client",
    "roles/serviceusage.serviceUsageConsumer"
  ])
  role    = each.key
  project = local.project
  member  = "serviceAccount:${local.enterprise_pgbouncer_rfm1001_email}"
}
