resource "google_project_service" "redis" {
  project            = local.project
  service            = "redis.googleapis.com"
  disable_on_destroy = false
}

module "rdo_api_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "rdo-api-cache"
  tier            = "STANDARD_HA"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 7
  redis_version   = "REDIS_4_0"
  role            = "cache"
  division        = "analytics"
  memory_size_gb  = 4
}

module "valuation_cms_legacy_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "valuation-cms-legacy-cache"
  zone            = "us-central1-f"
  environment     = var.environment
  tier            = "BASIC"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = null
  auto_ip_range   = true
  ip_range_number = 1
  connect_mode    = "PRIVATE_SERVICE_ACCESS"
  redis_version   = "REDIS_7_0"
  role            = "cache"
  division        = "rbval"
  memory_size_gb  = 1
}

module "valuations_service_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "valuations-service-cache"
  tier            = "STANDARD_HA"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 9
  redis_version   = "REDIS_4_0"
  role            = "cache"
  division        = "rbval"
  memory_size_gb  = 12
}

module "sales_rfm_channels_api_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-rfm-channels-api-cache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 11
  redis_version   = "REDIS_4_0"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

module "valuation_classification_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "valuation-classification-cache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 13
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "rbval"
  memory_size_gb  = 4
}

module "sales_fleet_manager_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-fleet-manager-cache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 15
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "platform"
  memory_size_gb  = 6
}

module "sales_ingest_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-ingest-cache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 17
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

output "rdo_api_cache_redis" {
  value = "${module.rdo_api_cache_redis_instance.host}:${module.rdo_api_cache_redis_instance.port}"
}

module "analytics_rdo_dataservice_cache_redis_stdinstance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "analytics-rdo-datasvc2-stdcache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 19
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "analytics"
  memory_size_gb  = 1
}

module "sales_portal_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-portal-stdcache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 21
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 2
}

module "honeycomb_refinery_redis_std_instance_prod" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "honeycomb-refinery-std"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 23
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 3
}

module "kafka_retry_service_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "kafka-retry-service-std-cache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 25
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 2
}

module "rdo_data_service_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "rdo-data-service-std-cache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  auth_enabled    = true
  base_ip_range   = "**********/16"
  ip_range_number = 27
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "analytics"
  memory_size_gb  = 2
}

module "sales_rfm_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-rfm-stdcache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 45
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

module "enterprise_media_service_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "enterprise-media-service-cache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 29
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

module "sales_catalog_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-catalog-stdcache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 37
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

# Redis instance for FMI to use for batch job processing
module "fleet_manager_integrations_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "fleet-manager-integrations-stdcache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 55
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "platform"
  memory_size_gb  = 2
}

module "sales_fmx_api_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-fmx-api-stdcache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 57
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 2
}

module "sales_notification_service_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-notification-service-stdcache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 61
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}
