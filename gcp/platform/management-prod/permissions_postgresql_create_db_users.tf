resource "google_project_iam_member" "postgresql_create_db_users_services" {
  for_each = toset([
    "roles/viewer",
    "roles/cloudsql.instanceUser",
    "roles/cloudsql.client",
    "roles/cloudsql.admin",
    "roles/alloydb.admin",
    "roles/secretmanager.viewer",
    "roles/container.developer",
    "roles/secretmanager.secretAccessor"
  ])
  role    = each.key
  project = local.services
  member  = "serviceAccount:${google_service_account.workload_identity["postgresql-create-db-users"].email}"
}

resource "google_project_iam_member" "postgresql_create_db_users_services_ppe" {
  for_each = toset([
    "roles/viewer",
    "roles/cloudsql.instanceUser",
    "roles/cloudsql.client",
    "roles/cloudsql.admin",
    "roles/alloydb.admin",
    "roles/secretmanager.viewer",
    "roles/container.developer",
    "roles/secretmanager.secretAccessor"
  ])
  role    = each.key
  project = local.services_ppe
  member  = "serviceAccount:${google_service_account.workload_identity["postgresql-create-db-users"].email}"
}

resource "google_project_iam_member" "postgresql_create_db_users_mgmt_prod" {
  for_each = toset([
    "roles/secretmanager.viewer",
    "roles/secretmanager.secretAccessor"
  ])
  role    = each.key
  project = local.management_prod
  member  = "serviceAccount:${google_service_account.workload_identity["postgresql-create-db-users"].email}"
}

resource "google_project_iam_member" "postgresql_create_db_users_images" {
  for_each = toset([
    "roles/cloudkms.cryptoKeyEncrypterDecrypter",
    "roles/compute.viewer",
    "roles/storage.objectViewer"
  ])
  role    = each.key
  project = local.images
  member  = "serviceAccount:${google_service_account.workload_identity["postgresql-create-db-users"].email}"
}
