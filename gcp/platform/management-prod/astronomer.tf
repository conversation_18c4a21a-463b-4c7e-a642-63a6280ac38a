module "astronomer_cluster" {
  source                        = "../../modules/compute/gke-cluster/v1/"
  location                      = "us-central1-a"
  name                          = "astronomer"
  environment                   = var.environment
  initial_node_count            = 0
  min_master_version            = "1.20"
  machine_type                  = "e2-standard-16"
  default_pool_machine_type     = "e2-standard-2"
  division                      = "platform"
  role                          = "airflow"
  folder_name                   = local.folder_name
  project                       = local.project
  min_node_count                = 0
  max_node_count                = 100
  disk_size_gb                  = 150
  network_link                  = module.astronomer_network.network_link
  subnet_link                   = module.astronomer_network.subnet_link
  cluster_secondary_range_name  = "astronomer-pods"
  services_secondary_range_name = "astronomer-services"
  cluster_autoscaling           = false
  release_channel               = "UNSPECIFIED"
  oauth_scopes = [
    "https://www.googleapis.com/auth/logging.write",
    "https://www.googleapis.com/auth/monitoring",
    "https://www.googleapis.com/auth/cloud-platform",
    "https://www.googleapis.com/auth/devstorage.read_write",
    "https://www.googleapis.com/auth/drive",
  ]
  taints = [
    {
      key    = "astronomer"
      value  = "airflow"
      effect = "NO_SCHEDULE"
    }
  ]
  enable_primary_pool             = true
  enable_spot_pool         = false
  workload_identity_enabled       = false
  workload_identity_pools_enabled = false
  providers = {
    google = google-beta
  }
}

resource "google_project_iam_member" "astronomer_cluster_artifactory_images_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.images
  role    = each.key
  member  = "serviceAccount:${module.astronomer_cluster.service_account_email}"
}

resource "google_project_iam_member" "astronomer_cluster_artifactory_images_permissions_dr" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${local.dr_prod_jobs_cluster_sa}"
}

resource "google_project_iam_member" "astronomer_cluster_artifactory_management_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${module.astronomer_cluster.service_account_email}"
}

resource "google_project_iam_member" "astronomer_images_permissions" {
  for_each = toset([
    "roles/storage.objectAdmin",
  ])
  role    = each.key
  project = local.images
  member  = "serviceAccount:${module.astronomer_cluster.service_account_email}"
}

resource "google_project_iam_member" "astronomer_cluster_prod_terraform_admin_permissions" {
  for_each = toset([
    "roles/secretmanager.secretAccessor",
  ])
  project = local.terraform_admin
  role    = each.key
  member  = "serviceAccount:${module.astronomer_cluster.service_account_email}"
}

data "google_secret_manager_secret_version" "cloudsql_astronomer" {
  secret  = "dbadmin_astronomer01_${var.environment}"
  project = local.project
}

module "cloudsql_astronomer_v2" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "astronomer02"
  environment       = var.environment
  availability_type = "REGIONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-14-79872"
  additional_labels = {
    division = "platform"
    role     = "database"
  }
  database_version               = "POSTGRES_14"
  database_name                  = "astronomer"
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_astronomer.secret_data)["password"]
  private_network                = module.astronomer_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7

  database_flags = [
    { name = "cloudsql.pg_shadow_select_role", value = "root" },
    { name = "temp_file_limit", value = "4087508" },
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
  ]

  providers = {
    google = google-beta
  }
}

resource "google_storage_bucket" "cloudsql_backups" {
  name     = "${local.project}-cloudsql-backups"
  location = var.region

  versioning {
    enabled = false
  }
}

resource "google_storage_bucket" "astronomer_images" {
  name     = "${local.project}-astronomer-images"
  location = var.region

  versioning {
    enabled = false
  }
}

resource "google_storage_bucket" "astronomer_dags" {
  name     = "${local.project}-astronomer-dags"
  location = var.region

  versioning {
    enabled = false
  }
}

resource "google_storage_bucket_iam_member" "sales_api_bucket_permission" {
  bucket = google_storage_bucket.astronomer_dags.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${data.terraform_remote_state.services.outputs.workload_identity_gcp_service_accounts["sales-api"]}"
}

output "astronomer_dag_bucket_name" {
  value = google_storage_bucket.astronomer_dags.name
}

output "astronomer_cluster_location" {
  value = module.astronomer_cluster.gke_cluster.location
}

output "astronomer_cluster_name" {
  value = module.astronomer_cluster.gke_cluster.name
}

output "astronomer_cluster_account_email" {
  value = module.astronomer_cluster.service_account_email
}

resource "google_filestore_instance" "astronomer_dags" {
  provider = google-beta
  name     = "astronomer-dags"
  location = "us-central1-b"
  tier     = "BASIC_HDD"

  file_shares {
    capacity_gb = 1024
    name        = "astronomer_dags"
  }

  networks {
    network = "astronomer-network"
    modes   = ["MODE_IPV4"]
  }
}

# Same structure as on services-prod/cloudsql_instances.tf. This to ease creating monitoring alerts.
locals {

  cloudsql_instances_ids = [
    module.cloudsql_astronomer_v2.instance_name,
  ]

  cloudsql_instances_db_version = [
    module.cloudsql_astronomer_v2.instance_db_version,
  ]

  cloudsql_instances_disk_size = [
    module.cloudsql_astronomer_v2.instance_disk_size,
  ]

  cloudsql_instances_memory_size = [
    module.cloudsql_astronomer_v2.instance_memory_size,
  ]
  cloudsql_instances_max_connections = [
    module.cloudsql_astronomer_v2.instance_max_connections,
  ]

  cloudsql_info = [
    for index in range(length(local.cloudsql_instances_ids)) : {
      "instance_id"     = local.cloudsql_instances_ids[index],
      "db_version"      = local.cloudsql_instances_db_version[index],
      "disk_size"       = local.cloudsql_instances_disk_size[index],
      "memory_size"     = local.cloudsql_instances_memory_size[index],
      "max_connections" = local.cloudsql_instances_max_connections[index]
    }
  ]

}

output "cloudsql_instances_info" {
  value = {
    (local.project) = local.cloudsql_info
  }
}
