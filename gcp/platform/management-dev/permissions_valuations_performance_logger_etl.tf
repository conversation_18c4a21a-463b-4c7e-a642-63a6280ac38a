
resource "google_project_iam_member" "valuations_performance_logger_appraisals" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/bigquery.dataOwner",
    "roles/bigquery.jobUser",
  ])
  role    = each.key
  project = local.appraisals
  member  = "serviceAccount:${google_service_account.workload_identity["vals-performance-logger-etl"].email}"
}

resource "google_project_iam_member" "valuations_performance_logger_services" {
  for_each = toset([
    "roles/container.developer",
  ])
  role    = each.key
  project = local.services
  member  = "serviceAccount:${google_service_account.workload_identity["vals-performance-logger-etl"].email}"
}

resource "google_project_iam_member" "valuations_performance_logger_logs_data" {
  for_each = toset([
    "roles/bigquery.dataOwner",
    "roles/bigquery.jobUser",
  ])
  role    = each.key
  project = local.logs
  member  = "serviceAccount:${google_service_account.workload_identity["vals-performance-logger-etl"].email}"
}