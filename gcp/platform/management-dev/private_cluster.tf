module "airflow_jobs_private_cluster" {
  source                             = "../../modules/compute/gke-cluster-autopilot/v1/"
  location                           = "us-central1"
  name                               = "airflow-jobs-private"
  environment                        = var.environment
  initial_node_count                 = 0
  min_master_version                 = "1.16"
  division                           = "platform"
  role                               = "airflow-jobs-private"
  folder_name                        = local.folder_name
  project                            = local.project
  min_node_count                     = 0
  max_node_count                     = 10
  network_link                       = module.airflow_jobs_private_cluster_network.network_link
  subnet_link                        = module.airflow_jobs_private_cluster_network.subnet_link
  cluster_secondary_range_name       = "airflow-jobs-private-pods"
  services_secondary_range_name      = "airflow-jobs-private-services"
  enable_private_nodes               = true
  enable_private_endpoint            = false
  enable_master_global_access_config = true
  whitelisted_cidrs                  = ["0.0.0.0/0"]

  oauth_scopes = [
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/cloud-platform",
  ]
  taints = [
    {
      key    = "worker"
      value  = "jobs"
      effect = "NO_SCHEDULE"
    }
  ]
  maintenance_policy = {
    start_time = "1970-01-01T07:00:00Z"
    end_time   = "1970-01-02T04:00:00Z"
    recurrence = "FREQ=WEEKLY;BYDAY=SU"
  }

  providers = {
    google = google-beta
  }
}


resource "google_project_iam_member" "airflow_jobs_private_cluster_artifactory_images_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.images
  role    = each.key
  member  = "serviceAccount:${module.airflow_jobs_private_cluster.service_account_email}"
}

resource "google_project_iam_member" "airflow_jobs_private_cluster_artifactory_management_dev_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.management_dev
  role    = each.key
  member  = "serviceAccount:${module.airflow_jobs_private_cluster.service_account_email}"
}

resource "google_project_iam_member" "airflow_jobs_private_cluster_artifactory_management_prod_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.management_prod
  role    = each.key
  member  = "serviceAccount:${module.airflow_jobs_private_cluster.service_account_email}"
}

resource "google_project_iam_member" "airflow_jobs_private_cluster_artifactory_services_dev_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.services_dev
  role    = each.key
  member  = "serviceAccount:${module.airflow_jobs_private_cluster.service_account_email}"
}

resource "google_compute_router" "nat-router" {
  project = local.project
  name    = "nat-router"
  region  = var.region
  network = module.airflow_jobs_private_cluster_network.network_link
}

resource "google_compute_address" "private-cluster-nat-address" {
  project      = local.project
  name         = "private-nat-address"
  region       = var.region
  address_type = "EXTERNAL"
}

resource "google_compute_router_nat" "private-cluster-nat" {
  project                            = local.project
  name                               = "private-cluster-nat"
  router                             = google_compute_router.nat-router.name
  region                             = var.region
  nat_ips                            = [google_compute_address.private-cluster-nat-address.self_link]
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  min_ports_per_vm                   = 64
  nat_ip_allocate_option             = "MANUAL_ONLY"
  log_config {
    enable = true
    filter = "ALL"
  }
}

# VPN between the private cluster and the SmartEquip (AWS)
locals {
  tunnel_1 = {
    psk = data.google_secret_manager_secret_version.smartequip_dev_tunnel_1_psk.secret_data,
    outside_ip_addresses = {
      "customer_gateway" = "*************",
      "vpn_gateway"      = "**************"
    },
    inside_ip_addresses = {
      "customer_gateway" = "***************",
      "vpn_gateway"      = "***************"
    }
  }

  tunnel_2 = {
    psk = data.google_secret_manager_secret_version.smartequip_dev_tunnel_2_psk.secret_data,
    outside_ip_addresses = {
      "customer_gateway" = "*************",
      "vpn_gateway"      = "**************"
    },
    inside_ip_addresses = {
      "customer_gateway" = "***************",
      "vpn_gateway"      = "***************"
    }
  }
}
data "google_secret_manager_secret_version" "smartequip_dev_tunnel_1_psk" {
  secret  = "se-vpn-tunnel-psk-1"
  project = local.project
}

data "google_secret_manager_secret_version" "smartequip_dev_tunnel_2_psk" {
  secret  = "se-vpn-tunnel-psk-2"
  project = local.project
}

resource "google_compute_router" "se-vpn-router" {
  project     = local.project
  name        = "smartequip-vpn-dev-router"
  description = "Router dedicated to SmartEquip AWS  VPN connection (dev)"
  region      = var.region
  network     = module.airflow_jobs_private_cluster_network.network_link

  bgp {
    asn = 65000
  }
}

resource "google_compute_external_vpn_gateway" "se_vpn_external_gateway" {
  name            = "smartequip-aws-dev"
  redundancy_type = "TWO_IPS_REDUNDANCY"
  interface {
    id         = 0
    ip_address = "**************"
  }

  interface {
    id         = 1
    ip_address = "**************"
  }
}

resource "google_compute_ha_vpn_gateway" "se_ha_gateway1" {
  region  = var.region
  name    = "se-ha-vpn-1"
  network = module.airflow_jobs_private_cluster_network.network_link
}

resource "google_compute_vpn_tunnel" "tunnel1" {
  name                            = "tunnel1-conn1-se-aws-dev"
  description                     = "Tunnel 1 on the first interface for AWS Dev VPN connection"
  region                          = var.region
  vpn_gateway                     = google_compute_ha_vpn_gateway.se_ha_gateway1.id
  peer_external_gateway           = google_compute_external_vpn_gateway.se_vpn_external_gateway.id
  peer_external_gateway_interface = 0
  shared_secret                   = local.tunnel_1.psk
  router                          = google_compute_router.se-vpn-router.id
  vpn_gateway_interface           = 0

  lifecycle {
    ignore_changes = [
      shared_secret
    ]
  }
}

resource "google_compute_vpn_tunnel" "tunnel2" {
  name                            = "tunnel2-conn1-se-aws-dev"
  region                          = var.region
  vpn_gateway                     = google_compute_ha_vpn_gateway.se_ha_gateway1.id
  peer_external_gateway           = google_compute_external_vpn_gateway.se_vpn_external_gateway.id
  peer_external_gateway_interface = 1
  shared_secret                   = local.tunnel_2.psk
  router                          = google_compute_router.se-vpn-router.id
  vpn_gateway_interface           = 0

  lifecycle {
    ignore_changes = [
      shared_secret
    ]
  }
}
