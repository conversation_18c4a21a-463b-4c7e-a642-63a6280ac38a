module "jobs_cluster_v3" {
  source                        = "../../modules/compute/gke-cluster/v1/"
  location                      = "us-central1-b"
  name                          = "composer-jobs-v3"
  environment                   = var.environment
  initial_node_count            = 0
  min_master_version            = "1.16"
  machine_type                  = "n1-standard-16"
  default_pool_machine_type     = "n1-standard-4"
  division                      = "platform"
  role                          = "composer-jobs"
  folder_name                   = local.folder_name
  project                       = local.project
  min_node_count                = 0
  max_node_count                = 100
  disk_size_gb                  = 300
  network_link                  = module.cloud_composer_jobs_network_v3.network_link
  subnet_link                   = module.cloud_composer_jobs_network_v3.subnet_link
  cluster_secondary_range_name  = "composer-jobs-pods"
  services_secondary_range_name = "composer-jobs-services"
  cluster_autoscaling           = false
  release_channel               = "UNSPECIFIED"

  oauth_scopes = [
    "https://www.googleapis.com/auth/logging.write",
    "https://www.googleapis.com/auth/monitoring",
    "https://www.googleapis.com/auth/cloud-platform",
    "https://www.googleapis.com/auth/devstorage.read_write",
    "https://www.googleapis.com/auth/drive",
  ]
  taints = [
    {
      key    = "worker"
      value  = "jobs"
      effect = "NO_SCHEDULE"
    }
  ]
  enable_primary_pool             = false
  enable_spot_pool                = true
  workload_identity_enabled       = true
  workload_identity_pools_enabled = true
  maintenance_policy = {
    start_time = "2019-01-01T07:00:00Z"
    end_time   = "2019-01-02T04:00:00Z"
    recurrence = "FREQ=WEEKLY;BYDAY=SU"
  }
  providers = {
    google = google-beta
  }
}


resource "google_project_iam_member" "jobs_cluster_artifactory_images_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.images
  role    = each.key
  member  = "serviceAccount:${module.jobs_cluster_v3.service_account_email}"
}

resource "google_project_iam_member" "jobs_cluster_artifactory_management_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.management_prod
  role    = each.key
  member  = "serviceAccount:${module.jobs_cluster_v3.service_account_email}"
}

resource "google_project_iam_member" "jobs_cluster_artifactory_management_dev_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.management_dev
  role    = each.key
  member  = "serviceAccount:${module.jobs_cluster_v3.service_account_email}"
}

resource "google_project_iam_member" "jobs_cluster_artifactory_services_dev_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.services_dev
  role    = each.key
  member  = "serviceAccount:${module.jobs_cluster_v3.service_account_email}"
}



resource "google_container_node_pool" "jobs_postgres_node_pool" {
  provider           = google-beta
  name               = "postgres"
  location           = "us-central1-b"
  cluster            = module.jobs_cluster_v3.gke_cluster.name
  initial_node_count = 0
  project            = local.project
  autoscaling {
    min_node_count = 0
    max_node_count = 3
  }
  management {
    auto_repair  = true
    auto_upgrade = true
  }
  node_config {
    service_account = module.jobs_cluster_v3.service_account_email
    spot            = false
    machine_type    = "e2-standard-4"
    disk_size_gb    = 150
    disk_type       = "pd-ssd"
    metadata = {
      disable-legacy-endpoints = true
    }
    labels = {
      machine-platform = "general-purpose"
      machine-type     = "e2-standard-4"
      machine-pricing  = "on-demand"
      purpose          = "postgres"
    }

    oauth_scopes = [
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/devstorage.read_write",
      "https://www.googleapis.com/auth/drive",
    ]
    taint {
      key    = "postgres"
      value  = "reserved"
      effect = "NO_SCHEDULE"
    }

  }
  timeouts {
    update = "60m"
  }

  lifecycle {
    ignore_changes = [initial_node_count]
  }
}
