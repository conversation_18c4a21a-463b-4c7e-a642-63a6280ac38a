resource "google_project_iam_member" "platform_classification_data_permissions" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/bigquery.dataOwner",
    "roles/bigquery.jobUser",
    "roles/bigquery.readSessionUser",
  ])
  role    = each.key
  project = local.platform_data
  member  = "serviceAccount:${google_service_account.workload_identity["platform-classification"].email}"
}

resource "google_project_iam_member" "platform_classification_services_permissions" {
  for_each = toset([
    "roles/cloudsql.client",
  ])
  role    = each.key
  project = local.services
  member  = "serviceAccount:${google_service_account.workload_identity["platform-classification"].email}"
}

resource "google_project_iam_member" "platform_classification_sales_data_permissions" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/bigquery.dataOwner",
    "roles/bigquery.jobUser",
  ])
  role    = each.key
  project = local.sales_data
  member  = "serviceAccount:${google_service_account.workload_identity["platform-classification"].email}"
}

resource "google_project_iam_member" "platform_classification_sales_data_permissions_prod" {
  for_each = toset([
    "roles/bigquery.dataViewer",
    "roles/bigquery.jobUser",
  ])
  role    = each.key
  project = local.sales_data_prod
  member  = "serviceAccount:${google_service_account.workload_identity["platform-classification"].email}"
}

resource "google_project_iam_member" "platform_classification_appraisals_data" {
  for_each = toset([
    "roles/bigquery.jobUser",
    "roles/bigquery.dataViewer",
    "roles/bigquery.readSessionUser",
  ])
  role    = each.key
  project = local.appraisals
  member  = "serviceAccount:${google_service_account.workload_identity["platform-classification"].email}"
}
