data "google_kms_secret" "svc_kerberos_test_pass" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU70H3iuS7ircipyZ+Uw0YUWNrdHOemic92Bikcyzx+5oSOgCc5a1pJSuo3COI1CuFI+5UnBJI4tX9j5QvhjkmD32/lW2cbyDZ3l4N06nIMDgZsDNDyXjlCYONsOE="
}

data "google_client_config" "default" {}

provider "kubernetes" {
  alias                  = "services-dev"
  host                   = "https://${module.services_gke_cluster.gke_cluster.endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(module.services_gke_cluster.gke_cluster.master_auth.0.cluster_ca_certificate)
}

provider "kubernetes" {
  alias                  = "services-dev-europe-west-2"
  host                   = "https://${module.europe_west_2_services_gke_cluster.gke_cluster.endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(module.europe_west_2_services_gke_cluster.gke_cluster.master_auth.0.cluster_ca_certificate)
}

resource "kubernetes_secret" "svc_kerberos_test" {
  for_each = toset([
    "default",
    "sales-portal",
    "sales-api",
    "sales-cge",
    "sales-cache",
    "appraisals-residuals",
    "enterprise-messaging",
    "enterprise-notification",
    "appraisals-coda",
    "appraisals-valuator",
    "analytics-api",
    "analytics-rdo-api",
  ])

  provider = kubernetes.services-dev
  metadata {
    name      = "svc-kerberos-test"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_kerberos_test"
    password = data.google_kms_secret.svc_kerberos_test_pass.plaintext
  }
}

data "google_kms_secret" "bugsnag_api_key_sales_txns" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU70dPjLY2A9gfLgzm5wg3kn6o9PD7qoiS4RrSBUnKbnkSSQCc5a1p9KJ8WvXrHBdFcl3MRwYUFRUE98gGp64jBMd0VlR37TJqwdOCPLr2KCm4ZmD/gkjGb33yJ5i1XT7dRPE7eQoeIelFlcs="
}

data "google_kms_secret" "bugsnag_api_key_collateral" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72rCOgsEV2Y4oY3WXFu5FHiwA/0jjDX6b9CpNuwq4UQSSQCc5a1pv8R4n9kc5yZh5vQ5NrZFnO84k8sSqaVZ2uE8qmddE2t38kFvNtpmsfCtAVL/j3rt5bU/D77kJZeLUmlFd89BMjvY1jQ="
}

data "google_kms_secret" "bugsnag_api_key_appraisals_valuations" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU71iBG+QUDZ/BPccKB2ZXIO/jVth/qCOqZMKt0MIySZwSSQA4/8/uR4qrb9yMa01jdy5ed/Ut8nF7YIuTvPHVwMoD6rDwXsMOuytkkY/WgwC4gRuA6m1gcGsnFsg/rdd69COJ4juSdBcXARc="
}

data "google_kms_secret" "bugsnag_api_key_appraisals_record360" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU71HUAwlYeszWMF/llmWCLhFOJG70rvj6y8gDPUshFdkSSQA4/8/uQT0u6HuFbR9aRw6TW1jYcjh/AiePQNf+N6gYbBzxGK/y73GdRrJhaTS2GN0k2ztChWysg3XKyagJI/7GEsxR3ACQ1Sw="
}

data "google_kms_secret" "bugsnag_api_key_sales_catalog" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU77sc9WmidsQOO1/yG78SW2cn648E7VOAFSae9n14Ep4SSQA4/8/uGno1a12eFKRf9/zBsFodadBlQ11gycRY28b5hCjpBP+LQ9I/fxFYnQRVBsIWxEkbjNE26jTs9h2SpUB/6BFpIh9QVlk="
}

data "google_kms_secret" "bugsnag_api_key_enterprise_config_service" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU77ix2gnMBmKv4N7RzSqrcrhb2Pp4A9QI6NfIedS86PYSSQA4/8/uNxvO93kaoSfzb5s1SWalF5vYtakDNRMMFaWHE+8H/t38aKb77hPZNpGW/omZd27q6dhhBrw4IrpzD2GABlpvUdjUxcU="
}

data "google_kms_secret" "bugsnag_api_key_analytics_metrics" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU79cAtT38sd46CgRs6tUo2V7qOyw5Y2cHmbicB0/faRoSSQA4/8/ukTi25FJ5Ghg1kPSQ7I+VlvdIzLyqdn/+lwEU84DLhWALUrrRfIzDawZkmnT+rgELIISysALSfSgcvf6rFltyfoitfDM="
}

data "google_kms_secret" "bugsnag_api_key_appraisals_portal" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU75DiB6scWdri6eHG+/7NgNZVFCoOqyyNqbN1X1yLIAkSSQA4/8/uIj2i54+xtsCQbMNhwYVMP4noV2w4/WgXJuG3JXhjfvw+wOsB3ergRttyOF+6oTrkQddMYHtNj7Q+CncU9MTdSOKsqWI="
}

resource "kubernetes_secret" "bugsnag_api_key" {
  for_each = {
    sales-txns                = data.google_kms_secret.bugsnag_api_key_sales_txns.plaintext
    sales-cge                 = data.google_kms_secret.bugsnag_api_key_collateral.plaintext
    appraisals-valuations     = data.google_kms_secret.bugsnag_api_key_appraisals_valuations.plaintext
    appraisals-record360      = data.google_kms_secret.bugsnag_api_key_appraisals_record360.plaintext
    sales-catalog             = data.google_kms_secret.bugsnag_api_key_sales_catalog.plaintext
    sales-webshop             = data.google_kms_secret.bugsnag_api_key_sales_catalog.plaintext
    enterprise-config-service = data.google_kms_secret.bugsnag_api_key_enterprise_config_service.plaintext
    analytics-metrics         = data.google_kms_secret.bugsnag_api_key_analytics_metrics.plaintext
    appraisals-portal         = data.google_kms_secret.bugsnag_api_key_appraisals_portal.plaintext
  }
  provider = kubernetes.services-dev
  metadata {
    name      = "bugsnag-api-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = each.value
  }
}

data "google_secret_manager_secret_version" "honeycomb_api_key" {
  secret  = "honeycomb_api_key"
  project = local.project
}

resource "kubernetes_secret" "honeycomb_key" {
  for_each = toset([
    "analytics-dtt",
    "analytics-metrics",
    "analytics-public-api",
    "analytics-public-py-api",
    "analytics-rdo-api",
    "analytics-rdo-dataservice",
    "appraisals-classify",
    "appraisals-coda",
    "appraisals-legacy-api-v2",
    "appraisals-portal",
    "appraisals-record360",
    "appraisals-residuals",
    "appraisals-valuations",
    "appraisals-valuator",
    "default",
    "enterprise-domain-service",
    "enterprise-authentication",
    "enterprise-config-service",
    "enterprise-image-service",
    "enterprise-messaging",
    "enterprise-notification",
    "fleet-manager-integrations",
    "kafka-retry-service",
    "platform-classification",
    "platform-service-template",
    "platform-user-config",
    "platform-valuation",
    "rdo-user-service",
    "sales-api",
    "sales-cache",
    "sales-catalog",
    "sales-webshop",
    "sales-cge",
    "sales-classification",
    "sales-fleet-manager",
    "sales-fleet-manager-tools",
    "sales-fmx-api",
    "sales-portal",
    "sales-proposals",
    "sales-notification-service",
    "sales-rfm-channels-api",
    "sales-txns",
    "sales-ingest",
    "valuation-classification",
    "honeycomb-refinery",
  ])

  provider = kubernetes.services-dev
  metadata {
    name      = "honeycomb-api-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = data.google_secret_manager_secret_version.honeycomb_api_key.secret_data
  }
}

data "sentry_key" "default" {
  for_each = data.terraform_remote_state.terraform_admin.outputs.sentry_projects

  organization = each.value["org"]
  project      = each.value["name"]
  name         = "Default"
}

resource "kubernetes_secret" "sentry_integration_dsn" {
  for_each = {
    analytics-metrics = {
      sentry_project = "analytics_metrics_api"
    }
    sales-txns = {
      sentry_project = "sales_rfm_api"
    }
    sales-proposals = {
      sentry_project = "sales_proposals"
    }
    sales-ingest = {
      sentry_project = "sales_ingest_api"
    }
    appraisals-portal = {
      sentry_project = "appraisals_portal_api"
    }
    appraisals-valuations = {
      sentry_project = "appraisals_values_api"
    }
    appraisals-classify = {
      sentry_project = "classify_tags_api"
    }
    sales-catalog = {
      sentry_project = "sales_catalog_api"
    }
    sales-webshop = {
      sentry_project = "sales_webshop_api"
    }
    platform-user-config = {
      sentry_project = "platform_user_config"
    }
    fleet-manager-integrations = {
      sentry_project = "platform_fleet_manager_integrations"
    }
    sales-fmx-api = {
      sentry_project = "sales_fmx_api"
    }
    sales-notification-service = {
      sentry_project = "sales_notification_service"
    }
    sales-rfm-channels-api = {
      sentry_project = "sales_rfm_channels_api"
    },
    appraisals-legacy-api-v2 = {
      sentry_project = "appraisals_legacy_api_v2"
    },
    appraisals-dcs-api = {
      sentry_project = "appraisals_dcs_api"
    },
    enterprise-domain-service = {
      sentry_project = "enterprise_domain_service"
    },
    valuation-classification = {
      sentry_project = "platform_valuation_taxonomy_service"
    },
    kafka-retry-service = {
      sentry_project = "kafka_retry_service"
    },
  }

  provider = kubernetes.services-dev
  metadata {
    name      = "sentry-integration-dsn"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = data.sentry_key.default[each.value["sentry_project"]].dsn_public
  }
}

data "google_secret_manager_secret_version" "loggly_customer_token" {
  secret  = "loggly_customer_token"
  project = local.project
}

resource "kubernetes_secret" "loggly_customer_token" {
  for_each = toset([
    "analytics-dtt",
    "analytics-metrics",
    "analytics-public-api",
    "analytics-public-py-api",
    "analytics-rdo-api",
    "appraisals-legacy-api-v2",
    "appraisals-classify",
    "appraisals-coda",
    "appraisals-portal",
    "appraisals-record360",
    "appraisals-residuals",
    "appraisals-valuations",
    "appraisals-valuator",
    "default",
    "enterprise-domain-service",
    "enterprise-authentication",
    "enterprise-config-service",
    "enterprise-image-service",
    "enterprise-messaging",
    "enterprise-notification",
    "fleet-manager-integrations",
    "platform-classification",
    "platform-service-template",
    "platform-user-config",
    "platform-valuation",
    "sales-api",
    "sales-cache",
    "sales-catalog",
    "sales-webshop",
    "sales-cge",
    "sales-classification",
    "sales-fleet-manager",
    "sales-fleet-manager-tools",
    "sales-portal",
    "sales-proposals",
    "sales-fmx-api",
    "sales-notification-service",
    "sales-rfm-channels-api",
    "sales-txns",
    "sales-ingest",
    "valuation-classification",
  ])

  provider = kubernetes.services-dev
  metadata {
    name      = "loggly-customer-token"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = data.google_secret_manager_secret_version.loggly_customer_token.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics-ect-db-password" {
  secret = "analytics-ect-db-password"
  project = local.project
}

resource "kubernetes_secret" "analytics_ect_db_password" {
  for_each = toset([
    "analytics-ect",
  ])

  provider = kubernetes.services-dev
  metadata {
    name      = "analytics-ect-db-password"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    password = data.google_secret_manager_secret_version.analytics-ect-db-password.secret_data
  }
}

resource "kubernetes_secret" "analytics_ect_iap_client_credentials" {
  provider = kubernetes.services-dev
  metadata {
    name      = "iap-client-credentials"
    namespace = "analytics-ect"
  }

  data = {
    client_id     = google_iap_client.analytics_ect.client_id
    client_secret = google_iap_client.analytics_ect.secret
  }
}

data "google_kms_secret" "rfm_dev_sql_pass" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU79wsBTrcl06LWedIPw5t/HFG/eTdxDQSuI0bSa02c58SOQA4/8/ulflVeUM0scrkIWZ9SGDZaIjzMRzkEgz1a/XDcH/dqaKynSZHiNzmIq1YexNLcrKg+brEwg=="
}

resource "kubernetes_secret" "rfm_sql_user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-txns",
    "sales-ingest"
  ])
  metadata {
    name      = "rfm-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.rfm_dev_sql_pass.plaintext
  }
}

data "google_kms_secret" "rfm-equipment-catalog-api" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7yUAwgHzVqNYSTSUSJnmf0E6UyGMO0fe38SRCaxzNIASPQA4/8/u+/N0amKFl39lqrdVFMZLPbgXlBeknyxgBOZ+eu2mJzaIj5DTchzfxiq6Xs2vmDhu8bOuHEWhc9o="
}

resource "kubernetes_secret" "rfm-equipment-catalog-api" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-catalog",
    "sales-webshop",
    "enterprise-config-service",
  ])
  metadata {
    name      = "pg-catalog-user-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_catalog_api"
    password = data.google_kms_secret.rfm-equipment-catalog-api.plaintext
  }
}

data "google_kms_secret" "rfm-equipment-rfm-api" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7z6zzZWMSETWDuNGGZMbkQk9+1CGSdEBN2VpOBGEqWwSPQA4/8/umTh9K3KrBtbyMlB6UKBxofHD7i40bhPAOa274hYduKJb/QyEh61EdZpfHw1tSfteY2zCTtuUT1o="
}

resource "kubernetes_secret" "rfm-equipment-rfm-api" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-txns",
    "sales-ingest"
  ])
  metadata {
    name      = "pg-equipment-rfm-api"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_rfm_api"
    password = data.google_kms_secret.rfm-equipment-rfm-api.plaintext
  }
}

data "google_kms_secret" "enterprise_configs_db_url" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72sDx6gBBp0uVmgUt4jCIjLRakUaHT7CmeqhkOp2u2kSXQA4/8/uu//dM+Yj/XtPKzKtf4ZIWRBap1tKaOrEoOjyCx89cYw63/XjCpyMhmiXTNizPEpfImFABud9O3Az7s9Aku7hA0R/bifAPSUMOqFHSHY5BSGbHZMKZQTPcg=="
}

data "google_kms_secret" "enterprise_configs_secret_key" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU77BPLvABd9jxUEygfndRycAmqec7YYr0Qwl1yK+IhN8SOQA4/8/unX43gbwbTiwf4+b7A9qvOBywRQu/C+Gc2WBC04Aq7U+DcYMVzSg0iIFQjIzmrLwnBqfKvw=="
}

resource "kubernetes_secret" "enterprise_configs_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "enterprise-config-service",
  ])
  metadata {
    name      = "secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    db_url     = data.google_kms_secret.enterprise_configs_db_url.plaintext
    secret_key = data.google_kms_secret.enterprise_configs_secret_key.plaintext
    sentry_dsn = data.sentry_key.default["client_config_service"].dsn_public
  }
}

data "google_kms_secret" "sales_catalog_recaptcha" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/q6QBRcikATled+DWulgYKjmrz4JWA46pjytkX1gzESUQA4/8/uurixOiPRUYL0px/sZJwfLEl9r4I9GBxyBDTDDqsR3YGi3KpeJczPC37tUR5k9SL/SvNYc/2qgSGaTMCEEbyGpgZyCQ5lWUUlfkYU8Q=="
}

resource "kubernetes_secret" "sales_catalog_recaptcha" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-catalog",
    "sales-webshop",
  ])
  metadata {
    name      = "recaptcha-api-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = data.google_kms_secret.sales_catalog_recaptcha.plaintext
  }
}

data "google_kms_secret" "sales_catalog_akismet" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU70kdW3LgpMSRBDKwLj4pdqcHgLO2pmGnheKxnuoDOIwSNQA4/8/ufBw+p3s6agryr1qlNWzuKqf6VWc3vF8KBjYwDrO4KCTYTSOiSkmg6JnNqZ4Xq3vV"
}

resource "kubernetes_secret" "sales_catalog_akismet" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-catalog",
    "sales-webshop",
  ])
  metadata {
    name      = "akismet-api-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = data.google_kms_secret.sales_catalog_akismet.plaintext
  }
}

data "google_kms_secret" "record-360-pg-user" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU730h2Sjhs1ObfO7o78Mqp6ID3COGnlWuLq/a2EnExZkSOQBp1KhiTDkEN2yjcHrfewGC0FrOZhgxQNyigh1BNmdYjnNLIAtG6hPBV1LB9nXphOq3JZthhjbIIQ=="
}

resource "kubernetes_secret" "record-360-pg-user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-portal",
  ])
  metadata {
    name      = "pg-record360-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.record-360-pg-user.plaintext
  }
}

resource "kubernetes_secret" "pg-appraisals-user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-portal",
  ])
  metadata {
    name      = "pg-appraisals-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.record-360-pg-user.plaintext
  }
}

data "google_secret_manager_secret_version" "rabbitmq-password" {
  secret  = "appraisals_rabbitmq_admin_password"
  project = local.project
}

data "google_secret_manager_secret_version" "rabbitmq-erlang-cookie" {
  secret  = "appraisals_rabbitmq_erlang_cookie"
  project = local.project
}

resource "kubernetes_secret" "rabbitmq-secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-rabbitmq",
  ])
  metadata {
    name      = "rabbitmq-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    rabbitmq-password      = data.google_secret_manager_secret_version.rabbitmq-password.secret_data
    rabbitmq-erlang-cookie = data.google_secret_manager_secret_version.rabbitmq-erlang-cookie.secret_data
  }
}

data "google_secret_manager_secret_version" "sales-txns-algolia-api" {
  secret  = "sales-txns-algolia-api"
  project = local.project
  version = 2
}

resource "kubernetes_secret" "sales-txns-algolia-api" {
  provider = kubernetes.services-dev

  for_each = toset([
    "sales-txns",
  ])

  metadata {
    name      = "sales-txns-algolia-api"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    id      = lookup(jsondecode(data.google_secret_manager_secret_version.sales-txns-algolia-api.secret_data), "id")
    api_key = lookup(jsondecode(data.google_secret_manager_secret_version.sales-txns-algolia-api.secret_data), "api_key")
  }
}

data "google_secret_manager_secret_version" "sales-ingest-algolia-api" {
  secret  = "sales-ingest-algolia-api"
  project = local.project
  version = 1
}

resource "kubernetes_secret" "sales-ingest-algolia-api" {
  provider = kubernetes.services-dev

  for_each = toset([
    "sales-ingest",
  ])

  metadata {
    name      = "sales-ingest-algolia-api"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    id      = lookup(jsondecode(data.google_secret_manager_secret_version.sales-ingest-algolia-api.secret_data), "id")
    api_key = lookup(jsondecode(data.google_secret_manager_secret_version.sales-ingest-algolia-api.secret_data), "api_key")
  }
}

data "google_secret_manager_secret_version" "platform-valuation-algolia-api" {
  secret  = "platform_valuation_algolia_api_key"
  project = local.project
  version = 2
}

resource "kubernetes_secret" "platform-valuation-algolia-api" {
  provider = kubernetes.services-dev

  for_each = toset([
    "platform-valuation",
  ])

  metadata {
    name      = "platform-valuation-algolia-api-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    id      = lookup(jsondecode(data.google_secret_manager_secret_version.platform-valuation-algolia-api.secret_data), "id")
    api_key = lookup(jsondecode(data.google_secret_manager_secret_version.platform-valuation-algolia-api.secret_data), "api_key")
  }
}

data "google_secret_manager_secret_version" "dbuser-rdo04-rdoapi" {
  secret  = "dbuser_rdo04_rdo_api"
  project = local.project
}

resource "kubernetes_secret" "rdo04-rdoapi" {
  provider = kubernetes.services-dev

  for_each = toset([
    "analytics-metrics",
    "analytics-public-py-api",
    "analytics-rdo-dataservice",
    "rdo-user-service"
  ])


  metadata {
    name      = "rdo04-rdoapi"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "rdo_api"
    password = data.google_secret_manager_secret_version.dbuser-rdo04-rdoapi.secret_data
  }
}

data "google_secret_manager_secret_version" "appraisals-loggly-customer-token" {
  secret  = "appraisals_loggly_customer_token"
  project = local.project
}

resource "kubernetes_secret" "appraisals-loggly" {
  provider = kubernetes.services-dev

  for_each = toset([
    "appraisals-classify",
  ])

  metadata {
    name      = "appraisals-loggly"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    customer_token = data.google_secret_manager_secret_version.appraisals-loggly-customer-token.secret_data
  }
}

data "google_secret_manager_secret_version" "appraisals-classify-sql-password" {
  secret  = "appraisals_classify_sql_password"
  project = local.project
}

resource "kubernetes_secret" "appraisals-classify-sql" {
  provider = kubernetes.services-dev

  for_each = toset([
    "appraisals-classify",
  ])

  metadata {
    name      = "appraisals-classify-sql"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_classify_tags"
    password = data.google_secret_manager_secret_version.appraisals-classify-sql-password.secret_data
    ip       = "***********"
    ip-alpha = "**********"
    ip-beta  = "***********"
  }
}

data "google_secret_manager_secret_version" "rfm_sales_fleet_manager_user" {
  secret  = "dbuser_sales_fleet_manager"
  project = local.project
}

data "google_secret_manager_secret_version" "rfm_delta_user" {
  secret  = "dbuser_rfm_fleet_manager_delta"
  project = local.management
}

data "google_secret_manager_secret_version" "sales_fleet_manager_secret_key" {
  secret  = "sales_fleet_manager_secret_key"
  project = local.project
}

resource "kubernetes_secret" "fleet_manager_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-fleet-manager",
  ])
  metadata {
    name      = "secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    database_password = lookup(jsondecode(data.google_secret_manager_secret_version.rfm_sales_fleet_manager_user.secret_data), "password")
    secret_key        = data.google_secret_manager_secret_version.sales_fleet_manager_secret_key.secret_data
    sentry_dsn        = data.sentry_key.default["platform_fleet_manager"].dsn_public
  }
}

resource "kubernetes_secret" "fleet_manager_delta_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-fleet-manager",
  ])
  metadata {
    name      = "delta-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    db_password = lookup(jsondecode(data.google_secret_manager_secret_version.rfm_delta_user.secret_data), "password")
  }
}

# sales_fleet_manager_integrations

# accessed from GCP secret manager directly
data "google_secret_manager_secret_version" "enterprise_domain_service_api_auth" {
  secret  = "enterprise_domain_service_static_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_integrations_api_auth" {
  secret  = "sales_fleet_manager_integrations_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_integrations_db_credentials" {
  secret  = "dbuser_sales_fleet_manager_integrations"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_integrations_secret_key" {
  secret  = "sales_fleet_manager_integrations_secret_key"
  project = local.project
}

resource "kubernetes_secret" "fleet_manager_integrations_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "fleet-manager-integrations",
  ])
  metadata {
    name      = "secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    database_password = lookup(jsondecode(data.google_secret_manager_secret_version.sales_fleet_manager_integrations_db_credentials.secret_data), "password")
    database_username = lookup(jsondecode(data.google_secret_manager_secret_version.sales_fleet_manager_integrations_db_credentials.secret_data), "username")
    redis_host        = module.fleet_manager_integrations_cache_redis_instance.host
    redis_port        = module.fleet_manager_integrations_cache_redis_instance.port
    secret_key        = data.google_secret_manager_secret_version.sales_fleet_manager_secret_key.secret_data
    sentry_dsn        = data.sentry_key.default["platform_fleet_manager_integrations"].dsn_public
  }
}

# ----------------------------

data "google_secret_manager_secret_version" "platform_classification_auth0_client_id" {
  secret  = "platform_classification_auth0_client_id"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_classification_auth0_client_secret" {
  secret  = "platform_classification_auth0_client_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_classification_secret_key" {
  secret  = "platform_classification_secret_key"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_classification_db_user" {
  secret  = "dbuser_platform_classification"
  project = local.project
}

resource "kubernetes_secret" "platform_classification_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "platform-classification",
  ])
  metadata {
    name      = "secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    db_username         = lookup(jsondecode(data.google_secret_manager_secret_version.platform_classification_db_user.secret_data), "username")
    db_password         = lookup(jsondecode(data.google_secret_manager_secret_version.platform_classification_db_user.secret_data), "password")
    secret_key          = data.google_secret_manager_secret_version.platform_classification_secret_key.secret_data
    sentry_dsn          = data.sentry_key.default["platform_classification"].dsn_public
    auth0_client_id     = data.google_secret_manager_secret_version.platform_classification_auth0_client_id.secret_data
    auth0_client_secret = data.google_secret_manager_secret_version.platform_classification_auth0_client_secret.secret_data
  }
}

data "google_secret_manager_secret_version" "platform_user_config_secret_key" {
  secret  = "platform_user_config_secret_key"
  project = local.project
}

resource "kubernetes_secret" "platform-user-config-secret-key" {
  provider = kubernetes.services-dev

  for_each = toset([
    "platform-user-config",
  ])

  metadata {
    name      = "platform-user-config-secret-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    secret_key                       = data.google_secret_manager_secret_version.platform_user_config_secret_key.secret_data
    auth0_client_id                  = data.google_secret_manager_secret_version.sales_fleet_manager_tools_auth0_client_id.secret_data
    auth0_client_secret              = data.google_secret_manager_secret_version.sales_fleet_manager_tools_auth0_client_secret.secret_data
    stage_auth0_client_id            = data.google_secret_manager_secret_version.sales_fleet_manager_tools_stage_auth0_client_id.secret_data
    stage_auth0_client_secret        = data.google_secret_manager_secret_version.sales_fleet_manager_tools_stage_auth0_client_secret.secret_data
    sales_portal_auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_id")
    sales_portal_auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_secret")
    rca_auth0_client_id              = lookup(jsondecode(data.google_secret_manager_secret_version.platform_user_config_auth0_credentials.secret_data), "client_id")
    rca_auth0_client_secret          = lookup(jsondecode(data.google_secret_manager_secret_version.platform_user_config_auth0_credentials.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "platform_user_config_db_password" {
  secret  = "dbuser_rfm_user_config_rfm_user_config_api"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_user_config_enterprise_authentication_key" {
  secret  = "platform_user_config_enterprise_authentication_key"
  project = local.project
}

resource "kubernetes_secret" "platform-user-config-db-password" {
  provider = kubernetes.services-dev

  for_each = toset([
    "platform-user-config",
  ])

  metadata {
    name      = "platform-user-config-db-password"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    db_password = data.google_secret_manager_secret_version.platform_user_config_db_password.secret_data
  }
}

resource "kubernetes_secret" "platform-user-config-sentry-dsn" {
  provider = kubernetes.services-dev

  for_each = toset([
    "platform-user-config",
  ])

  metadata {
    name      = "platform-user-config-sentry-dsn"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    sentry_dsn = data.sentry_key.default["platform_user_config"].dsn_public
  }
}

data "google_secret_manager_secret_version" "platform_valuation_secret_key" {
  secret  = "platform_valuation_secret_key"
  project = local.project
}

resource "kubernetes_secret" "platform_valuation_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "platform-valuation",
  ])
  metadata {
    name      = "secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    secret_key          = data.google_secret_manager_secret_version.platform_valuation_secret_key.secret_data
    sentry_dsn          = data.sentry_key.default["platform_valuation"].dsn_public
    algo_sentry_dsn     = data.sentry_key.default["platform_valuation_algo"].dsn_public
    auth0_client_id     = data.google_secret_manager_secret_version.platform_classification_auth0_client_id.secret_data
    auth0_client_secret = data.google_secret_manager_secret_version.platform_classification_auth0_client_secret.secret_data
  }
}

data "google_secret_manager_secret_version" "valuation_classification_secret_key" {
  secret  = "valuation_classification_secret_key"
  project = local.project
}

resource "kubernetes_secret" "classification_prediction_service_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "valuation-classification",
  ])
  metadata {
    name      = "cps-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    sentry_dsn = data.sentry_key.default["classification_prediction_service"].dsn_public
    secret_key = data.google_secret_manager_secret_version.valuation_classification_secret_key.secret_data
  }
}

resource "kubernetes_secret" "classification_management_service_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "valuation-classification",
  ])
  metadata {
    name      = "cms-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    sentry_dsn = data.sentry_key.default["classification_management_service"].dsn_public
    secret_key = data.google_secret_manager_secret_version.valuation_classification_secret_key.secret_data
  }
}

data "google_secret_manager_secret_version" "appraissals_classify_db_connection_string_alpha" {
  secret  = "appraissals_classify_db_connection_string_alpha"
  project = local.project
}

data "google_secret_manager_secret_version" "appraissals_classify_db_connection_string_beta" {
  secret  = "appraissals_classify_db_connection_string_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "appraissals_classify_db_connection_string_delta" {
  secret  = "appraissals_classify_db_connection_string_delta"
  project = local.project
}

data "google_secret_manager_secret_version" "appraissals_classify_db_connection_string_develop" {
  secret  = "appraissals_classify_db_connection_string_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "appraissals_classify_auth0_config_develop" {
  secret  = "appraissals_classify_auth0_config_develop"
  project = local.project
}

resource "kubernetes_secret" "appraissals_classify_api_secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "appraisals-classify-api-secrets"
    namespace = "appraisals-classify"
  }

  data = {
    sentry_dsn                  = data.sentry_key.default["appraisals_classify"].dsn_public
    connection_string_alpha     = data.google_secret_manager_secret_version.appraissals_classify_db_connection_string_alpha.secret_data
    connection_string_beta      = data.google_secret_manager_secret_version.appraissals_classify_db_connection_string_beta.secret_data
    connection_string_delta     = data.google_secret_manager_secret_version.appraissals_classify_db_connection_string_delta.secret_data
    connection_string_develop   = data.google_secret_manager_secret_version.appraissals_classify_db_connection_string_develop.secret_data
    auth0_client_id_alpha       = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_alpha   = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_beta        = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_beta    = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_delta       = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_delta   = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_develop     = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_develop = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_secret")
  }
}

resource "kubernetes_secret" "appraissals_classify_service_secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "appraisals-classify-service-secrets"
    namespace = "appraisals-classify"
  }

  data = {
    sentry_dsn                  = data.sentry_key.default["appraisals_classify_service"].dsn_public
    connection_string_alpha     = data.google_secret_manager_secret_version.appraissals_classify_db_connection_string_alpha.secret_data
    connection_string_beta      = data.google_secret_manager_secret_version.appraissals_classify_db_connection_string_beta.secret_data
    connection_string_delta     = data.google_secret_manager_secret_version.appraissals_classify_db_connection_string_delta.secret_data
    connection_string_develop   = data.google_secret_manager_secret_version.appraissals_classify_db_connection_string_develop.secret_data
    auth0_client_id_alpha       = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_alpha   = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_beta        = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_beta    = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_delta       = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_delta   = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_develop     = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_develop = lookup(jsondecode(data.google_secret_manager_secret_version.appraissals_classify_auth0_config_develop.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "sales_portal_db_connection_string_sql_cache_develop" {
  secret  = "sales_portal_db_connection_string_sql_cache_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_db_connection_string_sql_cache_beta" {
  secret  = "sales_portal_db_connection_string_sql_cache_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_db_connection_string_sql_cache_charlie" {
  secret  = "sales_portal_db_connection_string_sql_cache_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_db_connection_string_sql_cache_echo" {
  secret  = "sales_portal_db_connection_string_sql_cache_echo"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_db_connection_string_sql_cache_foxtrot" {
  secret  = "sales_portal_db_connection_string_sql_cache_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_db_connection_string_sql_cache_insi" {
  secret  = "sales_portal_db_connection_string_sql_cache_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_db_connection_string_sql_cache_rc" {
  secret  = "sales_portal_db_connection_string_sql_cache_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_db_connection_string_sql_cache_stage" {
  secret  = "sales_portal_db_connection_string_sql_cache_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_auth0_config_develop" {
  secret  = "sales_portal_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_auth0_config_stage" {
  secret  = "sales_portal_auth0_config_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_identity_relying_party" {
  secret  = "sales_portal_identity_relying_party"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_application_max_mind_license" {
  secret  = "sales_portal_application_max_mind_license"
  project = local.project
}

resource "kubernetes_secret" "sales_portal_secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "sales-portal-secrets"
    namespace = "sales-portal"
  }

  data = {
    sentry_dsn                          = data.sentry_key.default["sales_portal"].dsn_public
    connection_string_sql_cache_beta    = data.google_secret_manager_secret_version.sales_portal_db_connection_string_sql_cache_beta.secret_data
    connection_string_sql_cache_stage   = data.google_secret_manager_secret_version.sales_portal_db_connection_string_sql_cache_stage.secret_data
    connection_string_sql_cache_develop = data.google_secret_manager_secret_version.sales_portal_db_connection_string_sql_cache_develop.secret_data
    connection_string_sql_cache_charlie = data.google_secret_manager_secret_version.sales_portal_db_connection_string_sql_cache_develop.secret_data
    connection_string_sql_cache_echo    = data.google_secret_manager_secret_version.sales_portal_db_connection_string_sql_cache_echo.secret_data
    connection_string_sql_cache_foxtrot = data.google_secret_manager_secret_version.sales_portal_db_connection_string_sql_cache_develop.secret_data
    connection_string_sql_cache_insi    = data.google_secret_manager_secret_version.sales_portal_db_connection_string_sql_cache_develop.secret_data
    connection_string_sql_cache_rc      = data.google_secret_manager_secret_version.sales_portal_db_connection_string_sql_cache_develop.secret_data
    auth0_client_id_develop             = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_develop         = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_beta                = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_beta            = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_charlie             = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_charlie         = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_echo                = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_echo            = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_foxtrot             = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_foxtrot         = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_insi                = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_insi            = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_rc                  = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_rc              = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_stage               = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_stage.secret_data), "client_id")
    auth0_client_secret_stage           = lookup(jsondecode(data.google_secret_manager_secret_version.sales_portal_auth0_config_stage.secret_data), "client_secret")
    identity_relying_party              = data.google_secret_manager_secret_version.sales_portal_identity_relying_party.secret_data
    application_max_mind_license        = data.google_secret_manager_secret_version.sales_portal_application_max_mind_license.secret_data
  }
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_identity_develop" {
  secret  = "sales_netcore_api_db_connection_string_identity_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_identity_beta" {
  secret  = "sales_netcore_api_db_connection_string_identity_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_identity_charlie" {
  secret  = "sales_netcore_api_db_connection_string_identity_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_identity_echo" {
  secret  = "sales_netcore_api_db_connection_string_identity_echo"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_identity_foxtrot" {
  secret  = "sales_netcore_api_db_connection_string_identity_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_identity_insi" {
  secret  = "sales_netcore_api_db_connection_string_identity_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_identity_rc" {
  secret  = "sales_netcore_api_db_connection_string_identity_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_identity_stage" {
  secret  = "sales_netcore_api_db_connection_string_identity_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_rfm_sales_develop" {
  secret  = "sales_netcore_api_db_connection_string_rfm_sales_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_rfm_sales_beta" {
  secret  = "sales_netcore_api_db_connection_string_rfm_sales_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_rfm_sales_charlie" {
  secret  = "sales_netcore_api_db_connection_string_rfm_sales_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_rfm_sales_echo" {
  secret  = "sales_netcore_api_db_connection_string_rfm_sales_echo"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_rfm_sales_foxtrot" {
  secret  = "sales_netcore_api_db_connection_string_rfm_sales_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_rfm_sales_insi" {
  secret  = "sales_netcore_api_db_connection_string_rfm_sales_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_rfm_sales_rc" {
  secret  = "sales_netcore_api_db_connection_string_rfm_sales_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_db_connection_string_rfm_sales_stage" {
  secret  = "sales_netcore_api_db_connection_string_rfm_sales_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_auth0_config_develop" {
  secret  = "sales_netcore_api_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_auth0_config_beta" {
  secret  = "sales_netcore_api_auth0_config_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_auth0_config_charlie" {
  secret  = "sales_netcore_api_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_auth0_config_echo" {
  secret  = "sales_netcore_api_auth0_config_echo"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_auth0_config_foxtrot" {
  secret  = "sales_netcore_api_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_auth0_config_insi" {
  secret  = "sales_netcore_api_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_auth0_config_rc" {
  secret  = "sales_netcore_api_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_auth0_config_stage" {
  secret  = "sales_netcore_api_auth0_config_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_oauth_config_develop" {
  secret  = "sales_netcore_api_oauth_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_fleet_manager_token_beta" {
  secret  = "sales_netcore_api_fleet_manager_token_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_oauth_config_charlie" {
  secret  = "sales_netcore_api_oauth_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_fleet_manager_token_echo" {
  secret  = "sales_netcore_api_fleet_manager_token_echo"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_oauth_config_foxtrot" {
  secret  = "sales_netcore_api_oauth_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_oauth_config_insi" {
  secret  = "sales_netcore_api_oauth_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_oauth_config_rc" {
  secret  = "sales_netcore_api_oauth_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_fleet_manager_token_develop" {
  secret  = "sales_netcore_api_fleet_manager_token_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_fleet_manager_token_stage" {
  secret  = "sales_netcore_api_fleet_manager_token_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_netcore_api_rfm_shards" {
  secret  = "sales_netcore_api_rfm_shards"
  project = local.project
}

resource "kubernetes_secret" "sales_netcore_api_secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "sales-netcore-api-secrets"
    namespace = "sales-api"
  }

  data = {
    sentry_dsn                          = data.sentry_key.default["sales_netcore_api"].dsn_public
    connection_string_rfm_sales_beta    = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_rfm_sales_beta.secret_data
    connection_string_rfm_sales_develop = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_rfm_sales_develop.secret_data
    connection_string_rfm_sales_charlie = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_rfm_sales_develop.secret_data
    connection_string_rfm_sales_echo    = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_rfm_sales_echo.secret_data
    connection_string_rfm_sales_foxtrot = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_rfm_sales_develop.secret_data
    connection_string_rfm_sales_insi    = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_rfm_sales_develop.secret_data
    connection_string_rfm_sales_rc      = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_rfm_sales_develop.secret_data
    connection_string_rfm_sales_stage   = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_rfm_sales_stage.secret_data
    connection_string_identity_develop  = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_identity_develop.secret_data
    connection_string_identity_beta     = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_identity_beta.secret_data
    connection_string_identity_charlie  = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_identity_develop.secret_data
    connection_string_identity_echo     = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_identity_echo.secret_data
    connection_string_identity_foxtrot  = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_identity_develop.secret_data
    connection_string_identity_insi     = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_identity_develop.secret_data
    connection_string_identity_rc       = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_identity_develop.secret_data
    connection_string_identity_stage    = data.google_secret_manager_secret_version.sales_netcore_api_db_connection_string_identity_stage.secret_data
    fleet_manager_token_beta            = data.google_secret_manager_secret_version.sales_netcore_api_fleet_manager_token_beta.secret_data
    fleet_manager_token_develop         = data.google_secret_manager_secret_version.sales_netcore_api_fleet_manager_token_develop.secret_data
    fleet_manager_token_charlie         = data.google_secret_manager_secret_version.sales_netcore_api_fleet_manager_token_develop.secret_data
    fleet_manager_token_echo            = data.google_secret_manager_secret_version.sales_netcore_api_fleet_manager_token_echo.secret_data
    fleet_manager_token_foxtrot         = data.google_secret_manager_secret_version.sales_netcore_api_fleet_manager_token_develop.secret_data
    fleet_manager_token_insi            = data.google_secret_manager_secret_version.sales_netcore_api_fleet_manager_token_develop.secret_data
    fleet_manager_token_rc              = data.google_secret_manager_secret_version.sales_netcore_api_fleet_manager_token_develop.secret_data
    fleet_manager_token_stage           = data.google_secret_manager_secret_version.sales_netcore_api_fleet_manager_token_stage.secret_data

    rfm_shards = data.google_secret_manager_secret_version.sales_netcore_api_rfm_shards.secret_data

    auth0_client_id_develop     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_develop = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_beta        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_beta.secret_data), "client_id")
    auth0_client_secret_beta    = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_beta.secret_data), "client_secret")
    auth0_client_id_charlie     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_charlie = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_echo        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_echo.secret_data), "client_id")
    auth0_client_secret_echo    = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_echo.secret_data), "client_secret")
    auth0_client_id_foxtrot     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_foxtrot = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_insi        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_insi    = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_rc          = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_rc      = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_stage       = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_stage.secret_data), "client_id")
    auth0_client_secret_stage   = lookup(jsondecode(data.google_secret_manager_secret_version.sales_netcore_api_auth0_config_stage.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "sales_proposals_auth0_config_beta" {
  secret  = "sales_proposals_auth0_config_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_auth0_config_charlie" {
  secret  = "sales_proposals_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_auth0_config_echo" {
  secret  = "sales_proposals_auth0_config_echo"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_auth0_config_foxtrot" {
  secret  = "sales_proposals_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_auth0_config_insi" {
  secret  = "sales_proposals_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_auth0_config_rc" {
  secret  = "sales_proposals_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_auth0_config_develop" {
  secret  = "sales_proposals_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_auth0_config_stage" {
  secret  = "sales_proposals_auth0_config_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_users_eqd_beta" {
  secret  = "sales_proposals_sugar_crm_users_eqd_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_users_eqd_charlie" {
  secret  = "sales_proposals_sugar_crm_users_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_users_eqd_echo" {
  secret  = "sales_proposals_sugar_crm_users_eqd_echo"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_users_eqd_foxtrot" {
  secret  = "sales_proposals_sugar_crm_users_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_users_eqd_insi" {
  secret  = "sales_proposals_sugar_crm_users_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_users_eqd_rc" {
  secret  = "sales_proposals_sugar_crm_users_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_users_eqd_develop" {
  secret  = "sales_proposals_sugar_crm_users_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_users_eqd_stage" {
  secret  = "sales_proposals_sugar_crm_users_eqd_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_eqd_develop" {
  secret  = "sales_proposals_sugar_crm_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_eqd_beta" {
  secret  = "sales_proposals_sugar_crm_eqd_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_eqd_charlie" {
  secret  = "sales_proposals_sugar_crm_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_eqd_echo" {
  secret  = "sales_proposals_sugar_crm_eqd_echo"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_eqd_foxtrot" {
  secret  = "sales_proposals_sugar_crm_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_eqd_insi" {
  secret  = "sales_proposals_sugar_crm_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_eqd_rc" {
  secret  = "sales_proposals_sugar_crm_eqd_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_proposals_sugar_crm_eqd_stage" {
  secret  = "sales_proposals_sugar_crm_eqd_stage"
  project = local.project
}

resource "kubernetes_secret" "sales-proposals-secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "sales-proposals-secrets"
    namespace = "sales-proposals"
  }

  data = {
    sentry_dsn                  = data.sentry_key.default["sales_proposals"].dsn_public
    auth0_client_id_develop     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_develop = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_secret")

    auth0_client_id_beta     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_beta.secret_data), "client_id")
    auth0_client_secret_beta = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_beta.secret_data), "client_secret")

    auth0_client_id_charlie     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_charlie = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_secret")

    auth0_client_id_echo     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_echo.secret_data), "client_id")
    auth0_client_secret_echo = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_echo.secret_data), "client_secret")

    auth0_client_id_foxtrot     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_foxtrot = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_secret")

    auth0_client_id_insi     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_insi = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_secret")

    auth0_client_id_rc     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_rc = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_develop.secret_data), "client_secret")

    auth0_client_id_stage     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_stage.secret_data), "client_id")
    auth0_client_secret_stage = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_auth0_config_stage.secret_data), "client_secret")


    sugar_crm_users_eqd_username_develop = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "username")
    sugar_crm_users_eqd_password_develop = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "password")

    sugar_crm_users_eqd_username_beta = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_beta.secret_data), "username")
    sugar_crm_users_eqd_password_beta = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_beta.secret_data), "password")

    sugar_crm_users_eqd_username_charlie = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "username")
    sugar_crm_users_eqd_password_charlie = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "password")

    sugar_crm_users_eqd_username_echo = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_echo.secret_data), "username")
    sugar_crm_users_eqd_password_echo = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_echo.secret_data), "password")

    sugar_crm_users_eqd_username_foxtrot = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "username")
    sugar_crm_users_eqd_password_foxtrot = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "password")

    sugar_crm_users_eqd_username_insi = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "username")
    sugar_crm_users_eqd_password_insi = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "password")

    sugar_crm_users_eqd_username_rc = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "username")
    sugar_crm_users_eqd_password_rc = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_develop.secret_data), "password")

    sugar_crm_users_eqd_username_stage = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_stage.secret_data), "username")
    sugar_crm_users_eqd_password_stage = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_users_eqd_stage.secret_data), "password")


    sugar_crm_eqd_domain_develop        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "domain")
    sugar_crm_eqd_client_id_develop     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_id")
    sugar_crm_eqd_client_secret_develop = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_secret")
    sugar_crm_eqd_use_cache_develop     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "use_cache")

    sugar_crm_eqd_domain_beta        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_beta.secret_data), "domain")
    sugar_crm_eqd_client_id_beta     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_beta.secret_data), "client_id")
    sugar_crm_eqd_client_secret_beta = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_beta.secret_data), "client_secret")
    sugar_crm_eqd_use_cache_beta     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_beta.secret_data), "use_cache")

    sugar_crm_eqd_domain_charlie        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "domain")
    sugar_crm_eqd_client_id_charlie     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_id")
    sugar_crm_eqd_client_secret_charlie = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_secret")
    sugar_crm_eqd_use_cache_charlie     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "use_cache")

    sugar_crm_eqd_domain_echo        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_echo.secret_data), "domain")
    sugar_crm_eqd_client_id_echo     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_echo.secret_data), "client_id")
    sugar_crm_eqd_client_secret_echo = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_echo.secret_data), "client_secret")
    sugar_crm_eqd_use_cache_echo     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_echo.secret_data), "use_cache")

    sugar_crm_eqd_domain_foxtrot        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "domain")
    sugar_crm_eqd_client_id_foxtrot     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_id")
    sugar_crm_eqd_client_secret_foxtrot = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_secret")
    sugar_crm_eqd_use_cache_foxtrot     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "use_cache")

    sugar_crm_eqd_domain_insi        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "domain")
    sugar_crm_eqd_client_id_insi     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_id")
    sugar_crm_eqd_client_secret_insi = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_secret")
    sugar_crm_eqd_use_cache_insi     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "use_cache")

    sugar_crm_eqd_domain_rc        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "domain")
    sugar_crm_eqd_client_id_rc     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_id")
    sugar_crm_eqd_client_secret_rc = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "client_secret")
    sugar_crm_eqd_use_cache_rc     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_develop.secret_data), "use_cache")

    sugar_crm_eqd_domain_stage        = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_stage.secret_data), "domain")
    sugar_crm_eqd_client_id_stage     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_stage.secret_data), "client_id")
    sugar_crm_eqd_client_secret_stage = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_stage.secret_data), "client_secret")
    sugar_crm_eqd_use_cache_stage     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_proposals_sugar_crm_eqd_stage.secret_data), "use_cache")
  }
}

data "google_secret_manager_secret_version" "analytics_rdo_api_connection_string_analytics_alpha" {
  secret  = "analytics_rdo_api_connection_string_analytics_alpha"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_api_connection_string_analytics_beta" {
  secret  = "analytics_rdo_api_connection_string_analytics_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_api_connection_string_analytics_develop" {
  secret  = "analytics_rdo_api_connection_string_analytics_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_api_connection_string_analytics_stage" {
  secret  = "analytics_rdo_api_connection_string_analytics_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_api_connection_string_analytics_noco" {
  secret  = "analytics_rdo_api_connection_string_analytics_noco"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_api_connection_string_customgrids_alpha" {
  secret  = "analytics_rdo_api_connection_string_customgrids_alpha"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_api_connection_string_customgrids_stage" {
  secret  = "analytics_rdo_api_connection_string_customgrids_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_api_connection_string_identity_develop" {
  secret  = "analytics_rdo_api_connection_string_identity_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_api_connection_string_identity_stage" {
  secret  = "analytics_rdo_api_connection_string_identity_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_api_auth0_config_develop" {
  secret  = "analytics_rdo_api_auth0_config_develop"
  project = local.project
}

resource "kubernetes_secret" "analytics_rdo_api_secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "analytics-rdo-api-secrets"
    namespace = "analytics-rdo-api"
  }

  data = {
    sentry_dsn                          = data.sentry_key.default["analytics_rdo_api"].dsn_public
    connection_string_analytics_alpha   = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_analytics_alpha.secret_data
    connection_string_analytics_beta    = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_analytics_beta.secret_data
    connection_string_analytics_develop = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_analytics_develop.secret_data
    connection_string_analytics_stage   = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_analytics_stage.secret_data
    connection_string_analytics_noco    = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_analytics_noco.secret_data
    connection_string_identity_alpha    = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_identity_develop.secret_data
    connection_string_identity_beta     = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_identity_develop.secret_data
    connection_string_identity_develop  = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_identity_develop.secret_data
    connection_string_identity_stage    = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_identity_stage.secret_data
    connection_string_identity_noco     = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_identity_develop.secret_data

    connection_string_customgrids_alpha   = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_customgrids_alpha.secret_data
    connection_string_customgrids_beta    = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_customgrids_stage.secret_data
    connection_string_customgrids_develop = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_customgrids_stage.secret_data
    connection_string_customgrids_stage   = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_customgrids_stage.secret_data
    connection_string_customgrids_noco    = data.google_secret_manager_secret_version.analytics_rdo_api_connection_string_customgrids_stage.secret_data

    auth0_client_id_alpha       = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_alpha   = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_beta        = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_beta    = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_develop     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_develop = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_stage       = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_stage   = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_noco        = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_noco    = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_api_auth0_config_develop.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "rdo_alloydb_certs" {
  secret  = "rdo-alloydb-certs"
  project = local.project
}

resource "kubernetes_secret" "rdo_alloydb_certs" {
  provider = kubernetes.services-dev
  for_each = toset([
    "analytics-rdo-api"
  ])
  metadata {
    name      = "rdo-alloydb-certs"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = lookup(jsondecode(data.google_secret_manager_secret_version.rdo_alloydb_certs.secret_data), "tls.key")
    crt = lookup(jsondecode(data.google_secret_manager_secret_version.rdo_alloydb_certs.secret_data), "tls.crt")
    ca  = lookup(jsondecode(data.google_secret_manager_secret_version.rdo_alloydb_certs.secret_data), "tls.ca")
  }
}

data "google_secret_manager_secret_version" "analytics_netcore_data_extractor_server_auth0_config_develop" {
  secret  = "analytics_netcore_data_extractor_server_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_netcore_data_extractor_server_connection_string_dtt_develop" {
  secret  = "analytics_netcore_data_extractor_server_connection_string_dtt_develop"
  project = local.project
}

resource "kubernetes_secret" "analytics_netcore_data_extractor_server_secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "analytics-netcore-data-extractor-server-secrets"
    namespace = "analytics-dtt"
  }

  data = {
    sentry_dsn            = data.sentry_key.default["analytics_netcore_data_extractor_server"].dsn_public
    connection_string_dtt = data.google_secret_manager_secret_version.analytics_netcore_data_extractor_server_connection_string_dtt_develop.secret_data

    auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_netcore_data_extractor_server_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_netcore_data_extractor_server_auth0_config_develop.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "appraisals_valuator_auth0_config_develop" {
  secret  = "appraisals_valuator_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "appraisals_valuator_auth0_config_stage" {
  secret  = "appraisals_valuator_auth0_config_stage"
  project = local.project
}

resource "kubernetes_secret" "appraisals_valuator_secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "appraisals-valuator-secrets"
    namespace = "appraisals-valuator"
  }

  data = {
    sentry_dsn = data.sentry_key.default["appraisals_valuator"].dsn_public

    auth0_client_id_beta        = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_valuator_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_beta    = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_valuator_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_develop     = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_valuator_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_develop = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_valuator_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_stage       = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_valuator_auth0_config_stage.secret_data), "client_id")
    auth0_client_secret_stage   = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_valuator_auth0_config_stage.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "enterprise_authentication_auth0_config_beta" {
  secret  = "enterprise_authentication_auth0_config_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_auth0_config_develop" {
  secret  = "enterprise_authentication_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_auth0_config_stage" {
  secret  = "enterprise_authentication_auth0_config_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_db_connection_string_identity_beta" {
  secret  = "enterprise_authentication_db_connection_string_identity_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_db_connection_string_identity_develop" {
  secret  = "enterprise_authentication_db_connection_string_identity_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_db_connection_string_identity_stage" {
  secret  = "enterprise_authentication_db_connection_string_identity_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_appsecrets_json_beta" {
  secret  = "enterprise_authentication_appsecrets_json_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_appsecrets_json_develop" {
  secret  = "enterprise_authentication_appsecrets_json_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_appsecrets_json_stage" {
  secret  = "enterprise_authentication_appsecrets_json_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_super_secret" {
  secret  = "enterprise_authentication_super_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_jwt_privatekey_beta" {
  secret  = "enterprise_authentication_jwt_privatekey_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_jwt_privatekey_develop" {
  secret  = "enterprise_authentication_jwt_privatekey_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_jwt_privatekey_stage" {
  secret  = "enterprise_authentication_jwt_privatekey_stage"
  project = local.project
}

resource "kubernetes_secret" "enterprise_authentication_secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "enterprise-authentication-secrets"
    namespace = "enterprise-authentication"
  }

  data = {
    sentry_dsn = data.sentry_key.default["enterprise_authentication"].dsn_public

    auth0_client_id_beta        = lookup(jsondecode(data.google_secret_manager_secret_version.enterprise_authentication_auth0_config_beta.secret_data), "client_id")
    auth0_client_secret_beta    = lookup(jsondecode(data.google_secret_manager_secret_version.enterprise_authentication_auth0_config_beta.secret_data), "client_secret")
    auth0_client_id_develop     = lookup(jsondecode(data.google_secret_manager_secret_version.enterprise_authentication_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_develop = lookup(jsondecode(data.google_secret_manager_secret_version.enterprise_authentication_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_stage       = lookup(jsondecode(data.google_secret_manager_secret_version.enterprise_authentication_auth0_config_stage.secret_data), "client_id")
    auth0_client_secret_stage   = lookup(jsondecode(data.google_secret_manager_secret_version.enterprise_authentication_auth0_config_stage.secret_data), "client_secret")

    connection_string_identity_beta    = data.google_secret_manager_secret_version.enterprise_authentication_db_connection_string_identity_beta.secret_data
    connection_string_identity_develop = data.google_secret_manager_secret_version.enterprise_authentication_db_connection_string_identity_develop.secret_data
    connection_string_identity_stage   = data.google_secret_manager_secret_version.enterprise_authentication_db_connection_string_identity_stage.secret_data

    appsecrets_json_beta    = data.google_secret_manager_secret_version.enterprise_authentication_appsecrets_json_beta.secret_data
    appsecrets_json_develop = data.google_secret_manager_secret_version.enterprise_authentication_appsecrets_json_develop.secret_data
    appsecrets_json_stage   = data.google_secret_manager_secret_version.enterprise_authentication_appsecrets_json_stage.secret_data

    authentication_super_secret_beta    = data.google_secret_manager_secret_version.enterprise_authentication_super_secret.secret_data
    authentication_super_secret_develop = data.google_secret_manager_secret_version.enterprise_authentication_super_secret.secret_data
    authentication_super_secret_stage   = data.google_secret_manager_secret_version.enterprise_authentication_super_secret.secret_data

    jwtauthorization_privatekey_beta    = data.google_secret_manager_secret_version.enterprise_authentication_jwt_privatekey_beta.secret_data
    jwtauthorization_privatekey_develop = data.google_secret_manager_secret_version.enterprise_authentication_jwt_privatekey_develop.secret_data
    jwtauthorization_privatekey_stage   = data.google_secret_manager_secret_version.enterprise_authentication_jwt_privatekey_stage.secret_data
  }
}

data "google_secret_manager_secret_version" "sendgrid_smtp_relay_services" {
  secret  = "sendgrid_smtp_relay_services"
  project = local.project
}

resource "kubernetes_secret" "sendgrid_smtp_relay_services" {
  provider = kubernetes.services-dev
  for_each = toset([
    "analytics-rdo-api",
  ])
  metadata {
    name      = "sendgrid-smtp-relay-services"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.sendgrid_smtp_relay_services.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.sendgrid_smtp_relay_services.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "bus_host_config" {
  secret  = "bus_host_config"
  project = local.project
}

resource "kubernetes_secret" "bus_host_config_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-classify",
  ])
  metadata {
    name      = "bus-host-config"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.bus_host_config.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.bus_host_config.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "sales_rca_pagerduty_secret" {
  secret = "sales_channels_api_pagerduty_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_confluent_kafka_key" {
  secret  = "sales_rfm_channels_api_confluent_key"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_confluent_kafka_secret" {
  secret  = "sales_rfm_channels_api_confluent_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_confluent_schema_registry_key" {
  secret  = "sales_rfm_channels_api_confluent_schema_reg_key"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_confluent_schema_registry_secret" {
  secret  = "sales_rfm_channels_api_confluent_schema_reg_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_iron_planet_api_auth" {
  secret  = "sales_iron_planet_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_api_auth" {
  secret  = "sales_fleet_manager_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_api_auth_prod" {
  secret  = "sales_fleet_manager_auth_token"
  project = data.terraform_remote_state.project_structure.outputs.projects["services-prod"]
}

data "google_secret_manager_secret_version" "sales_fleet_manager_gcp_auth0_credentials" {
  secret  = "sales_fleet_manager_auth0_credentials"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_confluent_kafka_key" {
  secret  = "enterprise_domain_service_confluent_key"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_confluent_kafka_dev_sa_key" {
  secret  = "confluent-kafka-dev-sa-key"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_confluent_kafka_dev_sa_secret" {
  secret  = "confluent-kafka-dev-sa-secret"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_confluent_kafka_dev_schema_reg_key" {
  secret  = "confluent-kafka-dev-sa-schema-reg-key"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_confluent_kafka_dev_schema_reg_secret" {
  secret  = "confluent-kafka-dev-sa-schema-reg-secret"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_confluent_kafka_secret" {
  secret  = "enterprise_domain_service_confluent_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_confluent_schema_registry_key" {
  secret  = "enterprise_domain_service_confluent_schema_reg_key"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_confluent_schema_registry_secret" {
  secret  = "enterprise_domain_service_api_confluent_schema_reg_secret"
  project = local.project
}

resource "kubernetes_secret" "sales_fleet_manager_auth0_credentials" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-fleet-manager",
    "fleet-manager-integrations",
  ])
  metadata {
    name      = "sales-fleet-manager-auth0-credentials"
    namespace = each.key
  }
  data = {
    "client_id" : jsondecode(data.google_secret_manager_secret_version.sales_fleet_manager_gcp_auth0_credentials.secret_data)["client_id"]
    "client_secret" : jsondecode(data.google_secret_manager_secret_version.sales_fleet_manager_gcp_auth0_credentials.secret_data)["client_secret"]
  }
}

data "google_secret_manager_secret_version" "sales_classification_api_auth" {
  secret  = "sales_classification_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_classification_gcp_auth0_credentials" {
  secret  = "sales_classification_auth0_credentials"
  project = local.project
}

resource "kubernetes_secret" "sales_sales_classification_auth0_credentials" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-classification",
  ])
  metadata {
    name      = "sales-classification-auth0-credentials"
    namespace = each.key
  }
  data = {
    "client_id" : jsondecode(data.google_secret_manager_secret_version.sales_classification_gcp_auth0_credentials.secret_data)["client_id"]
    "client_secret" : jsondecode(data.google_secret_manager_secret_version.sales_classification_gcp_auth0_credentials.secret_data)["client_secret"]
  }
}

data "google_secret_manager_secret_version" "platform_user_config_api_auth" {
  secret  = "platform_user_config_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_user_config_ga4_credentials_key" {
  secret  = "platform_user_config_ga4_credentials_key"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_user_config_default_webshop_credentials_key" {
  secret  = "platform_user_config_default_webshop_credentials_key"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_user_config_sso_integration_token" {
  secret  = "platform_user_config_sso_integration_token"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_user_config_api_auth_prod" {
  secret  = "platform_user_config_auth_token"
  project = data.terraform_remote_state.project_structure.outputs.projects["services-prod"]
}

data "google_secret_manager_secret_version" "platform_user_config_auth0_credentials" {
  secret  = "platform_user_config_auth0_credentials"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_classification_api_auth" {
  secret  = "platform_classification_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sendgrid_api_key" {
  secret  = "sendgrid-api-key"
  project = local.project
}

resource "kubernetes_secret" "sendgrid_api_key" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-portal",
  ])
  metadata {
    name      = "sendgrid-api-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = data.google_secret_manager_secret_version.sendgrid_api_key.secret_data
  }
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_db_user" {
  secret  = "dbuser_fleet_manager_tools"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_secret_key" {
  secret  = "sales_fleet_manager_tools_secret_key"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_airflow_api_token" {
  secret  = "sales_fleet_manager_tools_airflow_api_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_auth0_client_id" {
  secret  = "sales_fleet_manager_tools_auth0_client_id"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_auth0_client_secret" {
  secret  = "sales_fleet_manager_tools_auth0_client_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_stage_auth0_client_id" {
  secret  = "sales_fleet_manager_tools_stage_auth0_client_id"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_stage_auth0_client_secret" {
  secret  = "sales_fleet_manager_tools_stage_auth0_client_secret"
  project = local.project
}

resource "kubernetes_secret" "fleet_manager_tools_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-fleet-manager-tools",
  ])
  metadata {
    name      = "fleet-manager-tools-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    db_username           = lookup(jsondecode(data.google_secret_manager_secret_version.sales_fleet_manager_tools_db_user.secret_data), "username")
    db_password           = lookup(jsondecode(data.google_secret_manager_secret_version.sales_fleet_manager_tools_db_user.secret_data), "password")
    secret_key            = data.google_secret_manager_secret_version.sales_fleet_manager_tools_secret_key.secret_data
    airflow_api_token_dev = data.google_secret_manager_secret_version.sales_fleet_manager_tools_airflow_api_token.secret_data
    # these will be removed once the move to platform-user-config is complete
    auth0_client_id_dev     = data.google_secret_manager_secret_version.sales_fleet_manager_tools_auth0_client_id.secret_data
    auth0_client_secret_dev = data.google_secret_manager_secret_version.sales_fleet_manager_tools_auth0_client_secret.secret_data
  }
}

data "google_secret_manager_secret_version" "sales_classification_db_user" {
  secret  = "dbuser_sales_classification"
  project = local.project
}
data "google_secret_manager_secret_version" "sales_classification_secrets" {
  secret  = "sales-classification-secrets"
  project = local.project
}

resource "kubernetes_secret" "sales_classification_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-classification",
  ])
  metadata {
    name      = "sales-classification-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    db_username = lookup(jsondecode(data.google_secret_manager_secret_version.sales_classification_db_user.secret_data), "username")
    db_password = lookup(jsondecode(data.google_secret_manager_secret_version.sales_classification_db_user.secret_data), "password")
    secret_key  = lookup(jsondecode(data.google_secret_manager_secret_version.sales_classification_secrets.secret_data), "secret_key")
    sentry_dsn  = lookup(jsondecode(data.google_secret_manager_secret_version.sales_classification_secrets.secret_data), "sentry_dsn")
  }

}

data "google_secret_manager_secret_version" "analytics_rdo_scheduler_auth0_config" {
  secret  = "analytics_rdo_scheduler_auth0_config"
  project = local.project
}

resource "google_secret_manager_secret_iam_member" "platform_services_ci" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.analytics_rdo_scheduler_auth0_config.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${data.terraform_remote_state.project_structure.outputs.platform_services_ci}"
}

resource "kubernetes_secret" "auth0-credentials" {
  provider = kubernetes.services-dev
  metadata {
    name      = "auth0-credentials"
    namespace = "analytics-metrics"
  }

  data = {
    auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_scheduler_auth0_config.secret_data), "client_id")
    auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_scheduler_auth0_config.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "pgbouncer_monitoring_user" {
  secret  = "pgbouncer_monitoring_user"
  project = local.project
}

resource "kubernetes_secret" "pgbouncer_alloydb_monitoring_user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "platform-alloydb",
    "enterprise-pgbouncer-rfm03",
    "enterprise-pgbouncer-platform",
    "enterprise-pgbouncer-alloydb",
    "enterprise-pgbouncer-rdo101",
  ])
  metadata {
    name      = "pgbouncer-pgbouncer-monitoring-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "user" : jsondecode(data.google_secret_manager_secret_version.pgbouncer_monitoring_user.secret_data)["user"]
    "password" : jsondecode(data.google_secret_manager_secret_version.pgbouncer_monitoring_user.secret_data)["password"]
  }
}

resource "kubernetes_secret" "pgbouncer_rfm04_monitoring_user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "enterprise-pgbouncer-rfm04",
  ])
  metadata {
    name      = "pgbouncer-pgbouncer-monitoring-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "user" : jsondecode(data.google_secret_manager_secret_version.pgbouncer_monitoring_user.secret_data)["user"]
    "password" : jsondecode(data.google_secret_manager_secret_version.pgbouncer_monitoring_user.secret_data)["password"]
  }
}

resource "kubernetes_secret" "pgbouncer_rfm_services01_monitoring_user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "pgbouncer-rfmservices01",
  ])
  metadata {
    name      = "pgbouncer-pgbouncer-monitoring-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "user" : jsondecode(data.google_secret_manager_secret_version.pgbouncer_monitoring_user.secret_data)["user"]
    "password" : jsondecode(data.google_secret_manager_secret_version.pgbouncer_monitoring_user.secret_data)["password"]
  }
}

resource "kubernetes_secret" "pgbouncer_rfm07_monitoring_user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "enterprise-pgbouncer-rfm07",
  ])
  metadata {
    name      = "pgbouncer-pgbouncer-monitoring-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "user" : jsondecode(data.google_secret_manager_secret_version.pgbouncer_monitoring_user.secret_data)["user"]
    "password" : jsondecode(data.google_secret_manager_secret_version.pgbouncer_monitoring_user.secret_data)["password"]
  }
}

data "google_secret_manager_secret_version" "appraisals_cloud_messaging_secret" {
  secret  = "cloudmessaging-server-key"
  project = local.project
}

resource "kubernetes_secret" "appraisals_cloud_messaging_kubernetes_secret" {
  provider = kubernetes.services-dev
  metadata {
    name      = "cloudmessaging-server-key"
    namespace = "appraisals-portal"
  }

  data = {
    key = data.google_secret_manager_secret_version.appraisals_cloud_messaging_secret.secret_data
  }
}

data "google_secret_manager_secret_version" "dromo_frontend_token" {
  secret  = "dromo_frontend_key"
  project = local.project
}


resource "kubernetes_secret" "dromo_frontend_token" {
  provider = kubernetes.services-dev

  for_each = toset([
    "sales-txns",
    "sales-ingest"
  ])

  metadata {
    name      = "dromo-frontend-token"
    namespace = each.key
  }

  data = {
    token = data.google_secret_manager_secret_version.dromo_frontend_token.secret_data
  }
}

data "google_secret_manager_secret_version" "svc_kerberos_test" {
  secret  = "svc_kerberos_test"
  project = local.project
}

resource "kubernetes_secret" "svc_kerberos_test_secret" {
  provider = kubernetes.services-dev

  for_each = toset([
    "default",
    "sales-portal",
    "sales-api",
    "sales-cge",
    "sales-cache",
    "appraisals-residuals",
    "enterprise-messaging",
    "enterprise-notification",
    "appraisals-coda",
    "appraisals-valuator",
    "analytics-api",
    "analytics-rdo-api",
  ])

  metadata {
    name      = "svc-kerberos-test-secret"
    namespace = each.key
  }

  data = {
    username = jsondecode(data.google_secret_manager_secret_version.svc_kerberos_test.secret_data)["username"]
    password = jsondecode(data.google_secret_manager_secret_version.svc_kerberos_test.secret_data)["password"]
  }
}

data "google_secret_manager_secret_version" "platform_service_template_example_secret" {
  secret  = "platform_service_template_example"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_service_template_secret_key_secret" {
  secret  = "platform_service_template_secret_key"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_service_template_dbuser_secret" {
  secret  = "dbuser_platform_service_template"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_service_template_auth_token_secret" {
  secret  = "platform_service_template_auth_token"
  project = local.project
}

resource "kubernetes_secret" "platform_service_template_secrets" {
  provider = kubernetes.services-dev

  for_each = toset([
    "platform-service-template",
  ])

  metadata {
    name      = "platform-service-template-secrets"
    namespace = each.key
  }

  data = {
    db_username = lookup(jsondecode(data.google_secret_manager_secret_version.platform_service_template_dbuser_secret.secret_data), "username")
    db_password = lookup(jsondecode(data.google_secret_manager_secret_version.platform_service_template_dbuser_secret.secret_data), "password")
    example     = data.google_secret_manager_secret_version.platform_service_template_example_secret.secret_data
    secret_key  = data.google_secret_manager_secret_version.platform_service_template_secret_key_secret.secret_data
    sentry_dsn  = data.sentry_key.default["platform_service_template"].dsn_public
  }
}

data "google_secret_manager_secret_version" "sales_fmx_api_dbuser_secret" {
  secret  = "dbuser_sales_fmx_api"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_rfm_channels_api_dbuser_secret" {
  secret  = "dbuser_sales_rfm_channels_api"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_rfm_channels_api_kafka_secret" {
  secret  = "sales_rfm_channels_api_kafka_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_rfm_channels_api_mascus_secret" {
  secret  = "sales_rfm_channels_api_mascus_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "equipment_rfm_api_dbuser_map_secret" {
  secret  = "dbuser_map_rfm_equipment_rfm_api"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_rfm_channels_api_auth_token_secret" {
  secret  = "sales_rfm_channels_api_auth_token" # pragma: allowlist secret
  project = local.project
}

data "google_secret_manager_secret_version" "sales_rfm_channels_api_secret_key_secret" {
  secret  = "sales_rfm_channels_api_secret_key"
  project = local.project
}

resource "kubernetes_secret" "sales_rfm_channels_api_secrets" {
  provider = kubernetes.services-dev

  for_each = toset([
    "sales-rfm-channels-api"
  ])

  metadata {
    name      = "sales-rfm-channels-api-secrets"
    namespace = each.key
  }

  data = {
    db_username             = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rfm_channels_api_dbuser_secret.secret_data), "username")
    db_password             = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rfm_channels_api_dbuser_secret.secret_data), "password")
    rfm_global_db_username  = lookup(jsondecode(data.google_secret_manager_secret_version.equipment_rfm_api_dbuser_map_secret.secret_data), "username")
    rfm_global_db_password  = lookup(jsondecode(data.google_secret_manager_secret_version.equipment_rfm_api_dbuser_map_secret.secret_data), "password")
    kafka_sasl_password     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rfm_channels_api_kafka_secret.secret_data), "kafka_sasl_password")
    kafka_sasl_username     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rfm_channels_api_kafka_secret.secret_data), "kafka_sasl_username")
    kafka_schema_user_info  = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rfm_channels_api_kafka_secret.secret_data), "kafka_schema_user_info")
    mascus_credits_password = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rfm_channels_api_mascus_secret.secret_data), "mascus_credits_password")
    mascus_credits_user     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rfm_channels_api_mascus_secret.secret_data), "mascus_credits_user")
    mascus_listing_password = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rfm_channels_api_mascus_secret.secret_data), "mascus_listing_password")
    mascus_listing_user     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rfm_channels_api_mascus_secret.secret_data), "mascus_listing_user")
    secret_key              = data.google_secret_manager_secret_version.sales_rfm_channels_api_secret_key_secret.secret_data
    sentry_dsn              = data.sentry_key.default["sales_rfm_channels_api"].dsn_public
  }
}

data "google_secret_manager_secret_version" "sales_fmx_api_secret_key_secret" {
  secret  = "sales_fmx_api_secret_key"
  project = local.project
}

resource "kubernetes_secret" "sales_fmx_api_secrets" {
  provider = kubernetes.services-dev

  for_each = toset([
    "sales-fmx-api"
  ])

  metadata {
    name      = "sales-fmx-api-secrets"
    namespace = each.key
  }

  data = {
    db_username             = lookup(jsondecode(data.google_secret_manager_secret_version.sales_fmx_api_dbuser_secret.secret_data), "username")
    db_password             = lookup(jsondecode(data.google_secret_manager_secret_version.sales_fmx_api_dbuser_secret.secret_data), "password")
    secret_key              = data.google_secret_manager_secret_version.sales_fmx_api_secret_key_secret.secret_data
    sentry_dsn              = data.sentry_key.default["sales_fmx_api"].dsn_public
  }
}

data "google_secret_manager_secret_version" "enterprise_media_service_static_auth_token" {
  secret  = "enterprise_media_service_static_auth_token"
  project = local.project
}

resource "kubernetes_secret" "enterprise_media_service_api_secrets" {
  provider = kubernetes.services-dev

  for_each = toset([
    "enterprise-media-service",
    "enterprise-domain-service",
  ])

  metadata {
    name      = "enterprise-media-service-secrets"
    namespace = each.key
  }

  data = {
    static_auth_token = data.google_secret_manager_secret_version.enterprise_media_service_static_auth_token.secret_data
  }
}

data "google_secret_manager_secret_version" "classification-manager-service-secret-key_secret" {
  secret  = "classification-manager-service-secret-key"
  project = local.project
}

resource "kubernetes_secret" "classification-manager-service-secret-key_secret" {
  provider = kubernetes.services-dev

  for_each = toset([
    "valuation-classification"
  ])

  metadata {
    name      = "classification-manager-service-secret-key"
    namespace = each.key
  }

  data = {
    auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.classification-manager-service-secret-key_secret.secret_data), "clientId")
    auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.classification-manager-service-secret-key_secret.secret_data), "clientSecret")
    username            = lookup(jsondecode(data.google_secret_manager_secret_version.classification-manager-service-secret-key_secret.secret_data), "username")
  }
}

# Updated 2024-09-18
data "google_secret_manager_secret_version" "ims-shard-map-gcp" {
  secret  = "ims_shard_map"
  project = local.project
}

resource "kubernetes_secret" "ims-shard-map" {
  provider = kubernetes.services-dev

  for_each = toset([
    "fleet-manager-integrations",
    "platform-user-config",
    "sales-catalog",
    "sales-fleet-manager",
    "sales-fleet-manager-tools",
    "sales-fmx-api",
    "sales-ingest",
    "sales-webshop",
    "sales-txns",
  ])

  metadata {
    name      = "ims-shard-map"
    namespace = each.key
  }

  data = {
    shard_map = data.google_secret_manager_secret_version.ims-shard-map-gcp.secret_data
  }
}

data "google_secret_manager_secret_version" "ims-shard-map-haproxy-gcp" {
  secret  = "ims_shard_map_haproxy"
  project = local.project
}

resource "kubernetes_secret" "ims-shard-map-haproxy" {
  provider = kubernetes.services-dev

  for_each = toset([
    "sales-txns",
  ])

  metadata {
    name      = "ims-shard-map-haproxy"
    namespace = each.key
  }

  data = {
    shard_map = data.google_secret_manager_secret_version.ims-shard-map-haproxy-gcp.secret_data
  }
}

resource "kubernetes_secret" "platform_user_service_auth_token_secret" {
  provider = kubernetes.services-dev

  for_each = toset([
    "enterprise-authentication",
    "sales-api",
    "sales-proposals",
  ])

  metadata {
    name      = "user-service-auth-token"
    namespace = each.key
  }

  data = {
    token    = data.google_secret_manager_secret_version.platform_user_config_api_auth.secret_data
    tokenSSO = data.google_secret_manager_secret_version.platform_user_config_sso_integration_token.secret_data
  }
}

data "google_secret_manager_secret_version" "multi_tenant_api_key" {
  secret  = "multi_tenant_api_key"
  project = local.project
}

resource "kubernetes_secret" "multi_tenant_api_key" {
  provider = kubernetes.services-dev

  for_each = toset([
    "sales-txns",
    "sales-ingest",
  ])

  metadata {
    name      = "multi-tenant-api-key"
    namespace = each.key
  }

  data = {
    value = data.google_secret_manager_secret_version.multi_tenant_api_key.secret_data
  }
}

data "google_secret_manager_secret_version" "legacy_api_v2_auth0_config_alpha" {
  secret  = "legacy_api_v2_auth0_config_alpha"
  project = local.project
}

data "google_secret_manager_secret_version" "legacy_api_v2_auth0_config_beta" {
  secret  = "legacy_api_v2_auth0_config_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "legacy_api_v2_auth0_config_develop" {
  secret  = "legacy_api_v2_auth0_config_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "legacy_api_v2_auth0_config_stage" {
  secret  = "legacy_api_v2_auth0_config_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "legacy_api_v2_connectionstring_legacydb_alpha" {
  secret  = "legacy_api_v2_connectionstring_legacydb_alpha"
  project = local.project
}

data "google_secret_manager_secret_version" "legacy_api_v2_connectionstring_legacydb_beta" {
  secret  = "legacy_api_v2_connectionstring_legacydb_beta"
  project = local.project
}

data "google_secret_manager_secret_version" "legacy_api_v2_connectionstring_legacydb_develop" {
  secret  = "legacy_api_v2_connectionstring_legacydb_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "legacy_api_v2_connectionstring_legacydb_stage" {
  secret  = "legacy_api_v2_connectionstring_legacydb_stage"
  project = local.project
}

resource "kubernetes_secret" "legacy_api_v2_secrets" {

  provider = kubernetes.services-dev

  for_each = toset([
    "appraisals-legacy-api-v2",
  ])

  metadata {
    name      = "legacy-api-v2-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    sentry_dsn                        = data.sentry_key.default["appraisals_legacy_api_v2"].dsn_public
    auth0_client_id_alpha             = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_alpha.secret_data), "client_id")
    auth0_client_secret_alpha         = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_alpha.secret_data), "client_secret")
    auth0_client_id_beta              = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_beta.secret_data), "client_id")
    auth0_client_secret_beta          = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_beta.secret_data), "client_secret")
    auth0_client_id_develop           = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret_develop       = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_develop.secret_data), "client_secret")
    auth0_client_id_stage             = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_stage.secret_data), "client_id")
    auth0_client_secret_stage         = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_stage.secret_data), "client_secret")
    connectionstring_legacydb_alpha   = data.google_secret_manager_secret_version.legacy_api_v2_connectionstring_legacydb_alpha.secret_data
    connectionstring_legacydb_beta    = data.google_secret_manager_secret_version.legacy_api_v2_connectionstring_legacydb_beta.secret_data
    connectionstring_legacydb_develop = data.google_secret_manager_secret_version.legacy_api_v2_connectionstring_legacydb_develop.secret_data
    connectionstring_legacydb_stage   = data.google_secret_manager_secret_version.legacy_api_v2_connectionstring_legacydb_stage.secret_data
  }
}

resource "kubernetes_secret" "appraisals_portal_secrets" {

  provider = kubernetes.services-dev

  for_each = toset([
    "appraisals-portal",
  ])

  metadata {
    name      = "appraisals-portal-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_develop.secret_data), "client_id")
    auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.legacy_api_v2_auth0_config_develop.secret_data), "client_secret")

  }
}
data "google_secret_manager_secret_version" "pgbouncer_rfm02_certs" {
  secret  = "pgbouncer-rfm02-certs"
  project = local.project
}

data "google_secret_manager_secret_version" "pgbouncer_rfm03_certs" {
  secret  = "pgbouncer-rfm03-certs"
  project = local.project
}

data "google_secret_manager_secret_version" "pgbouncer_rfm04_certs" {
  secret  = "pgbouncer-rfm04-certs"
  project = local.project
}

data "google_secret_manager_secret_version" "pgbouncer_rfm07_certs" {
  secret  = "pgbouncer-rfm07-certs"
  project = local.project
}

data "google_secret_manager_secret_version" "db_password" {
  secret  = "dbuser_sales_fleet_manager"
  project = local.project
}

data "google_secret_manager_secret_version" "platform01_certs" {
  secret  = "platform01_certs"
  project = local.project
}

data "google_secret_manager_secret_version" "dbuser_platform_user_config" {
  secret  = "dbuser_platform_user_config_dev"
  project = local.project
}

resource "kubernetes_secret" "rfm_secrets" {

  provider = kubernetes.services-dev

  for_each = toset([
    "platform-valuation",
    "sales-ims-migrations"
  ])

  metadata {
    name      = "pg-bouncer-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "pgbouncer_rfm02_client-cert.pem" = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfm02_certs.secret_data), "tls.crt")
    "pgbouncer_rfm02_client-key.pem"  = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfm02_certs.secret_data), "tls.key")
    "server-ca.pem"                   = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfm02_certs.secret_data), "tls.ca")
    "pgbouncer_rfm03_client-key.pem"  = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfm03_certs.secret_data), "tls.key")
    "pgbouncer_rfm03_client-cert.pem" = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfm03_certs.secret_data), "tls.crt")
    "pgbouncer_rfm04_client-key.pem"  = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfm04_certs.secret_data), "tls.key")
    "pgbouncer_rfm04_client-cert.pem" = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfm04_certs.secret_data), "tls.crt")
    "pgbouncer_rfm07_client-cert.pem" = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfm07_certs.secret_data), "tls.crt")
    "pgbouncer_rfm07_client-key.pem"  = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfm07_certs.secret_data), "tls.key")
    "platform01-cert.pem"             = lookup(jsondecode(data.google_secret_manager_secret_version.platform01_certs.secret_data), "tls.crt")
    "platform01-key.pem"              = lookup(jsondecode(data.google_secret_manager_secret_version.platform01_certs.secret_data), "tls.key")
    "username"                        = lookup(jsondecode(data.google_secret_manager_secret_version.db_password.secret_data), "username")
    "password"                        = lookup(jsondecode(data.google_secret_manager_secret_version.db_password.secret_data), "password")
    "username_config"                 = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_platform_user_config.secret_data), "username")
    "password_config"                 = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_platform_user_config.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "analytics_firestore_api_key_dev" {
  secret  = "analytics_firestore_api_key_dev"
  project = local.project
}

resource "kubernetes_secret" "analytics_firestore_api_key_dev" {
  provider = kubernetes.services-dev

  metadata {
    name      = "analytics-firestore-api-key-dev"
    namespace = kubernetes_namespace.workload_identity["analytics-rdo-ui"].metadata[0].name
  }

  data = {
    api_keys = data.google_secret_manager_secret_version.analytics_firestore_api_key_dev.secret_data
  }
}

data "google_secret_manager_secret_version" "cms_svc_classification_manager" {
  secret  = "cms_svc_classification_manager"
  project = local.project
}

resource "kubernetes_secret" "cms_svc_classification_manager_secrets" {

  provider = kubernetes.services-dev

  for_each = toset([
    "valuation-classification",
  ])

  metadata {
    name      = "cms-svc-classification-manager"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "DB_USER"     = lookup(jsondecode(data.google_secret_manager_secret_version.cms_svc_classification_manager.secret_data), "user")
    "DB_PASSWORD" = lookup(jsondecode(data.google_secret_manager_secret_version.cms_svc_classification_manager.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "appraisals_user_manager_secret_key" {
  secret  = "appraisals-user-manager-secret-key"
  project = local.project
}

resource "kubernetes_secret" "appraisals_user_manager_secret_key" {
  provider = kubernetes.services-dev

  metadata {
    name      = "appraisals-user-manager-secret-key"
    namespace = kubernetes_namespace.workload_identity["appraisals-portal"].metadata[0].name
  }

  data = {
    auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_user_manager_secret_key.secret_data), "auth0_client_id")
    auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_user_manager_secret_key.secret_data), "auth0_client_secret")
  }
}

resource "kubernetes_secret" "dcs_user_manager_secret_key" {
  provider = kubernetes.services-dev

  metadata {
    name      = "dcs-user-manager-secret-key"
    namespace = kubernetes_namespace.workload_identity["appraisals-dcs-api"].metadata[0].name
  }

  data = {
    auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_user_manager_secret_key.secret_data), "auth0_client_id")
    auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_user_manager_secret_key.secret_data), "auth0_client_secret")
  }
}

data "google_secret_manager_secret_version" "analytics_public_api_sentry_integration_dsn" {
  secret  = "analytics-public-api-sentry-integration-dsn"
  project = local.project
}

resource "kubernetes_secret" "analytics_public_api_sentry_integration_dsn" {
  provider = kubernetes.services-dev

  metadata {
    name      = "analytics-public-api-sentry-integration-dsn"
    namespace = kubernetes_namespace.workload_identity["analytics-public-py-api"].metadata[0].name
  }

  data = {
    key = data.google_secret_manager_secret_version.analytics_public_api_sentry_integration_dsn.secret_data
  }
}
data "google_secret_manager_secret_version" "sales_catalog_api_static_auth_token" {
  secret  = "sales_catalog_api_static_auth_token"
  project = local.project
}

resource "kubernetes_secret" "sales_catalog_api_static_auth_token" {
  for_each = toset(["sales-txns", "sales-ingest", "sales-catalog", "sales-webshop", "sales-fleet-manager"])
  provider = kubernetes.services-dev

  metadata {
    name      = "sales-catalog-api-static-auth-token"
    namespace = each.key
  }

  data = {
    key = data.google_secret_manager_secret_version.sales_catalog_api_static_auth_token.secret_data
  }
}

data "google_secret_manager_secret_version" "sales_ingest_api_static_auth_token" {
  secret  = "sales_ingest_api_static_auth_token"
  project = local.project
}

resource "kubernetes_secret" "sales_ingest_api_static_auth_token" {
  for_each = toset(["sales-txns", "sales-ingest", "sales-catalog", "sales-webshop", "sales-fleet-manager"])
  provider = kubernetes.services-dev

  metadata {
    name      = "sales-ingest-api-static-auth-token"
    namespace = each.key
  }

  data = {
    key = data.google_secret_manager_secret_version.sales_ingest_api_static_auth_token.secret_data
  }
}

data "google_secret_manager_secret_version" "sales_rfm_api_static_auth_token" {
  secret  = "sales_rfm_api_static_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "dbuser_replication_lag_user" {
  secret  = "dbuser_replication_lag_user"
  project = local.project
}

resource "kubernetes_secret" "dbuser_replication_lag_user" {
  for_each = toset(["enterprise-pg-bouncer", "enterprise-pgbouncer-rfm07", "enterprise-pgbouncer-rfm04", "enterprise-pgbouncer-alloydb"])
  provider = kubernetes.services-dev

  metadata {
    name      = "dbuser-replication-lag-user"
    namespace = each.key
  }

  data = {
    key = data.google_secret_manager_secret_version.dbuser_replication_lag_user.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_rdo_dataservice_sentry_integration_dsn" {
  secret  = "analytics-rdo-dataservice-sentry-integration-dsn"
  project = local.project
}

resource "kubernetes_secret" "analytics_rdo_dataservice_sentry_integration_dsn" {
  for_each = toset(["analytics-rdo-dataservice"])
  provider = kubernetes.services-dev

  metadata {
    name      = "analytics-rdo-dataservice-sentry-integration-dsn"
    namespace = each.key
  }

  data = {
    key = data.google_secret_manager_secret_version.analytics_rdo_dataservice_sentry_integration_dsn.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_rdo_ras_metrics" {
  secret  = "rdo-ras-metrics"
  project = local.project
}

resource "kubernetes_secret" "analytics_rdo_ras_metrics" {
  for_each = toset(["analytics-rdo-dataservice", "analytics-public-py-api", "analytics-metrics", "rdo-user-service"])
  provider = kubernetes.services-dev

  metadata {
    name      = "rdo-ras-metrics"
    namespace = each.key
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_ras_metrics.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_ras_metrics.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "analytics_rdo_dataservice_auth_token" {
  secret  = "analytics_rdo_dataservice_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_dataservice_secret_key" {
  secret  = "analytics_rdo_dataservice_secret_key"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_public_api_auth_token" {
  secret  = "analytics_public_api_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "appraisals_auctions_services_secrets" {
  secret  = "appraisals_auctions_services_secrets"
  project = local.project
}

data "google_secret_manager_secret_version" "appraisals_auctions_api_secrets" {
  secret  = "appraisals_auctions_api_secrets"
  project = local.project
}

data "google_secret_manager_secret_version" "pgbouncer_rfmservices01_certs" {
  secret  = "pgbouncer-rfmservices01-certs"
  project = local.project
}

resource "kubernetes_secret" "pgbouncer_rfmservices01_secret" {

  provider = kubernetes.services-dev

  for_each = toset([
    "sales-fleet-manager",
  ])

  metadata {
    name      = "pg-bouncer-rfmservices01-secret"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "pgbouncer_rfmservices01_client-cert.pem" = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfmservices01_certs.secret_data), "tls.crt")
    "pgbouncer_rfmservices01_client-key.pem"  = lookup(jsondecode(data.google_secret_manager_secret_version.pgbouncer_rfmservices01_certs.secret_data), "tls.key")

  }
}

data "google_service_account" "fleet_manager_integrations" {
  account_id = local.fleet_manager_integrations_sa_id
}

resource "google_service_account_key" "fmi_signing_key" {
  service_account_id = data.google_service_account.fleet_manager_integrations.account_id
}

resource "google_secret_manager_secret" "fmi_signing_key_secret" {
  secret_id = "fleet-manager-integrations-signing-key-secret"
  replication {
    auto {}
  }
  project = local.project
}

resource "google_secret_manager_secret_version" "fmi_signing_key_secret" {
  secret      = google_secret_manager_secret.fmi_signing_key_secret.id
  secret_data = base64decode(google_service_account_key.fmi_signing_key.private_key)
}

data "google_secret_manager_secret_version" "sales_portal_secrets_develop" {
  secret  = "sales_portal_secrets_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_portal_secrets_stage" {
  secret  = "sales_portal_secrets_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_catalog_global_secret" {
  secret  = "sales_catalog_dev_global"
  project = local.project
}

data "google_secret_manager_secret_version" "confluentd_credentials" {
  secret  = "confluentd_api_credentials"
  project = local.project
}

resource "kubernetes_secret" "confluentd_credentials" {
  for_each = toset([
    "confluentd-monitoring",
  ])

  provider = kubernetes.services-dev
  metadata {
    name      = "confluentd-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    user     = lookup(jsondecode(data.google_secret_manager_secret_version.confluentd_credentials.secret_data), "user")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.confluentd_credentials.secret_data), "password")

  }
}

data "google_secret_manager_secret_version" "sales_proposal_secrets_develop" {
  secret  = "sales_proposal_secrets_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_public_py_api_auth0_config" {
  secret  = "analytics_public_py_api_auth0_config"
  project = local.project
}

resource "kubernetes_secret" "analytics_public_py_api_secrets" {
  provider = kubernetes.services-dev
  metadata {
    name      = "analytics-public-py-api-secrets"
    namespace = "analytics-public-py-api"
  }

  data = {
    auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_public_py_api_auth0_config.secret_data), "client_id")
    auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_public_py_api_auth0_config.secret_data), "client_secret")

  }
}

data "google_secret_manager_secret_version" "rdo_user_service_sentry_dsn" {
  secret  = "rdo-user-service-sentry-dsn"
  project = local.project
}

resource "kubernetes_secret" "rdo_user_service_sentry_secret" {
  provider = kubernetes.services-dev

  for_each = toset([
    "rdo-user-service"
  ])

  metadata {
    name      = "rdo-user-service-sentry-dsn"
    namespace = each.key
  }

  data = {
    sentry_dsn = data.google_secret_manager_secret_version.rdo_user_service_sentry_dsn.secret_data
  }
}

data "google_secret_manager_secret_version" "rdo_user_service_sa_key" {
  secret  = "rdo-user-service-sa-key"
  project = local.project
}

resource "kubernetes_secret" "rdo_user_service_sa_secret" {
  provider = kubernetes.services-dev
  for_each = toset([
    "rdo-user-service"
  ])
  metadata {
    name      = "rdo-user-service-sa-key"
    namespace = each.key
  }
  data = {
    key = data.google_secret_manager_secret_version.rdo_user_service_sa_key.secret_data
  }
}

data "google_secret_manager_secret_version" "appraisals_ras_sas_db" {
  secret  = "appraisals_ras_sas_db"
  project = local.project
}

resource "kubernetes_secret" "appraisals-ras-sas-db" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-portal",
  ])
  metadata {
    name      = "appraisals-ras-sas-db"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_ras_sas_db.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_ras_sas_db.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "appraisals-portal-secret-key" {
  secret  = "appraisals-portal-secret-key"
  project = local.project
}

data "google_secret_manager_secret_version" "appraisals-portal-secret-stage-key" {
  secret  = "appraisals-portal-secret-stage-key"
  project = local.project
}

resource "kubernetes_secret" "appraisals-portal-secret-key" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-portal",
  ])
  metadata {
    name      = "appraisals-portal-secret-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = data.google_secret_manager_secret_version.appraisals-portal-secret-key.secret_data
  }
}

resource "kubernetes_secret" "appraisals-portal-secret-stage-key" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-portal",
  ])
  metadata {
    name      = "appraisals-portal-secret-stage-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = data.google_secret_manager_secret_version.appraisals-portal-secret-stage-key.secret_data
  }
}

data "google_secret_manager_secret_version" "dcs-api-gfs04-dev-user" {
  secret  = "dcs-api-gfs04-dev-user"
  project = local.project
}

resource "kubernetes_secret" "dcs-api-gfs04-dev-user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-dcs-api",
  ])
  metadata {
    name      = "dcs-gfs04-dev-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-api-gfs04-dev-user.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-api-gfs04-dev-user.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "dcs-eft-api-credentials" {
  secret  = "dcs-eft-api-credentials"
  project = local.project
}

resource "kubernetes_secret" "dcs-eft-api-credentials" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-dcs-api",
  ])
  metadata {
    name      = "dcs-eft-api-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    eft_api_user = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-eft-api-credentials.secret_data), "eft_api_user")
    eft_api_password = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-eft-api-credentials.secret_data), "eft_api_password")
  }
}

data "google_secret_manager_secret_version" "dcs-slack-api-token" {
  secret  = "dcs-slack-api-token"
  project = local.project
}

resource "kubernetes_secret" "dcs-slack-api-token" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-dcs-api",
  ])
  metadata {
    name      = "dcs-slack-api-token"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    app_id = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-slack-api-token.secret_data), "app_id")
    secret = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-slack-api-token.secret_data), "secret")
    verification_token = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-slack-api-token.secret_data), "verification_token")
    oauth_token = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-slack-api-token.secret_data), "oauth_token")
    api_token = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-slack-api-token.secret_data), "api_token")
  }
}

data "google_secret_manager_secret_version" "dcs_postgres_dcs_api_user_dev" {
  secret  = "dcs_postgres_dcs_api_user_dev"
  project = local.project
}

resource "kubernetes_secret" "appraisals-dcs-api-db-user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-dcs-api",
  ])
  metadata {
    name      = "dcs-postgres-dcs-api-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dcs_postgres_dcs_api_user_dev.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dcs_postgres_dcs_api_user_dev.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "rdo_user_service_auth0_config" {
  secret  = "rdo_user_service_auth0_config"
  project = local.project
}

resource "kubernetes_secret" "rdo_user_service_auth0_config" {
  provider = kubernetes.services-dev
  for_each = toset([
    "rdo-user-service"
  ])
  metadata {
    name      = "rdo-user-service-auth0-config"
    namespace = each.key
  }
  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.rdo_user_service_auth0_config.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.rdo_user_service_auth0_config.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "enterprise_domain_service_auth_token_secret" {
  secret  = "enterprise_domain_service_auth_token" # pragma: allowlist secret
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_secret_key_secret" {
  secret  = "enterprise_domain_service_secret_key"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_auth0_config" {
  secret  = "enterprise_domain_service_auth0_config"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_domain_service_auth0_webshop_client_id" {
  secret  = "enterprise_domain_service_auth0_webshop_client_id"
  project = local.project
}

resource "kubernetes_secret" "enterprise_domain_service_api_secrets" {
  provider = kubernetes.services-dev

  for_each = toset([
    "enterprise-domain-service"
  ])

  metadata {
    name      = "enterprise-domain-service-secrets"
    namespace = each.key
  }

  data = {
    secret_key = data.google_secret_manager_secret_version.enterprise_domain_service_secret_key_secret.secret_data
    sentry_dsn = data.sentry_key.default["enterprise_domain_service"].dsn_public
  }
}

data "google_secret_manager_secret_version" "pgbouncer_alloydb_rfm_userlist" {
  secret  = "pgbouncer_alloydb_rfm_userlist"
  project = local.project
}

resource "kubernetes_secret" "pgbouncer_alloydb_rfm_userlist2" {
  provider = kubernetes.services-dev
  for_each = toset([
    "platform-alloydb",
    "enterprise-pgbouncer-alloydb"
  ])
  metadata {
    name      = "pgbouncer-alloydb-rfm-userlist2"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "userlist.txt" : data.google_secret_manager_secret_version.pgbouncer_alloydb_rfm_userlist.secret_data
  }
}

data "google_secret_manager_secret_version" "auth0-pvac-dev" {
  secret  = "auth0-pvac-dev"
  project = local.project
}

data "google_secret_manager_secret_version" "platform01-pvac-dev" {
  secret  = "dbuser_pvac_user"
  project = local.project
}


data "google_secret_manager_secret_version" "svc_pvac_secret" {
  secret  = "svc_pvac_secret"
  project = local.project
}

resource "kubernetes_secret" "pvac-secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "platform-valuation",
  ])
  metadata {
    name      = "pvac-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    db_username       = lookup(jsondecode(data.google_secret_manager_secret_version.platform01-pvac-dev.secret_data), "username")
    db_password       = lookup(jsondecode(data.google_secret_manager_secret_version.platform01-pvac-dev.secret_data), "password")
    username          = lookup(jsondecode(data.google_secret_manager_secret_version.auth0-pvac-dev.secret_data), "username")
    client_id         = lookup(jsondecode(data.google_secret_manager_secret_version.auth0-pvac-dev.secret_data), "client_id")
    client_secret     = lookup(jsondecode(data.google_secret_manager_secret_version.auth0-pvac-dev.secret_data), "client_secret")
    sentry_dsn        = data.sentry_key.default["platform_valuation_admin_console"].dsn_public
    svc_pvac_username = lookup(jsondecode(data.google_secret_manager_secret_version.svc_pvac_secret.secret_data), "username")
    svc_pvac_password = lookup(jsondecode(data.google_secret_manager_secret_version.svc_pvac_secret.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "appraisals_portal_slack_api_token" {
  secret  = "appraisals-portal-slack-api-token"
  project = local.project
}

resource "kubernetes_secret" "appraisals_portal_slack_api_token_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-portal",
  ])
  metadata {
    name      = "appraisals-portal-slack-api-token"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "api_token" : data.google_secret_manager_secret_version.appraisals_portal_slack_api_token.secret_data
  }
}

data "google_secret_manager_secret_version" "appraisals_kafka_internal_secret_key" {
  secret  = "appraisals_kafka_internal_secret_key"
  project = local.project
}

resource "kubernetes_secret" "appraisals-portal-kafka-secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-portal",
  ])
  metadata {
    name      = "appraisals-portal-kafka-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key    = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_kafka_internal_secret_key.secret_data), "key")
    secret = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_kafka_internal_secret_key.secret_data), "secret")
  }
}

resource "kubernetes_secret" "appraisals-dcs-kafka-secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "appraisals-dcs-api",
  ])
  metadata {
    name      = "appraisals-dcs-kafka-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key    = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_kafka_internal_secret_key.secret_data), "key")
    secret = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals_kafka_internal_secret_key.secret_data), "secret")
  }
}


data "google_secret_manager_secret_version" "alloy-shard-map" {
  secret  = "alloy_shard_map"
  project = local.project
}

# Grant CI access to WRITE to the shard map so we can control shard map contents through GitHub
resource "google_secret_manager_secret_iam_member" "grant_shard_map_management_ci" {
  for_each = toset([
    data.google_secret_manager_secret_version.alloy-shard-map.secret,
    data.google_secret_manager_secret_version.ims-shard-map-gcp.secret,
  ])
  project   = local.project
  secret_id = each.key
  member    = "serviceAccount:${data.terraform_remote_state.project_structure.outputs.platform_services_ci}"
  role = "roles/secretmanager.secretVersionManager"
}

data "google_secret_manager_secret_version" "pypi-password" {
  secret  = "pypi-password"
  project = local.project
}

data "google_secret_manager_secret_version" "pgbouncer_rdo101_userlist" {
  secret  = "pgbouncer_rdo101_userlist"
  project = local.project
}

resource "kubernetes_secret" "pgbouncer_rdo101_userlist" {
  provider = kubernetes.services-dev
  for_each = toset([
    "enterprise-pgbouncer-rdo101"
  ])
  metadata {
    name      = "pgbouncer-rdo101-userlist"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "userlist.txt" : data.google_secret_manager_secret_version.pgbouncer_rdo101_userlist.secret_data
  }
}

data "google_secret_manager_secret_version" "user-service-alerts-slack-webhook" {
  secret  = "user-service-alerts-slack-webhook"
  project = local.project
}

resource "kubernetes_secret" "user-service-alerts-slack-webhook" {
  provider = kubernetes.services-dev
  for_each = toset([
    "platform-user-config",
    "rdo-user-service"
  ])
  metadata {
    name      = "user-service-alerts-slack-webhook"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "webhook" : data.google_secret_manager_secret_version.user-service-alerts-slack-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_platform_cms_user" {
  secret  = "dbuser_platform_cms_user"
  project = local.project
}

resource "kubernetes_secret" "dbuser-platform-cms-user" {
  provider = kubernetes.services-dev
  for_each = toset([
    "valuation-classification"
  ])
  metadata {
    name      = "dbuser-platform-cms-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "user" : lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_platform_cms_user.secret_data), "user")
    "password" : lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_platform_cms_user.secret_data), "password")

  }
}

data "google_secret_manager_secret_version" "rdo-alloy-admin" {
  secret  = "rdo-alloy-admin"
  project = local.project
}

resource "kubernetes_secret" "rdo-alloy-admin" {
  provider = kubernetes.services-dev
  for_each = toset([
    "analytics-public-py-api",
    "analytics-rdo-dataservice",
    "analytics-metrics",
    "rdo-user-service",
  ])
  metadata {
    name      = "rdo-alloy-admin"
    namespace = each.key
  }

  data = {
    "user" : lookup(jsondecode(data.google_secret_manager_secret_version.rdo-alloy-admin.secret_data), "username")
    "password" : lookup(jsondecode(data.google_secret_manager_secret_version.rdo-alloy-admin.secret_data), "password")

  }
}


data "google_secret_manager_secret_version" "github_npm_token" {
  secret  = "github_npm_token"
  project = local.project
}

data "google_secret_manager_secret_version" "etcinsi_test_user_password" {
  secret  = "etcinsi_test_user_password"
  project = local.project
}

resource "kubernetes_secret" "sales_portal_testing_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-portal"
  ])
  metadata {
    name      = "sales-portal-testing-secrets"
    namespace = each.key
  }

  data = {
    npm_token = data.google_secret_manager_secret_version.github_npm_token.secret_data
    test_user_password = data.google_secret_manager_secret_version.etcinsi_test_user_password.secret_data

  }
}

data "google_secret_manager_secret_version" "taxonomy_secret_key" {
  secret  = "taxonomy_secret_key"
  project = local.project
}

resource "kubernetes_secret" "taxonomy_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "valuation-classification",
  ])
  metadata {
    name      = "taxonomy-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    secret_key          = data.google_secret_manager_secret_version.taxonomy_secret_key.secret_data
    sentry_dsn          = data.sentry_key.default["platform_valuation_taxonomy_service"].dsn_public
  }
}

data "google_secret_manager_secret_version" "platform_sales_classification_airflow_api_token" {
  secret  = "platform_sales_classification_airflow_api_token"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_rdo_scheduler_auth0_config_dev" {
  secret  = "analytics_rdo_scheduler_auth0_config_dev"
  project = local.project
}

resource "kubernetes_secret" "auth0-credentials-dev" {
  provider = kubernetes.services-dev
  metadata {
    name      = "auth0-credentials-dev"
    namespace = "analytics-metrics"
  }

  data = {
    auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_scheduler_auth0_config_dev.secret_data), "client_id")
    auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_scheduler_auth0_config_dev.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "dbuser_platform_valuation_taxonomy_user_gcp_secret" {
  secret  = "dbuser_platform_valuation_taxonomy_user"
  project = local.project
}

resource "kubernetes_secret" "dbuser_platform_valuation_taxonomy_user_secrets" {
  provider = kubernetes.services-dev
  for_each = toset([
    "valuation-classification",
  ])
  metadata {
    name      = "platform-valuation-taxonomy-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "user" : lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_platform_valuation_taxonomy_user_gcp_secret.secret_data), "username")
    "password" : lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_platform_valuation_taxonomy_user_gcp_secret.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "dev-rdo-ras-metrics" {
  secret  = "dev-rdo-ras-metrics"
  project = local.project
}

resource "kubernetes_secret" "dev-rdo-ras-metrics" {
  provider = kubernetes.services-dev
  metadata {
    name      = "dev-rdo-ras-metrics"
    namespace = "analytics-metrics"
  }

  data = {
    username                            = lookup(jsondecode(data.google_secret_manager_secret_version.dev-rdo-ras-metrics.secret_data), "username")
    password                            = lookup(jsondecode(data.google_secret_manager_secret_version.dev-rdo-ras-metrics.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "legacy_api_v2_secrets" {
  secret  = "legacy_api_v2_secrets"
  project = local.project
}

data "google_secret_manager_secret_version" "fmi_alerts_slack_webhook" {
  secret  = "fmi-alerts-slack-webhook"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_notification_service_gcp_auth0_credentials" {
  secret  = "sales_notification_service_auth0_credentials"
  project = local.project
}

resource "kubernetes_secret" "sales_notification_service_auth0_credentials" {
  provider = kubernetes.services-dev
  for_each = toset([
    "sales-notification-service",
  ])
  metadata {
    name      = "sales-notification-service-auth0-credentials"
    namespace = each.key
  }
  data = {
    "client_id" : jsondecode(data.google_secret_manager_secret_version.sales_notification_service_gcp_auth0_credentials.secret_data)["client_id"]
    "client_secret" : jsondecode(data.google_secret_manager_secret_version.sales_notification_service_gcp_auth0_credentials.secret_data)["client_secret"]
  }
}

data "google_secret_manager_secret_version" "sales_notification_service_api_token" {
  secret = "sales_notification_service_api_token"
  project = local.project
}

data "google_secret_manager_secret_version" "fleet_manager_core_pagerduty_secret" {
  secret = "fleet_manager_core_pagerduty_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "pgbouncer_rfm1001_userlist" {
  secret  = "pgbouncer_rfm1001_userlist"
  project = local.project
}

resource "kubernetes_secret" "pgbouncer_rfm1001_userlist" {
  provider = kubernetes.services-dev
  for_each = toset([
    "enterprise-pgbouncer-rfm1001"
  ])
  metadata {
    name      = "pgbouncer-rfm1001-userlist"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "userlist.txt" : data.google_secret_manager_secret_version.pgbouncer_rfm1001_userlist.secret_data
  }

  lifecycle {
    ignore_changes = [
      data,
    ]
  }
}

data "google_secret_manager_secret_version" "enterprise_authentication_secrets_develop" {
  secret  = "enterprise_authentication_secrets_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_authentication_secrets_stage" {
  secret  = "enterprise_authentication_secrets_stage"
  project = local.project
}

data "google_secret_manager_secret_version" "fleet_manager_historian_secret_key" {
  secret = "fleet_manager_historian_secret_key"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_usermanagement_api_secrets_develop" {
  secret  = "enterprise_usermanagement_api_secrets_develop"
  project = local.project
}

data "google_secret_manager_secret_version" "enterprise_usermanagement_api_secrets_stage" {
  secret  = "enterprise_usermanagement_api_secrets_stage"
  project = local.project
}