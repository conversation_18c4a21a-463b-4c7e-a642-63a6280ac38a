module "services_gke_cluster" {
  source                        = "../../modules/compute/gke-cluster/v1"
  min_master_version            = "1.14"
  environment                   = var.environment
  name                          = "services"
  initial_node_count            = 0
  initial_default_node_count    = 0
  machine_type                  = "n1-highmem-16"
  default_pool_machine_type     = "n1-standard-4"
  division                      = "platform"
  role                          = "application"
  folder_name                   = local.folder_name
  project                       = local.project
  managed_prometheus_config     = true
  min_node_count                = 0
  max_node_count                = 20
  network_link                  = module.services_kubernetes_network.network_link
  subnet_link                   = module.services_kubernetes_network.subnet_link
  disk_size_gb                  = 75
  enable_system_containers_pool = true
  enable_spot_pool              = false
  enable_primary_pool           = false
  cluster_autoscaling           = false
  vertical_pod_autoscaling      = true
  workload_identity_enabled     = true
  release_channel               = "UNSPECIFIED"
  notification_config           = true
  notification_pubsub           = module.gke_notifications.pubsub_id
  default_node_pool_min_count   = 0
  default_node_pool_autoscaling = false
  enable_dns_cache              = true
  enable_l4_ilb_subsetting      = true # Once its enabled, it cannot be disabled: https://cloud.google.com/kubernetes-engine/docs/how-to/internal-load-balancing#requirements_and_limitations
  oauth_scopes = [
    "https://www.googleapis.com/auth/logging.write",
    "https://www.googleapis.com/auth/monitoring",
    "https://www.googleapis.com/auth/cloud-platform",
    "https://www.googleapis.com/auth/devstorage.read_write",
    "https://www.googleapis.com/auth/cloud_debugger",
  ]
  taints = [
    {
      key    = "application"
      value  = "web"
      effect = "NO_SCHEDULE"
    }
  ]
  providers = {
    google = google-beta
  }
}


resource "google_container_node_pool" "workload_identity_on_demand" {
  name               = "workload-identity-on-demand"
  location           = module.services_gke_cluster.gke_cluster.location
  cluster            = module.services_gke_cluster.gke_cluster.name
  initial_node_count = 0
  project            = local.project

  management {
    auto_repair  = true
    auto_upgrade = true
  }
  node_config {
    machine_type = "n1-highmem-32"
    disk_size_gb = 75
    disk_type    = "pd-balanced"
    metadata = {
      disable-legacy-endpoints = true
    }
    labels = {
      machine-type     = "n1-highmem-32"
      machine-platform = "general-purpose"
      machine-pricing  = "on-demand"
      auth-type        = "workload-identity"
    }
    service_account = module.services_gke_cluster.service_account_email
    oauth_scopes = [
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/devstorage.read_write",
    ]
    taint {
      key    = "application"
      value  = "web"
      effect = "NO_SCHEDULE"
    }
    taint {
      key    = "workload-identity"
      value  = "enabled"
      effect = "NO_SCHEDULE"
    }

  }
  timeouts {
    update = "60m"
  }
  provider = google-beta
  lifecycle {
    prevent_destroy = true
  }
}

resource "google_container_node_pool" "gpu_on_demand" {
  name               = "gpu-on-demand"
  location           = module.services_gke_cluster.gke_cluster.location
  cluster            = module.services_gke_cluster.gke_cluster.name
  initial_node_count = 1
  project            = local.project

  management {
    auto_repair  = true
    auto_upgrade = true
  }
  node_config {
    spot         = true
    machine_type = "n1-highmem-32"
    disk_size_gb = 50
    disk_type    = "pd-balanced"
    ephemeral_storage_config {
      local_ssd_count = 2
    }
    guest_accelerator {
      type  = "nvidia-tesla-t4"
      count = 4
    }
    metadata = {
      disable-legacy-endpoints = true
    }
    labels = {
      machine-type     = "n1-highmem-32"
      machine-platform = "gpu"
      machine-pricing  = "on-demand"
      auth-type        = "workload-identity"
    }
    service_account = module.services_gke_cluster.service_account_email
    oauth_scopes = [
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/devstorage.read_write",
    ]
    taint {
      key    = "application"
      value  = "web"
      effect = "NO_SCHEDULE"
    }
    taint {
      key    = "workload-identity"
      value  = "enabled"
      effect = "NO_SCHEDULE"
    }
    taint {
      effect = "NO_SCHEDULE"
      key    = "nvidia.com/gpu"
      value  = "present"
    }
  }
  timeouts {
    update = "60m"
  }
  provider = google-beta

  lifecycle {
    prevent_destroy = true
  }
}

resource "google_container_node_pool" "platform_valuation" {
  name               = "platform-valuation"
  location           = module.services_gke_cluster.gke_cluster.location
  cluster            = module.services_gke_cluster.gke_cluster.name
  initial_node_count = 0
  project            = local.project

  management {
    auto_repair  = true
    auto_upgrade = true
  }
  network_config {
    pod_range = "workload-identity-pods"
  }
  node_config {
    machine_type = "n1-standard-64"
    disk_size_gb = 20
    disk_type    = "pd-balanced"
    ephemeral_storage_config {
      local_ssd_count = 6
    }
    metadata = {
      disable-legacy-endpoints = true
    }
    labels = {
      machine-type     = "n1-standard-64"
      machine-platform = "general-purpose"
      machine-pricing  = "on-demand"
      auth-type        = "workload-identity"
      purpose          = "platform-valuation"
      division         = "rbval"
    }
    service_account = module.services_gke_cluster.service_account_email
    oauth_scopes = [
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/devstorage.read_write",
    ]
    taint {
      key    = "application"
      value  = "web"
      effect = "NO_SCHEDULE"
    }
    taint {
      key    = "workload-identity"
      value  = "enabled"
      effect = "NO_SCHEDULE"
    }
    taint {
      effect = "NO_SCHEDULE"
      key    = "platform-valuation"
      value  = "reserved"
    }
  }
  timeouts {
    update = "60m"
  }
  provider = google-beta

  lifecycle {
  }
}

resource "google_container_node_pool" "spot_workload_identity_on_demand" {
  name               = "spot-workload-identity-on-demand"
  location           = module.services_gke_cluster.gke_cluster.location
  cluster            = module.services_gke_cluster.gke_cluster.name
  initial_node_count = 0
  project            = local.project

  management {
    auto_repair  = true
    auto_upgrade = false
  }
  node_config {
    spot         = true
    machine_type = "n1-highmem-32"
    disk_size_gb = 75
    disk_type    = "pd-balanced"
    metadata = {
      disable-legacy-endpoints = true
    }
    labels = {
      machine-type     = "spot"
      machine-platform = "general-purpose"
      machine-pricing  = "on-demand"
      auth-type        = "workload-identity"
    }
    service_account = module.services_gke_cluster.service_account_email
    oauth_scopes = [
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/devstorage.read_write",
    ]
    taint {
      key    = "application"
      value  = "web"
      effect = "NO_SCHEDULE"
    }
    taint {
      key    = "workload-identity"
      value  = "enabled"
      effect = "NO_SCHEDULE"
    }

  }
  network_config {
    pod_range = "workload-identity-pods"
  }
  timeouts {
    update = "60m"
  }
  provider = google-beta

  lifecycle {
    prevent_destroy = true
  }
  depends_on = [
    module.services_kubernetes_network
  ]
}

resource "google_container_node_pool" "platform_valuation_spot" {
  name               = "platform-valuation-spot"
  location           = module.services_gke_cluster.gke_cluster.location
  cluster            = module.services_gke_cluster.gke_cluster.name
  initial_node_count = 0
  project            = local.project

  management {
    auto_repair  = true
    auto_upgrade = true
  }
  network_config {
    pod_range = "workload-identity-pods"
  }
  node_config {
    spot         = true
    machine_type = "n1-standard-64"
    disk_size_gb = 20
    disk_type    = "pd-balanced"
    ephemeral_storage_config {
      local_ssd_count = 6
    }
    metadata = {
      disable-legacy-endpoints = true
    }
    labels = {
      machine-type     = "n1-standard-64"
      machine-platform = "general-purpose"
      machine-pricing  = "spot"
      auth-type        = "workload-identity"
      purpose          = "platform-valuation"
      division         = "rbval"
    }
    service_account = module.services_gke_cluster.service_account_email
    oauth_scopes = [
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/devstorage.read_write",
    ]
    taint {
      key    = "application"
      value  = "web"
      effect = "NO_SCHEDULE"
    }
    taint {
      key    = "workload-identity"
      value  = "enabled"
      effect = "NO_SCHEDULE"
    }
    taint {
      effect = "NO_SCHEDULE"
      key    = "platform-valuation"
      value  = "reserved"
    }

  }
  timeouts {
    update = "60m"
  }
  provider = google-beta
  lifecycle {
    prevent_destroy = true
  }
}

output "services_cluster_location" {
  value = module.services_gke_cluster.gke_cluster.location
}

output "services_cluster_location_eu" {
  value = module.europe_west_2_services_gke_cluster.gke_cluster.location
}

output "services_cluster_name" {
  value = module.services_gke_cluster.gke_cluster.name
}

output "services_cluster_name_eu" {
  value = module.europe_west_2_services_gke_cluster.gke_cluster.name
}

output "services_cluster_account_email" {
  value = module.services_gke_cluster.service_account_email
}

output "services_cluster_account_id" {
  value = module.services_gke_cluster.service_account_id
}

resource "google_project_iam_member" "cluster" {
  for_each = toset([
    "roles/cloudkms.cryptoKeyEncrypterDecrypter",
    "roles/cloudtrace.agent",
    "roles/errorreporting.writer",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectAdmin",
    "roles/viewer",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_project_iam_member" "cluster_artifactory_images_permissions" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.images_project
  role    = each.key
  member  = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_project_iam_member" "cluster_artifactory_images_permissions_eu" {
  for_each = toset([
    "roles/artifactregistry.admin"
  ])
  project = local.images_project
  role    = each.key
  member  = "serviceAccount:${module.europe_west_2_services_gke_cluster.service_account_email}"
}

resource "google_project_iam_member" "cluster_artifactory_management_prod_permissions" {
  for_each = toset([
    "roles/artifactregistry.writer"
  ])
  project = local.management_prod
  role    = each.key
  member  = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}


locals {
  images_project         = data.terraform_remote_state.project_structure.outputs.projects["images"]
  rfm_project            = data.terraform_remote_state.project_structure.outputs.projects["rfm-${var.environment}"]
  email_services_project = data.terraform_remote_state.project_structure.outputs.projects["email-service-${var.environment}"]
  appraisals_data        = data.terraform_remote_state.project_structure.outputs.projects["appraisals-data-${var.environment}"]
  rdo                    = data.terraform_remote_state.project_structure.outputs.projects["rdo-${var.environment}"]
  image_service          = data.terraform_remote_state.project_structure.outputs.projects["image-service-${var.environment}"]
  image_service_prod     = data.terraform_remote_state.project_structure.outputs.projects["image-service-prod"]
  dtt                    = data.terraform_remote_state.project_structure.outputs.projects["dtt-${var.environment}"]
  appraisals_portal      = data.terraform_remote_state.project_structure.outputs.projects["appraisals-portal-${var.environment}"]
  management             = data.terraform_remote_state.project_structure.outputs.projects["management-${var.environment}"]
  management_prod        = data.terraform_remote_state.project_structure.outputs.projects["management-prod"]
  qa_project_prod        = data.terraform_remote_state.project_structure.outputs.projects["qa-tools-prod"]
  valuations_ai_dev      = data.terraform_remote_state.project_structure.outputs.projects["valuations-ai-dev"]
}

resource "google_project_iam_member" "images" {
  for_each = toset([
    "roles/storage.objectViewer",
  ])
  project = local.images_project
  role    = each.key
  member  = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_project_iam_member" "rfm" {
  for_each = toset([
    "roles/datastore.user",
  ])
  project = local.rfm_project
  role    = each.key
  member  = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_service_account_iam_member" "rfm_firebase_sdk" {
  service_account_id = data.terraform_remote_state.rfm.outputs.firebase_admin_sdk_account
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_storage_bucket_iam_member" "rfm_transactions" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/storage.legacyBucketReader",
  ])
  bucket = data.terraform_remote_state.rfm.outputs.sales_txns_bucket_name
  role   = each.key
  member = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_storage_bucket_iam_member" "sales_assets" {
  bucket = data.terraform_remote_state.rfm.outputs.sales_assets_bucket_name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_storage_bucket_iam_member" "collateral_output" {
  bucket = data.terraform_remote_state.rfm.outputs.collateral_output_bucket_name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_project_iam_member" "email_service" {
  for_each = toset([
    "roles/datastore.user",
  ])
  project = local.email_services_project
  role    = each.key
  member  = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_project_iam_member" "management" {
  for_each = toset([
    "roles/storage.objectAdmin",
  ])
  project = local.management
  role    = each.key
  member  = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

resource "google_project_iam_member" "management_prod" {
  for_each = toset([
    "roles/storage.objectAdmin",
  ])
  project = local.management_prod
  role    = each.key
  member  = "serviceAccount:${module.services_gke_cluster.service_account_email}"
}

module "europe_west_2_services_gke_cluster" {
  source                        = "../../modules/compute/gke-cluster-autopilot/v1"
  min_master_version            = "1.22"
  environment                   = var.environment
  name                          = "europe-west-2-services"
  initial_node_count            = 0
  location                      = "europe-west2"
  division                      = "services"
  deletion_protection           = false
  whitelisted_cidrs             = ["0.0.0.0/0"]
  role                          = "application"
  folder_name                   = local.folder_name
  project                       = local.project
  network_link                  = module.services_kubernetes_network.network_link
  subnet_link                   = google_compute_subnetwork.europe_services_kubernetes_subnet.self_link
  vertical_pod_autoscaling      = true
  autopilot                     = true
  oauth_scopes = [
    "https://www.googleapis.com/auth/logging.write",
    "https://www.googleapis.com/auth/monitoring",
    "https://www.googleapis.com/auth/cloud-platform",
    "https://www.googleapis.com/auth/devstorage.read_write",
    "https://www.googleapis.com/auth/drive",
  ]
}
