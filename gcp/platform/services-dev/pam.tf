resource "google_project_service" "privilegedaccessmanager" {
  project            = local.project
  service            = "privilegedaccessmanager.googleapis.com"
  disable_on_destroy = false
}

resource "google_privileged_access_manager_entitlement" "tfentitlement" {
    entitlement_id = "poc-entitlement"
    location = "global"
    max_request_duration = "86400s" # 1 Day
    parent = "projects/${local.project}"
    requester_justification_config {    
        unstructured{}
    }
    eligible_users {
        principals = [
          "<EMAIL>"
        ]
    }
    privileged_access{
        gcp_iam_access{
            role_bindings{
                role = "roles/storage.objectAdmin"
            }
            resource = "//cloudresourcemanager.googleapis.com/projects/${local.project}"
            resource_type = "cloudresourcemanager.googleapis.com/Project"
        }
    }
    additional_notification_targets {
      admin_email_recipients     = [
        "<EMAIL>",
      ]
     
    }
    approval_workflow {
    manual_approvals {
      require_approver_justification = true
      steps {
        approvals_needed          = 1
        approver_email_recipients = [
          "<EMAIL>"
        ]
        approvers {
          principals = [
            "user:<EMAIL>"
          ]
        }
      }
    }
  }
}