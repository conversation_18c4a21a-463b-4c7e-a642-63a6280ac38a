# Permissions for sales-rfm-channels-api defined below

locals {
  sales_rfm_channels_api_account_email = google_service_account.workload_identity["sales-rfm-channels-api"].email
}

resource "google_project_iam_member" "sales_rfm_channels_api_permissions" {
  for_each = toset([
    "roles/clouddebugger.agent",
    "roles/cloudprofiler.agent",
    "roles/pubsub.publisher",
    "roles/storage.objectAdmin",
    "roles/cloudtrace.agent",
    "roles/pubsub.serviceAgent",
    "roles/monitoring.metricWriter",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_auth_token_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_rfm_channels_api_auth_token_secret.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_secret_key_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_rfm_channels_api_secret_key_secret.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_fleet_manager_api_auth.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_fmi_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_fleet_manager_integrations_api_auth.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_iron_planet_api_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_iron_planet_api_auth.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_user_service_api_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.platform_user_config_api_auth.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_rfm_global_dbuser_map_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.equipment_rfm_api_dbuser_map_secret.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_confluent_key_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_confluent_kafka_key.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_confluent_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_confluent_kafka_secret.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_confluent_schema_key_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_confluent_schema_registry_key.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_confluent_schema_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_confluent_schema_registry_secret.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_rfm_api_key_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_rfm_api_static_auth_token.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}


data "google_secret_manager_secret_version" "sales_rfm_channels_api_service_account_key" {
  secret  = "sales-rfm-channels-api-service-account-key"
  project = local.project
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_channels_api_service_account_key_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_rfm_channels_api_service_account_key.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rca_pagerduty_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_rca_pagerduty_secret.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_storage_bucket_iam_member" "channles_api_sales_txns_bucket_permission" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/storage.legacyBucketReader",
  ])
  bucket = data.terraform_remote_state.rfm.outputs.sales_txns_bucket_name
  role   = each.key
  member = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}

resource "google_secret_manager_secret_iam_member" "channels_api_fmi_alert_webhook_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.fmi_alerts_slack_webhook.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_rfm_channels_api_account_email}"
}
