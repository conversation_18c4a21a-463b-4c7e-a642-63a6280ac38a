locals {
  sales_notification_service_account_email = google_service_account.workload_identity["sales-notification-service"].email
}

resource "google_project_iam_member" "sales_notification_service_permissions" {
  for_each = toset([
    "roles/clouddebugger.agent",
    "roles/cloudprofiler.agent",
    "roles/storage.objectAdmin",
    "roles/cloudtrace.agent",
    "roles/monitoring.metricWriter",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${local.sales_notification_service_account_email}"
}

resource "google_bigquery_dataset_iam_member" "sales_notification_service_bq_emails_triggered_permission" {
    dataset_id = google_bigquery_dataset.bigquery_for_services_dashboards_dev.dataset_id
    for_each   = toset([
        "roles/bigquery.dataEditor",
    ])
    role   = each.key
    member = "serviceAccount:${local.sales_notification_service_account_email}"
}

# Secrets
resource "google_secret_manager_secret_iam_member" "sales_notification_service_user_config_auth_token_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.platform_user_config_api_auth.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_notification_service_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_notification_service_pagerduty_secret_permission" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_rca_pagerduty_secret.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_notification_service_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_notification_service_api_token_permission" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_notification_service_api_token.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_notification_service_account_email}"
}

# Permissions necessary to interact with GCP Firestore in the RFM project
resource "google_project_iam_member" "sales_notification_service_rfm_firestore_permission" {
  for_each = toset([
    "roles/datastore.user",
    "roles/firebase.admin",
  ])
  project = local.rfm_project
  role    = each.key
  member  = "serviceAccount:${local.sales_notification_service_account_email}"
}

resource "google_project_iam_member" "sales_notification_service_rfm_firestore_permission_email_service" {
  for_each = toset([
    "roles/datastore.user",
    "roles/firebase.admin",
  ])
  project = data.terraform_remote_state.project_structure.outputs.projects["email-service-dev"]
  role    = each.key
  member  = "serviceAccount:${local.sales_notification_service_account_email}"
}

# Firestore indexes
# Hot List Notifications index
resource "google_firestore_index" "sales_notification_service_rfm_firestore_hot_list_notifications_index" {
  project = local.rfm_project
  database = "(default)"
  collection = "HotListNotifications"
  fields {
    field_path = "state.status"
    order      = "ASCENDING"
  }
  fields {
    field_path = "state.delivery_attempts"
    order      = "ASCENDING"
  }
    fields {
    field_path = "__name__"
    order      = "ASCENDING"
  }
}

# Pending List Notifications index
resource "google_firestore_index" "sales_notification_service_rfm_firestore_pending_list_notifications_index" {
  project = local.rfm_project
  database = "(default)"
  collection = "PendingListNotifications"
  fields {
    field_path = "state.status"
    order      = "ASCENDING"
  }
  fields {
    field_path = "state.delivery_attempts"
    order      = "ASCENDING"
  }
    fields {
    field_path = "__name__"
    order      = "ASCENDING"
  }
}

# Scheduled Email Notifications index[Email Service DEV Project]
resource "google_firestore_index" "sales_notification_service_email_service_firestore_scheduled_email_notifications_index" {
  project = data.terraform_remote_state.project_structure.outputs.projects["email-service-dev"]
  database = "(default)"
  collection = "emailmessages"
  fields {
    field_path = "client_code"
    order      = "ASCENDING"
  }
  fields {
    field_path = "identity_user_id"
    order      = "ASCENDING"
  }
  fields {
    field_path = "creation_date"
    order      = "ASCENDING"
  }
    fields {
    field_path = "__name__"
    order      = "ASCENDING"
  }
}