locals {
  valuations_performance_logger_etl_email = google_service_account.workload_identity["vals-performance-logger-etl"].email
}

resource "google_project_iam_member" "valuations_performance_logger_services" {
  for_each = toset([
    "roles/redis.editor"
  ])
  role    = each.key
  project = local.project
  member  = "serviceAccount:${local.valuations_performance_logger_etl_email}"
}

resource "google_project_iam_member" "valuations_performance_logger_appraisals_data" {
  for_each = toset([
    "roles/storage.objectAdmin",
  ])
  role    = each.key
  project = local.appraisals_data
  member  = "serviceAccount:${local.valuations_performance_logger_etl_email}"
}

