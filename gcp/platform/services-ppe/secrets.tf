provider "kubernetes" {
  alias                  = "services-ppe"
  host                   = "https://${module.services_gke_cluster.gke_cluster.endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(module.services_gke_cluster.gke_cluster.master_auth.0.cluster_ca_certificate)
}

data "google_client_config" "default" {}

resource "random_password" "cloudsql_password" {
  length           = 16
  special          = true
  override_special = "!@#$%^&*()-_=+[]{}<>?"
}

resource "google_secret_manager_secret" "cloudsql_identity" {
  secret_id     = "dbadmin_identity01_${var.environment}"
  project  = local.project
  replication { 
    auto {} 
  }
}

data "google_secret_manager_secret_version" "cloudsql_rfm" {
  secret  = "dbadmin_rfm01_prod"
  project = local.management-prod
}

resource "google_secret_manager_secret_version" "cloudsql_identity" {
  secret = google_secret_manager_secret.cloudsql_identity.id
  secret_data = jsonencode({
    password = random_password.cloudsql_password.result
  })
}

resource "random_password" "sqlserver_user_password" {
  length           = 16
  special          = true
  override_special = "!@#$%^&*()-_=+[]{}<>?"
}

resource "google_secret_manager_secret" "sqlserver_user" {
  secret_id = "sqlserver_user_password_${var.environment}"
  project   = local.project
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "sqlserver_user" {
  secret = google_secret_manager_secret.sqlserver_user.id
  secret_data = random_password.sqlserver_user_password.result
}

resource "google_sql_user" "sqlserver_user" {
  name     = "sqlserver"
  instance = module.cloudsql_identity_v2.instance_name
  project  = local.project
  password = random_password.sqlserver_user_password.result
}

data "google_secret_manager_secret_version" "ppe_platform_user_config_api_auth" {
  secret  = "platform_user_config_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_platform_user_config_sso_integration_token" {
  secret  = "platform_user_config_sso_integration_token"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_platform_user_config_enterprise_authentication_key" {
  project = local.project
  secret  = "platform_user_config_enterprise_authentication_key"
}

data "google_secret_manager_secret_version" "ppe_platform_user_config_secret_key" {
  secret  = "platform_user_config_secret_key"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_platform_user_config_ga4_credentials_key" {
  secret  = "platform_user_config_ga4_credentials_key"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_platform_user_config_default_webshop_credentials_key" {
  secret  = "platform_user_config_default_webshop_credentials_key"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_platform_user_config_auth0_credentials" {
  secret  = "platform_user_config_auth0_credentials"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_dbuser_platform_user_config" {
  secret  = "dbuser_platform_user_config"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_platform_user_config_db_password" {
  secret  = "dbuser_rfm_user_config_rfm_user_config_api"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_sales_fleet_manager_tools_auth0_client_id" {
  secret  = "sales_fleet_manager_tools_auth0_client_id"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_sales_fleet_manager_tools_auth0_client_secret" {
  secret  = "sales_fleet_manager_tools_auth0_client_secret"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_sales_portal_auth0_config" {
  secret  = "sales_portal_auth0_config"
  project = local.project
}

data "google_secret_manager_secret_version" "ppe_platform_user_config_auth_token" {
  secret  = "platform_user_config_auth_token"
  project = local.project
}

resource "kubernetes_secret" "ppe-platform-user-config-db-password" {
  provider = kubernetes.services-ppe

  for_each = toset([
    "platform-user-config",
  ])

  metadata {
    name      = "platform-user-config-db-password"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    db_password = data.google_secret_manager_secret_version.ppe_platform_user_config_db_password.secret_data
  }
}

resource "kubernetes_secret" "platform-user-config-secret-key" {
  provider = kubernetes.services-ppe

  for_each = toset([
    "platform-user-config",
  ])

  metadata {
    name      = "platform-user-config-secret-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    secret_key                       = data.google_secret_manager_secret_version.ppe_platform_user_config_secret_key.secret_data
    auth0_client_id                  = data.google_secret_manager_secret_version.ppe_sales_fleet_manager_tools_auth0_client_id.secret_data
    auth0_client_secret              = data.google_secret_manager_secret_version.ppe_sales_fleet_manager_tools_auth0_client_secret.secret_data
    sales_portal_auth0_client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.ppe_sales_portal_auth0_config.secret_data), "client_id")
    sales_portal_auth0_client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.ppe_sales_portal_auth0_config.secret_data), "client_secret")
    rca_auth0_client_id              = lookup(jsondecode(data.google_secret_manager_secret_version.ppe_platform_user_config_auth0_credentials.secret_data), "client_id")
    rca_auth0_client_secret          = lookup(jsondecode(data.google_secret_manager_secret_version.ppe_platform_user_config_auth0_credentials.secret_data), "client_secret")
  }
}

resource "kubernetes_secret" "platform_user_service_auth_token_secret" {
  provider = kubernetes.services-ppe

  for_each = toset([
    "enterprise-authentication",
    "sales-api",
    "sales-proposals",
  ])

  metadata {
    name      = "user-service-auth-token"
    namespace = each.key
  }

  data = {
    token    = data.google_secret_manager_secret_version.ppe_platform_user_config_api_auth.secret_data
    tokenSSO = data.google_secret_manager_secret_version.ppe_platform_user_config_sso_integration_token.secret_data
  }
}

data "google_secret_manager_secret_version" "sales_rfm_api_static_auth_token" {
  secret  = "sales_rfm_api_static_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_api_auth" {
  secret  = "sales_fleet_manager_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_fleet_manager_integrations_api_auth" {
  secret  = "sales_fleet_manager_integrations_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "platform_user_config_api_auth" {
  secret  = "platform_user_config_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "ims-shard-map-gcp" {
  secret  = "ims_shard_map"
  project = local.project
}
data "google_secret_manager_secret_version" "alloy-shard-map" {
  secret  = "alloy_shard_map"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_ingest_api_static_auth_token" {
  secret  = "sales_ingest_api_static_auth_token"
  project = local.project
}

data "google_secret_manager_secret_version" "sales_classification_api_auth" {
  secret  = "sales_classification_auth_token"
  project = local.project
}

resource "kubernetes_secret" "ims-shard-map" {
  provider = kubernetes.services-ppe

  for_each = toset([
    "sales-txns",
  ])

  metadata {
    name      = "ims-shard-map"
    namespace = each.key
  }

  data = {
    shard_map = data.google_secret_manager_secret_version.ims-shard-map-gcp.secret_data
  }
}

resource "kubernetes_secret" "sales_ingest_api_static_auth_token" {
  for_each = toset([
    "sales-txns"
  ])
  provider = kubernetes.services-ppe

  metadata {
    name      = "sales-ingest-api-static-auth-token"
    namespace = each.key
  }

  data = {
    key = data.google_secret_manager_secret_version.sales_ingest_api_static_auth_token.secret_data
  }
}

resource "google_secret_manager_secret_iam_member" "platform_user_config_enterprise_authentication_secret_permissions" {
  for_each = toset([
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-key.secret,
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-schema-reg-key.secret,
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-schema-reg-secret.secret,
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-secret.secret,
    data.google_secret_manager_secret_version.jwt-signing-keys.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_api_auth.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_auth_token.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_enterprise_authentication_key.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_default_webshop_credentials_key.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_ga4_credentials_key.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_sso_integration_token.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_api_auth.secret,
    # Will uncomment the following lines after the services are deployed to PPE
    # data.google_secret_manager_secret_version.sales_catalog_api_static_auth_token.secret,
    # data.google_secret_manager_secret_version.sales_fleet_manager_api_auth.secret,
    # data.google_secret_manager_secret_version.sales_ingest_api_static_auth_token.secret,
    # data.google_secret_manager_secret_version.sales_rfm_api_static_auth_token.secret,
    # data.google_secret_manager_secret_version.sales_catalog_global_secret.secret,
  ])
  project   = local.project
  secret_id = each.key
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.platform_user_config_service_account_email}"
}

data "google_secret_manager_secret_version" "honeycomb_api_key" {
  secret  = "honeycomb_api_key"
  project = local.project
}

resource "kubernetes_secret" "honeycomb_key" {
  for_each = toset([
    # "analytics-dtt",
    # "analytics-metrics",
    # "analytics-public-api",
    # "analytics-public-py-api",
    # "analytics-rdo-api",
    # "analytics-rdo-dataservice",
    # "appraisals-classify",
    # "appraisals-coda",
    # "appraisals-legacy-api-v2",
    # "appraisals-portal",
    # "appraisals-record360",
    # "appraisals-residuals",
    # "appraisals-valuations",
    # "appraisals-valuator",
    # "default",
    # "enterprise-domain-service",
    "enterprise-authentication",
    # "enterprise-config-service",
    # "enterprise-image-service",
    # "enterprise-messaging",
    # "enterprise-notification",
    # "fleet-manager-integrations",
    # "kafka-retry-service",
    # "platform-classification",
    # "platform-service-template",
    "platform-user-config",
    # "platform-valuation",
    # "rdo-user-service",
    # "sales-api",
    # "sales-cache",
    # "sales-catalog",
    # "sales-webshop",
    # "sales-cge",
    # "sales-classification",
    # "sales-fleet-manager",
    # "sales-fleet-manager-tools",
    # "sales-fmx-api",
    # "sales-portal",
    # "sales-proposals",
    # "sales-notification-service",
    # "sales-rfm-channels-api",
    "sales-txns",
    # "sales-ingest",
    # "valuation-classification",
    # "honeycomb-refinery",
  ])

  provider = kubernetes.services-ppe
  metadata {
    name      = "honeycomb-api-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    key = data.google_secret_manager_secret_version.honeycomb_api_key.secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_sales_txns" {
  secret  = "dbuser_sales_equipment_rfm_api"
  project = local.project
}


resource "kubernetes_secret" "dbuser_sales_txns" {
  provider = kubernetes.services-ppe
  for_each = toset([
    "sales-txns",
    # "sales-ingest",
  ])
  metadata {
    name      = "pg-equipment-rfm-api"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_sales_txns.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_sales_txns.secret_data), "password")
  }
}

# data "sentry_key" "default" {
#   for_each = data.terraform_remote_state.terraform_admin.outputs.sentry_projects
#
#   organization = each.value["org"]
#   project      = each.value["name"]
#   name         = "Default"
# }

# resource "kubernetes_secret" "sentry_integration_dsn" {
#   for_each = {
#     # analytics-metrics = {
#     #   sentry_project = "analytics_metrics_api"
#     # }
#     sales-txns = {
#       sentry_project = "sales_rfm_api"
#     }
#     # sales-proposals = {
#     #   sentry_project = "sales_proposals"
#     # }
#     # sales-ingest = {
#     #   sentry_project = "sales_ingest_api"
#     # }
#     # appraisals-portal = {
#     #   sentry_project = "appraisals_portal_api"
#     # }
#     # appraisals-valuations = {
#     #   sentry_project = "appraisals_values_api"
#     # }
#     # appraisals-classify = {
#     #   sentry_project = "classify_tags_api"
#     # }
#     # sales-catalog = {
#     #   sentry_project = "sales_catalog_api"
#     # }
#     # sales-webshop = {
#     #   sentry_project = "sales_webshop_api"
#     # }
#     platform-user-config = {
#       sentry_project = "platform_user_config"
#     }
#     # fleet-manager-integrations = {
#     #   sentry_project = "platform_fleet_manager_integrations"
#     # }
#     # sales-fmx-api = {
#     #   sentry_project = "sales_fmx_api"
#     # }
#     # sales-notification-service = {
#     #   sentry_project = "sales_notification_service"
#     # }
#     # sales-rfm-channels-api = {
#     #   sentry_project = "sales_rfm_channels_api"
#     # },
#     # appraisals-legacy-api-v2 = {
#     #   sentry_project = "appraisals_legacy_api_v2"
#     # },
#     # appraisals-dcs-api = {
#     #   sentry_project = "appraisals_dcs_api"
#     # },
#     # enterprise-domain-service = {
#     #   sentry_project = "enterprise_domain_service"
#     # },
#     # valuation-classification = {
#     #   sentry_project = "platform_valuation_taxonomy_service"
#     # },
#     # kafka-retry-service = {
#     #   sentry_project = "kafka_retry_service"
#     # },
#   }
#
#   provider = kubernetes.services-ppe
#   metadata {
#     name      = "sentry-integration-dsn"
#     namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
#   }
#
#   data = {
#     key = data.sentry_key.default[each.value["sentry_project"]].dsn_public
#   }
# }


data "google_secret_manager_secret_version" "algolia-api-key" {
  secret  = "sales-txns-algolia-api"
  project = local.project
}

resource "kubernetes_secret" "sales-txns-algolia-api" {
  provider = kubernetes.services-ppe

  for_each = toset([
    "sales-txns",
  ])

  metadata {
    name      = "sales-txns-algolia-api"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    id      = lookup(jsondecode(data.google_secret_manager_secret_version.algolia-api-key.secret_data), "id")
    api_key = lookup(jsondecode(data.google_secret_manager_secret_version.algolia-api-key.secret_data), "api_key")
  }
}

data "google_secret_manager_secret_version" "enterprise_authentication_secrets" {
  secret  = "enterprise_authentication_secrets"
  project = local.project
}

data "google_secret_manager_secret_version" "rfm_pgbouncer_userlist_ppe" {
  project = local.project
  secret  = "rfm-secret-userlist-pgbouncer"
}

resource "kubernetes_secret" "rfm_pgbouncer_userlist_ppe" {
  provider = kubernetes.services-ppe

  for_each = toset([
    "enterprise-pgbouncer-rfm03",
    "pgbouncer-rfmservices01",
  ])

  metadata {
    name      = "pgbouncer-pgbouncer-secret-userlist-txt"
    namespace = each.key
  }

  data = {
    "userlist.txt" = data.google_secret_manager_secret_version.rfm_pgbouncer_userlist_ppe.secret_data
  }
}

import {
  for_each = toset([
    "enterprise-pgbouncer-rfm03",
    "pgbouncer-rfmservices01",
  ])
  to = kubernetes_secret.rfm_pgbouncer_userlist_ppe[each.key]
  id = "${each.key}/pgbouncer-pgbouncer-secret-userlist-txt"
}

data "google_secret_manager_secret_version" "enterprise_usermanagement_api_secrets" {
  secret  = "enterprise_usermanagement_api_secrets"
  project = local.project
}

data "google_secret_manager_secret_version" "confluent-kafka-ppe-sa-key" {
  secret  = "confluent-kafka-ppe-sa-key"
  project = local.project
}

data "google_secret_manager_secret_version" "confluent-kafka-ppe-sa-secret" {
  secret  = "confluent-kafka-ppe-sa-secret"
  project = local.project
}

data "google_secret_manager_secret_version" "confluent-kafka-ppe-sa-schema-reg-key" {
  secret  = "confluent-kafka-ppe-sa-schema-reg-key"
  project = local.project
}

data "google_secret_manager_secret_version" "confluent-kafka-ppe-sa-schema-reg-secret" {
  secret  = "confluent-kafka-ppe-sa-schema-reg-secret"
  project = local.project
}