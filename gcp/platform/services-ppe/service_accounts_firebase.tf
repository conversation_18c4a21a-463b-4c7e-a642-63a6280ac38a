resource "google_service_account" "firebase_default" {
  project      = local.project
  account_id   = "firebase"
  display_name = "Firebase Default"
}

resource "google_project_iam_member" "firebase_default" {
  for_each = toset([
    "roles/datastore.user",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${google_service_account.firebase_default.email}"
}

resource "google_project_iam_member" "firebase_ci_permissions" {
  for_each = toset([
    "roles/firebase.admin",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${data.terraform_remote_state.project_structure.outputs.platform_services_ci}"
}