locals {
  platform_user_config_service_account_email = google_service_account.workload_identity["platform-user-config"].email
}

resource "google_project_iam_member" "platform_user_config_service_permissions" {
  for_each = toset([
    "roles/clouddebugger.agent",
    "roles/cloudprofiler.agent",
    "roles/cloudtrace.agent",
    "roles/logging.logWriter",
    "roles/storage.objectAdmin",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${local.platform_user_config_service_account_email}"
}

resource "google_project_iam_member" "platform_user_config_service_permissions_rfm" {
  for_each = toset([
    "roles/datastore.user",
  ])
  project = local.rfm_project
  role    = each.key
  member  = "serviceAccount:${local.platform_user_config_service_account_email}"
}

data "google_secret_manager_secret_version" "jwt-signing-keys" {
  secret  = "jwt-signing-keys"
  project = local.project
}

resource "google_secret_manager_secret_iam_member" "ppe_platform_user_config_enterprise_authentication_secret_permissions" {
  for_each = toset([
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-key.secret,
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-schema-reg-key.secret,
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-schema-reg-secret.secret,
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-secret.secret,
    data.google_secret_manager_secret_version.jwt-signing-keys.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_api_auth.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_auth_token.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_enterprise_authentication_key.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_default_webshop_credentials_key.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_ga4_credentials_key.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_sso_integration_token.secret,
    data.google_secret_manager_secret_version.ppe_platform_user_config_api_auth.secret,
    # Will uncomment the following lines after the services are deployed to PPE
    # data.google_secret_manager_secret_version.sales_catalog_api_static_auth_token.secret,
    # data.google_secret_manager_secret_version.sales_fleet_manager_api_auth.secret,
    # data.google_secret_manager_secret_version.sales_ingest_api_static_auth_token.secret,
    # data.google_secret_manager_secret_version.sales_rfm_api_static_auth_token.secret,
    # data.google_secret_manager_secret_version.sales_catalog_global_secret.secret,
  ])
  project   = local.project
  secret_id = each.key
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.platform_user_config_service_account_email}"
}

resource "google_project_iam_member" "user_config_sales_webshop_secrets" {
  for_each = toset([
    "roles/secretmanager.admin"
  ])
  project = data.terraform_remote_state.project_structure.outputs.projects["webshop-dev"]
  role    = each.key
  member  = "serviceAccount:${local.platform_user_config_service_account_email}"
}

resource "google_project_iam_member" "platform_user_config_apigee_permissions" {
  for_each = toset([
    "roles/apigee.developerAdmin",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${local.platform_user_config_service_account_email}"
}