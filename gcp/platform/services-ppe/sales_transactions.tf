module "sales_txns_queue" {
  source      = "../../modules/events/pubsub/v1"
  name        = "sales-txns-queue"
  environment = var.environment
  division    = "sales"
  role        = "queue"
  project     = local.project
  region      = var.region
  publishers = [
    "serviceAccount:${local.sales_txns_account_email}"
  ]
  subscribers = [
    "serviceAccount:${local.sales_txns_account_email}"
  ]
  providers = {
    google = google-beta
  }
}

output "sales_txns_queue" {
  value = module.sales_txns_queue.pubsub_topic
}

