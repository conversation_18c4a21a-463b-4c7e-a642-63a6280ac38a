locals {
  namespaces = [
    "default",
    "enterprise-authentication",
    "enterprise-pgbouncer-rfm03",
    "enterprise-pgbouncer-rfm04",
    "sales-txns",
    "platform-user-config",
    "pgbouncer-rfmservices01"
  ]
}

resource "google_service_account" "workload_identity" {
  for_each     = toset(local.namespaces)
  account_id   = each.key
  display_name = substr("GCP SA bound to K8S SA ${each.key}", 0, 100)
  project      = local.project
}

resource "kubernetes_namespace" "workload_identity" {
  for_each = toset(local.namespaces)
  metadata {
    name = each.key
  }
  provider = kubernetes.services-ppe
}

resource "kubernetes_service_account" "workload_identity" {
  for_each = toset(local.namespaces)
  metadata {
    name      = each.key
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
    annotations = {
      "iam.gke.io/gcp-service-account" = google_service_account.workload_identity[each.key].email
    }
  }
  provider = kubernetes.services-ppe
}

resource "google_service_account_iam_member" "workload_identity" {
  for_each           = toset(local.namespaces)
  service_account_id = google_service_account.workload_identity[each.key].name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${local.project}.svc.id.goog[${each.key}/${kubernetes_service_account.workload_identity[each.key].metadata[0].name}]"
}

output "workload_identity_k8s_accounts" {
  value = {
    for namespace in local.namespaces :
    namespace => kubernetes_service_account.workload_identity[namespace].metadata[0].name
  }
}

output "workload_identity_gcp_service_accounts" {
  value = {
    for namespace in local.namespaces :
    namespace => google_service_account.workload_identity[namespace].email
  }
} 

