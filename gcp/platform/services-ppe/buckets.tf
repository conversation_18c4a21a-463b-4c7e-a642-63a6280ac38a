module "sales_txns_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "sales-txns"
  project_name = local.project
  region       = var.region

  cors = [{
    origin = [
      "https://localhost:5001",
      "https://localhost:5100",
      "https://rfm.ppe.rousesales.com",
      "https://ppe-salesplatform.rousesales.com",
    ]
    method          = ["GET", "HEAD", "OPTIONS"]
    response_header = ["Content-Type", "Access-Control-Allow-Origin"]
    max_age_seconds = 3600
  }]

  lifecycle_rules = {
    version_retention = {
      condition = {
        age        = 14
        with_state = "ARCHIVED"
      }
      action = {
        type = "Delete"
      }
    }
  }
}

module "sales_assets_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "sales-assets"
  project_name = local.project
  region       = var.region
}

module "collateral_output_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "collateral-output"
  project_name = local.project
  region       = var.region
}

module "auth_assets_bucket" {
  source       = "../../modules/storage/bucket/v1"
  name         = "auth-assets"
  project_name = local.project
  region       = var.region
  versioning   = false
}


module "platform_user_config_bucket" {
  source       = "../../modules/storage/bucket/v1"
  name         = "platform-user-config"
  project_name = local.project
  region       = var.region
}
