locals {
  sales_txns_account_email = google_service_account.workload_identity["sales-txns"].email
}

resource "google_storage_bucket_iam_member" "sales_txns_collateral_output" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/storage.legacyBucketReader",
  ])
  bucket = module.collateral_output_bucket.bucket_name
  role   = each.key
  member = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_service_account_iam_member" "sales_txns_rfm_firebase_sdk" {
  service_account_id = data.google_service_account.firebase_admin_sdk.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_project_iam_member" "sales_txns_firebase_ppe" {
  for_each = toset([
    "roles/firebase.admin"
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_storage_bucket_iam_member" "sales_txns_rfm_transactions" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/storage.legacyBucketReader",
  ])
  bucket = module.sales_txns_bucket.bucket_name
  role   = each.key
  member = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_storage_bucket_iam_member" "sales_txns_sales_assets" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/storage.legacyBucketReader",
  ])
  bucket = module.sales_assets_bucket.bucket_name
  role   = each.key
  member = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_project_iam_member" "sales_txns_permissions" {
  for_each = toset([
    "roles/cloudprofiler.agent",
    "roles/clouddebugger.agent",
    "roles/monitoring.metricWriter",
    "roles/cloudtrace.admin",
    "roles/redis.viewer",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_fleet_manager_api_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_fleet_manager_api_auth.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_secret_manager_secret_iam_member" "platform_user_config_api_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.platform_user_config_api_auth.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_txns_ims_shard_mapsecret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.ims-shard-map-gcp.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_txns_account_email}"
}

data "google_secret_manager_secret_version" "sales_txns_multi_tenant_api_key_secret" {
  secret  = "multi_tenant_api_key"
  project = local.project
}

resource "google_secret_manager_secret_iam_member" "sales_txns_multi_tenant_api_key_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_txns_multi_tenant_api_key_secret.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_txns_account_email}"
}


resource "google_secret_manager_secret_iam_member" "sales-txns-confluent-kafka" {
  for_each = toset([
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-key.secret,
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-secret.secret,
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-schema-reg-key.secret,
    data.google_secret_manager_secret_version.confluent-kafka-ppe-sa-schema-reg-secret.secret,
  ])
  project   = local.project
  secret_id = each.key
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_rfm_api_static_auth_token_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_rfm_api_static_auth_token.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_txns_fleet_manager_integrations_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_fleet_manager_integrations_api_auth.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_txns_alloy_shard_map_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.alloy-shard-map.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_txns_account_email}"
}

resource "google_secret_manager_secret_iam_member" "sales_txns_sales_classification_secret_permissions" {
  project   = local.project
  secret_id = data.google_secret_manager_secret_version.sales_classification_api_auth.secret
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.sales_txns_account_email}"
}


resource "google_service_account_iam_binding" "sales_txns_account_email_service_account_user" {
  for_each           = toset(["roles/iam.serviceAccountTokenCreator", "roles/iam.serviceAccountUser"])
  service_account_id = google_service_account.workload_identity["sales-txns"].id
  role               = each.key

  members = [
    "group:<EMAIL>",
    "serviceAccount:${local.sales_txns_account_email}",
  ]
}