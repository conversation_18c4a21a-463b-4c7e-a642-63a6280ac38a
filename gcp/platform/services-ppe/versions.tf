terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.45.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 5.45.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.36.0"
    }
    castai = {
      source  = "castai/castai"
      version = "~> 7.45"
    }
    sentry = {
      source  = "jianyuan/sentry"
      version = "~> 0.12.2"
    }
  }
}
