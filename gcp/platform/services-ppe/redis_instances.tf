module "sales_rfm_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-rfm-stdcache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 128
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

# Redis instance for FMI to use for batch job processing
module "fleet_manager_integrations_cache_redis_std_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "fleet-manager-integrations-stdcache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 129
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "platform"
  memory_size_gb  = 2
}

module "sales_rfm_channels_api_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-rfm-channels-api-cache"
  environment     = var.environment
  tier            = "STANDARD_HA"
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 130
  redis_version   = "REDIS_4_0"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}