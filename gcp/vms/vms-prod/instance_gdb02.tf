module "gdb02" {
  source               = "../../modules/compute/compute-instance-automated/v1"
  project              = local.project
  name                 = "gdb02"
  service_account_name = "gdb02svc"
  machine_type         = "n2-custom-12-262144-ext"
  disk_type            = "pd-ssd"
  disk_size            = "300"
  image                = "projects/images-4a3fb6/global/images/gdb02-standard-windows-server-2016-**********"
  subnetwork           = local.subnetwork
  environment          = var.environment
  tags = [
    "enable-ids-analysis",
    "rdp-internal",
    "winrm-internal",
    "smb-internal",
    "wmi-internal",
    "http-internal"
  ]
  additional_labels = {
    role          = "database"
    server_class  = "gdb02"
    database_data = "snapshots"
    division      = "appraisals"
    critical      = "true"
    always_on     = "true"
  }
  disks = [
    module.gdb02-data1-disk.disk_id,
    module.gdb02-log1-disk.disk_id,
    module.gdb02-tempdb2-disk.disk_id,
  ]
}
module "gdb02-data1-disk" {
  source   = "../../modules/compute/persistent-disks/v1"
  name     = "gdb02-data1"
  zone     = "us-central1-b"
  type     = "pd-ssd"
  project  = local.project
  labels   = module.gdb02.instance_labels
  snapshot = "projects/rouse-gcp-migration-prod/global/snapshots/gcpdb02-data1-2020-08-20-18-07"
  size     = 4736
}
module "gdb02-log1-disk" {
  source   = "../../modules/compute/persistent-disks/v1"
  name     = "gdb02-log1"
  zone     = "us-central1-b"
  type     = "pd-ssd"
  project  = local.project
  labels   = module.gdb02.instance_labels
  snapshot = "projects/rouse-gcp-migration-prod/global/snapshots/gcpdb02-log1-2020-08-20-18-07"
  size     = 2640
}
module "gdb02-tempdb2-disk" {
  source      = "../../modules/compute/persistent-disks/v1"
  name        = "gdb02-tempdb2"
  zone        = "us-central1-b"
  type        = "pd-ssd"
  project     = local.project
  labels      = module.gdb02.instance_labels
  size        = 1224
  description = "DI-4634"
}

resource "google_dns_record_set" "gdb02" {
  name    = "gdb02.rouseclients.prod."
  project = local.project
  type    = "A"
  ttl     = 5

  managed_zone = "vms-prod-private-zone"
  rrdatas      = [module.gdb02.internal_ip]
}

resource "google_project_iam_member" "services_dev_permission_gdb02" {
  for_each = toset([
    "roles/storage.objectAdmin",
  ])
  project = data.terraform_remote_state.project_structure.outputs.projects["services-dev"]
  role    = each.key
  member  = "serviceAccount:${module.gdb02.service_account_email}"
}

resource "google_project_iam_member" "services_prod_permission_gdb02" {
  for_each = toset([
    "roles/storage.objectAdmin",
  ])
  project = data.terraform_remote_state.project_structure.outputs.projects["services-prod"]
  role    = each.key
  member  = "serviceAccount:${module.gdb02.service_account_email}"
}

output "gdb02_service_account" {
  value = module.gdb02.service_account_email
}
