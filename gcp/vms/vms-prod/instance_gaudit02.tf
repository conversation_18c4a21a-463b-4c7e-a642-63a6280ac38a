## See: https://github.com/RouseServices/infrastructure-docs/blob/master/Infrastructure/Bloodhound/Setup.md
## Need to manually attach to the AD domain
# 
#locals {
#  gaudit02_name = "gaudit02-${var.environment}"
#}
#
#module "gaudit02" {
#  source                 = "../../modules/compute/compute-instance/v1"
#  name                   = local.gaudit02_name
#  project                = local.project
#  machine_type           = "e2-standard-2"
#  zone                   = "us-central1-b"
#  image                  = "projects/images-4a3fb6/global/images/windows-server-2016-base-1687176119"
#  subnetwork             = local.subnetwork
#  disk_size              = 200
#  disk_type              = "pd-balanced"
#  environment            = var.environment
#  block_project_ssh_keys = true
#  additional_labels = {
#    division      = "rbcompliance",
#    environment   = var.environment
#    instance_name = local.gaudit02_name,
#    role          = "audit",
#    server_class  = local.gaudit02_name,
#    server_name   = local.gaudit02_name,
#  }
#  tags = [
#    "enable-ids-analysis",
#    "rdp-internal",
#    "winrm-internal"
#  ]
#}
#
#
#output "gaudit02_service_account_email" {
#  value = module.gaudit02.service_account_email
#}
