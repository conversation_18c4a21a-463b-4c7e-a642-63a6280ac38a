locals {
  argocd_service_account = google_service_account.platform_engineering_workload_identity["argocd"]
}

# This permission is required by ArgoCD so we can pull infrastructure images from GCS
resource "google_project_iam_member" "helm_chart_repo_storage" {
  for_each = toset([
    "roles/storage.objectViewer",
  ])
  project = local.images
  role    = "roles/storage.objectViewer"
  member  = "serviceAccount:${google_service_account.platform_engineering_workload_identity["argocd"].email}"
}

# Reserved IP for internal ingress for ArgoCD server
resource "google_compute_address" "argocd_ingress_ip" {
  name         = "argocd-ingress-ip"
  purpose      = "SHARED_LOADBALANCER_VIP"
  address_type = "INTERNAL"
  project      = local.project
  subnetwork   = module.platform_engineering_cluster_network.subnet_link
}


# Reserved IP for nginx ingress controller
resource "google_compute_address" "nginx_ingress_controller_ip" {
  project      = local.project
  name         = "nginx-ingress-controller-reserved-ip-regional"
  description  = "Regional IP reserved for NGINX ingress controller (platform engineering cluster)"
  region       = var.region
  address_type = "EXTERNAL"
  address      = "*************"
}

import {
  id = "projects/${local.project}/regions/us-central1/addresses/nginx-ingress-controller-reserved-ip-regional"
  to = google_compute_address.nginx_ingress_controller_ip
}

provider "helm" {
  kubernetes {
    host                   = "https://${module.platform_engineering_kubernetes_cluster.gke_cluster.endpoint}"
    token                  = data.google_client_config.current.access_token
    cluster_ca_certificate = base64decode(module.platform_engineering_kubernetes_cluster.gke_cluster.master_auth.0.cluster_ca_certificate)
  }

  alias = "platform-engineering"
}

resource "helm_release" "argocd" {
  name = "argocd"

  repository       = "https://argoproj.github.io/argo-helm"
  chart            = "argo-cd"
  namespace        = "argocd"
  version          = "7.3.4"
  create_namespace = true

  values = [
    file("argocd/main_argocd_values.yaml"),
  ]

  provider = helm.platform-engineering
}

import {
  id = "argocd/argocd"
  to = helm_release.argocd
}

resource "google_project_iam_member" "argocd_terraform_admin_permissions" {
  for_each = toset(local.argocd_account_permissions)
  project  = local.project
  role     = each.key
  member   = "serviceAccount:${local.argocd_service_account.email}"
}

import {
  id = "terraform-admin-960537d6 roles/container.admin serviceAccount:<EMAIL>"
  to = google_project_iam_member.argocd_terraform_admin_permissions["roles/container.admin"]
}

import {
  id = "terraform-admin-960537d6 roles/iam.workloadIdentityUser serviceAccount:<EMAIL>"
  to = google_project_iam_member.argocd_terraform_admin_permissions["roles/iam.workloadIdentityUser"]
}
import {
  id = "terraform-admin-960537d6 roles/iam.serviceAccountTokenCreator serviceAccount:<EMAIL>"
  to = google_project_iam_member.argocd_terraform_admin_permissions["roles/iam.serviceAccountTokenCreator"]
}

# Configure ArgoCD Permissions on target clusters
# This involves both granting permissions to the ArgoCD service account on the target cluster
# While also creating Kubernetes secrets in the argocd namespace that contains the cluster endpoint info and certificate
# Services Dev

data "google_container_cluster" "services_dev" {
  location = data.terraform_remote_state.services_dev.outputs.services_cluster_location
  name     = data.terraform_remote_state.services_dev.outputs.services_cluster_name
  project  = data.terraform_remote_state.services_dev.outputs.project
}

resource "google_project_iam_member" "argocd_services_dev_permissions" {
  for_each = toset(local.argocd_account_permissions)
  project  = data.terraform_remote_state.services_dev.outputs.project
  role     = each.key
  member   = "serviceAccount:${local.argocd_service_account.email}"
}


provider "kubernetes" {
  alias                  = "services-dev"
  host                   = "https://${data.google_container_cluster.services_dev.endpoint}"
  token                  = data.google_client_config.current.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.services_dev.master_auth.0.cluster_ca_certificate)
}

resource "kubernetes_secret" "argocd_services_dev" {
  provider = kubernetes.platform-engineering 
  metadata {
    name      = "services-dev-cluster"
    namespace = "argocd"
    labels = {
      "argocd.argoproj.io/secret-type" = "cluster"
    }
    annotations = {}
  }

  data = {
    name   = "services-dev"
    server = "https://${data.google_container_cluster.services_dev.endpoint}"
    config = jsonencode({
      execProviderConfig = {
        command    = "argocd-k8s-auth"
        args       = ["gcp"]
        apiVersion = "client.authentication.k8s.io/v1beta1"
      }
      tlsClientConfig = {
        insecure = false
        caData   = data.google_container_cluster.services_dev.master_auth[0].cluster_ca_certificate
      }
    })
  }

  lifecycle {
    ignore_changes = [metadata[0].annotations]
  }
}


# Services Prod
data "google_container_cluster" "services_prod" {
  location = data.terraform_remote_state.services_prod.outputs.services_cluster_location
  name     = data.terraform_remote_state.services_prod.outputs.services_cluster_name
  project  = data.terraform_remote_state.services_prod.outputs.project
}

resource "google_project_iam_member" "argocd_services_prod_permissions" {
  for_each = toset(local.argocd_account_permissions)
  project  = data.terraform_remote_state.services_prod.outputs.project
  role     = each.key
  member   = "serviceAccount:${local.argocd_service_account.email}"
}

provider "kubernetes" {
  alias                  = "services-prod"
  host                   = "https://${data.google_container_cluster.services_prod.endpoint}"
  token                  = data.google_client_config.current.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.services_prod.master_auth.0.cluster_ca_certificate)
}

resource "kubernetes_secret" "argocd_services_prod" {
  provider = kubernetes.platform-engineering 
  metadata {
    name      = "services-prod-cluster"
    namespace = "argocd"
    labels = {
      "argocd.argoproj.io/secret-type" = "cluster"
    }
    annotations = {}
  }

  data = {
    name   = "services-prod"
    server = "https://${data.google_container_cluster.services_prod.endpoint}"
    config = jsonencode({
      execProviderConfig = {
        command    = "argocd-k8s-auth"
        args       = ["gcp"]
        apiVersion = "client.authentication.k8s.io/v1beta1"
      }
      tlsClientConfig = {
        insecure = false
        caData   = data.google_container_cluster.services_prod.master_auth[0].cluster_ca_certificate
      }
    })
  }

  lifecycle {
    ignore_changes = [metadata[0].annotations]
  }
}

data "terraform_remote_state" "services_ppe" {
  backend = "gcs"
  config = {
    bucket = "terraform-admin-state-bucket"
    prefix = "platform/services-ppe"
  }
}

# Services PPE
data "google_container_cluster" "services_ppe" {
  location = data.terraform_remote_state.services_ppe.outputs.services_cluster_location
  name     = data.terraform_remote_state.services_ppe.outputs.services_cluster_name
  project  = data.terraform_remote_state.services_ppe.outputs.project
}

resource "google_project_iam_member" "argocd_services_ppe_permissions" {
  for_each = toset(local.argocd_account_permissions)
  project  = data.terraform_remote_state.services_ppe.outputs.project
  role     = each.key
  member   = "serviceAccount:${local.argocd_service_account.email}"
}

provider "kubernetes" {
  alias                  = "services-ppe"
  host                   = "https://${data.google_container_cluster.services_ppe.endpoint}"
  token                  = data.google_client_config.current.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.services_ppe.master_auth.0.cluster_ca_certificate)
}

resource "kubernetes_secret" "argocd_services_ppe" {
  provider = kubernetes.platform-engineering 
  metadata {
    name      = "services-ppe-cluster"
    namespace = "argocd"
    labels = {
      "argocd.argoproj.io/secret-type" = "cluster"
    }
    annotations = {}
  }

  data = {
    name   = "services-ppe"
    server = "https://${data.google_container_cluster.services_ppe.endpoint}"
    config = jsonencode({
      execProviderConfig = {
        command    = "argocd-k8s-auth"
        args       = ["gcp"]
        apiVersion = "client.authentication.k8s.io/v1beta1"
      }
      tlsClientConfig = {
        insecure = false
        caData   = data.google_container_cluster.services_ppe.master_auth[0].cluster_ca_certificate
      }
    })
  }

  lifecycle {
    ignore_changes = [metadata[0].annotations]
  }
}