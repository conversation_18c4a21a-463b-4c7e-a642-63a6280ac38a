resource "google_monitoring_alert_policy" "rdo_ui_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - RDO UI"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RDO UI"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-ui-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow burn rate on Availability SLO - RDO UI"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-ui-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - RDO UI"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rdo_api_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - RDO-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow burn rate on Availability SLO - RDO-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-api-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Availability SLO - RDO-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rdo_api_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - RDO-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RDO-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-api-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Availability SLO - RDO-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rdo_api_hmi_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - RDO API HMI Client"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = " Fast Burn rate on Latency SLO - RDO API HMI Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-api-hmi-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow burn rate on Latency SLO - RDO API HMI Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-api-hmi-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - RDO API HMI Client"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "mobile_rates_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Rates and availability"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Rates and availability"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/mobile-rates-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow burn rate on Availability SLO - Rates and availability"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/mobile-rates-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Rates and availability"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "metrics_api_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - METRICS-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow burn rate on Availability SLO - METRICS-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/metrics-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Availability SLO - METRICS-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "metrics_api_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - METRICS-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - METRICS-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/metrics-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Availability SLO - METRICS-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "metrics_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - METRICS API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - METRICS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/metrics-api-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow burn rate on Latency SLO - METRICS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/metrics-api-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - METRICS API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rdo_user_services_availability_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - RDO USER SERVICE"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow burn rate on Availability SLO - RDO USER SERVICE"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-user-service-events-count-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Availability SLO - RDO USER SERVICE"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rdo_user_services_availability_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - RDO USER SERVICE"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RDO USER SERVICE"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-user-service-events-count-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Availability SLO - RDO USER SERVICE"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rdo_user_service_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - RDO USER Service"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - RDO USER Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-user-service-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow burn rate on Latency SLO - RDO USER Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rdo-web-service-prod/serviceLevelObjectives/rdo-user-service-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - RDO USER Service"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v1_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - Public API V1"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow burn rate on Availability SLO - Public API V1"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Availability SLO - Public API V1"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v1_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - Public API V1"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Public API V1"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Availability SLO - Public API V1"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v1_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Public API V1"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Public API V1"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v1-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow burn rate on Latency SLO - Public API V1"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v1-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - Public API V1"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v1_latency_slo_prod_burn_rate_alert_high_latency" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Public API V1 (High latency endpoints)"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    # local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Public API V1"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v1-latency-slo-prod-high\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Fast Burn rate alert for Latency SLO - Public API V1 (High latency endpoints)"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v1_latency_slo_prod_burn_rate_alert_low_latency" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Public API V1 (Low latency endpoints)"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    # local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Public API V1"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v1-latency-slo-prod-low\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Fast Burn rate alert for Latency SLO - Public API V1 (Low latency endpoints)"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v2_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - Public API V2"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow burn rate on Availability SLO - Public API V2"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v2-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Availability SLO - Public API V2"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v2_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - Public API V2"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Public API V2"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v2-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Availability SLO - Public API V2"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v2_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Public API V2"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Public API V2"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v2-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow burn rate on Latency SLO - Public API V2"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v2-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - Public API V2"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v2_latency_slo_prod_burn_rate_alert_high_latency" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Public API V2 (High latency endpoints)"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    # local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Public API V2 (High latency endpoints)"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v2-latency-slo-prod-high\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Fast Burn rate alert for Latency SLO - Public API V2 (High latency endpoints)"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v2_latency_slo_prod_burn_rate_alert_medium_latency" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Public API V2 (Medium latency endpoints)"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    # local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Public API V2 (Medium latency endpoints)"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v2-latency-slo-prod-medium\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Fast Burn rate alert for Latency SLO - Public API V2 (Medium latency endpoints)"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "public_api_v2_latency_slo_prod_burn_rate_alert_low_latency" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Public API V2 (Low latency endpoints)"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    # local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Public API V2 (Low latency endpoints)"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/public-api-service/serviceLevelObjectives/public-api-v2-latency-slo-prod-low\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Fast Burn rate alert for Latency SLO - Public API V2 (Low latency endpoints)"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_ios_rfm_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - RFM-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow burn rate on Availability SLO - RFM-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-ios-rfm-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - RFM-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_ios_rfm_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - RFM-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RFM-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-ios-rfm-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Burn rate alert for Availability SLO - RFM-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rfm_api_rsma_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - RFM API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - RFM API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rfm-api-rsma-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - RFM API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rfm_api_rsma_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - RFM API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - RFM API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rfm-api-rsma-latency-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - RFM API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rfm_api_rsma_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - RFM API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - RFM API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rfm-api-rsma-latency-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Burn rate alert for Latency SLO - RFM API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_sales_api_general_availability_slo_slow_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - RSMA-SALES-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - RSMA-SALES-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-sales-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - RSMA-SALES-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_sales_api_general_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - RSMA-SALES-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RSMA-SALES-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-sales-api-general-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - RSMA-SALES-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_sales_api_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Latency SLO - RSMA SALES API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - RSMA SALES API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-sales-api-latency-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - RSMA SALES API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_sales_api_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - RSMA SALES API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - RSMA SALES API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-sales-api-latency-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - RSMA SALES API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_api_general_availability_slo_slow_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - RSMA-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - RSMA-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - RSMA-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_api_general_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - RSMA-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RSMA-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-api-general-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - RSMA-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_api_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Latency SLO - RSMA API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - RSMA API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-api-latency-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - RSMA API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rsma_api_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - RSMA API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - RSMA API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rsma-ios-service/serviceLevelObjectives/rsma-api-latency-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Burn rate alert for Latency SLO - RSMA API"
    mime_type = "text/markdown"
  }
}



resource "google_monitoring_alert_policy" "webshopsv2_new_api_general_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Webshops New API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Webshops New API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv2-service-prod/serviceLevelObjectives/webshopsv2-new-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Webshops New API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv2-service-prod/serviceLevelObjectives/webshopsv2-new-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Webshops New API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "webshopsv2_new_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Webshops New API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Webshops New API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv2-service-prod/serviceLevelObjectives/webshopsv2-new-api-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - Webshops New API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv2-service-prod/serviceLevelObjectives/webshopsv2-new-api-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - Webshops New API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "webshops_xyz_catalog_api_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - WEBSHOPS XYZ Catalog API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - WEBSHOPS XYZ Catalog API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-xyz-catalog-api-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - WEBSHOPS XYZ Catalog API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-xyz-catalog-api-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - WEBSHOPS XYZ Catalog API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "webshops_xyz_catalog_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - WEBSHOPS XYZ CATALOG API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - WEBSHOPS XYZ CATALOG API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-xyz-catalog-api-latency-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - WEBSHOPS XYZ CATALOG API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-xyz-catalog-api-latency-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - WEBSHOPS XYZ CATALOG API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "webshops_catalog_assets_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Catalog Assets"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Catalog Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-catalog-assets-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Catalog Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-catalog-assets-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Catalog Assets"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "catalogs_assets_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Catalogs Assets"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Catalogs Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/catalogs-assets-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - Catalogs Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/catalogs-assets-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - Catalogs Assets"
    mime_type = "text/markdown"
  }
}



resource "google_monitoring_alert_policy" "webshops_general_catalogs_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - All Catalogs"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - All Catalogs"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-general-catalogs-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - All Catalogs"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-general-catalogs-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - All Catalogs"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "webshops_general_catalog_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - WEBSHOPS ALL Catalogs API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - WEBSHOPS ALL Catalogs API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-general-catalog-latency-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - WEBSHOPS ALL Catalogs API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-general-catalog-latency-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - WEBSHOPS ALL Catalogs API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "webshops_general_catalog_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - WEBSHOPS ALL CATALOGS API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - WEBSHOPS ALL CATALOGS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-general-catalog-api-latency-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - WEBSHOPS ALL CATALOGS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-general-catalog-api-latency-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - WEBSHOPS ALL CATALOGS API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "webshopsv2_proposals_assets_general_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Proposals Assets"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Proposals Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv2-service-prod/serviceLevelObjectives/webshopsv2-proposals-assets-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Proposals Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv2-service-prod/serviceLevelObjectives/webshopsv2-proposals-assets-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Proposals Assets"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "webshops_xyz_catalogs_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - XYZ Catalog"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - XYZ Catalog"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-xyz-catalogs-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - XYZ Catalog"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/webshopsv1-service-prod/serviceLevelObjectives/webshops-xyz-catalogs-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - XYZ Catalog"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "portal_sales_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Portal Sales API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Portal Sales API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-sales-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Portal Sales API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-sales-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Portal Sales API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "portal_sales_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Portal Sales API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Portal Sales API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-sales-api-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - Portal Sales API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-sales-api-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - Portal Sales API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "cache_api_general_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - CACHE API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - CACHE API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/cache-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - CACHE API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/cache-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - CACHE API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "cache_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - CACHE API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - CACHE API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/cache-api-latency-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - CACHE API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/cache-api-latency-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - CACHE API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "portal_rouseservices_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Portal RouseServices"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Portal RouseServices"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-rouseservices-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Portal RouseServices"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-rouseservices-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Portal RouseServices"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "portal_rouseservices_rfm_client_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Portal RouseServices RFM Client"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Portal RouseServices RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-rouseservices-rfm-client-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Portal RouseServices RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-rouseservices-rfm-client-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Portal RouseServices RFM Client"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "portal_rousesales_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Portal RouseSales"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Portal RouseSales"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-rousesales-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Portal RouseSales"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-rousesales-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Portal RouseSales"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "portal_rousesales_rfm_client_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Portal RouseSales RFM Client"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Portal RouseSales RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-rousesales-rfm-client-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Portal RouseSales RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/portal-rousesales-rfm-client-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Portal RouseSales RFM Client"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fleet_manager_integrations_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - FLEET-MANAGER-INTEGRATIONS-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - FLEET-MANAGER-INTEGRATIONS-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/fleet-manager-integrations-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - FLEET-MANAGER-INTEGRATIONS-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/fleet-manager-integrations-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - FLEET-MANAGER-INTEGRATIONS-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fleet_manager_integrations_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - FLEET MANAGER INTEGRATIONS API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - FLEET MANAGER INTEGRATIONS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/fleet-manager-integrations-api-latency-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - FLEET MANAGER INTEGRATIONS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/fleet-manager-integrations-api-latency-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - FLEET MANAGER INTEGRATIONS API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "user_service_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - User Service"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"],
    local.notification_channels["pagerduty_rfm_services"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - User Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/user-service-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - User Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/user-service-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - User Service"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "user_service_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - User Service"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"],
    local.notification_channels["pagerduty_rfm_services"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - User Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/user-service-api-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - User Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/user-service-api-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - User Service"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "sales_rfm_ingest_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - RFM-INGEST-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RFM-INGEST-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-rfm-ingest-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - RFM-INGEST-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-rfm-ingest-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - RFM-INGEST-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rfm_ingest_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - RFM INGEST API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - RFM INGEST API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/rfm-ingest-api-availability-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - RFM INGEST API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/rfm-ingest-api-availability-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - RFM INGEST API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "sales_rfm_channels_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - RFM-CHANNELS-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RFM-CHANNELS-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-rfm-channels-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - RFM-CHANNELS-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-rfm-channels-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - RFM-CHANNELS-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "sales_rfm_channels_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - SALES RFM CHANNELS API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - SALES RFM CHANNELS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-rfm-channels-api-availability-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - SALES RFM CHANNELS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-rfm-channels-api-availability-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - SALES RFM CHANNELS API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "sales_rfm_channels_api_latency_slo_prod_burn_rate_alert_bulk" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - SALES RFM CHANNELS API (Bulk endpoints)"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - SALES RFM CHANNELS API (Bulk endpoints)"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-rfm-channels-api-latency-slo_prod_bulk\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - SALES RFM CHANNELS API (Bulk endpoints)"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-rfm-channels-api-latency-slo_prod_bulk\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - SALES RFM CHANNELS API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "collaterals_rousesales_general_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Collaterals Generator RouseSales"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Collaterals Generator RouseSales"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/collaterals-rousesales-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Collaterals Generator RouseSales"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/collaterals-rousesales-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Collaterals Generator RouseSales"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "collaterals_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - COLLATERALS API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - COLLATERALS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/collaterals-api-latency-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - COLLATERALS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/collaterals-api-latency-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - COLLATERALS API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "collaterals_maker_rousesales_general_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Collaterals Maker RouseSales"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Collaterals Maker RouseSales"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/collateral-maker-rousesales-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Collaterals Maker RouseSales"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/collateral-maker-rousesales-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Collaterals Maker RouseSales"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "collaterals_maker_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - COLLATERALS MAKER API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - COLLATERALS MAKER API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/collaterals-maker-api-latency-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - COLLATERALS MAKER API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/collaterals-maker-api-latency-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - COLLATERALS MAKER API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "united_rentals_portal_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - United Rentals Portal"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - United Rentals Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/united-rentals-portal-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - United Rentals Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/united-rentals-portal-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - United Rentals Portal"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "united_rentals_portal_rfm_client_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - United Rentals Portal RFM Client"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - United Rentals Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/united-rentals-portal-rfm-client-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - United Rentals Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/united-rentals-portal-rfm-client-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - United Rentals Portal RFM Client"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "equipment_depot_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Equipment Depot Portal"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Equipment Depot Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/equipment-depot-portal-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Equipment Depot Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/equipment-depot-portal-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Equipment Depot Portal"
    mime_type = "text/markdown"
  }
}


resource "google_monitoring_alert_policy" "kiewit_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Kiewit Portal"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Kiewit Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/kiewit-portal-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Kiewit Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/kiewit-portal-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Kiewit Portal"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "kiewit_rfm_client_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Kiewit Portal RFM Client"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Kiewit Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/kiewit-portal-rfm-client-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Kiewit Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/kiewit-portal-rfm-client-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Kiewit Portal RFM Client"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "herc_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Herc Portal"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Herc Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/herc-portal-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Herc Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/herc-portal-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Herc Portal"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "herc_rfm_client_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Herc Portal RFM Client"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Herc Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/herc-portal-rfm-client-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Herc Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/herc-portal-rfm-client-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Herc Portal RFM Client"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "sunstate_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Sunstate Portal"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Sunstate Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sunstate-portal-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Sunstate Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sunstate-portal-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Sunstate Portal"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "sunstate_rfm_client_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Sunstate Portal RFM Client"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Sunstate Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sunstate-portal-rfm-client-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Sunstate Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sunstate-portal-rfm-client-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Sunstate Portal RFM Client"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "he_equipment_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - HE Equipment Portal"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - HE Equipment Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/he-equipment-portal-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - HE Equipment Portal"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/he-equipment-portal-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - HE Equipment Portal"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "he_equipment_rfm_client_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - HE Equipment Portal RFM Client"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - HE Equipment Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/he-equipment-portal-rfm-client-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - HE Equipment Portal RFM Client"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/he-equipment-portal-rfm-client-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - HE Equipment Portal RFM Client"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rfm_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - RFM-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"],
    local.notification_channels["pagerduty_rfm_services"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RFM-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/rfm-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - RFM-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/rfm-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - RFM-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rfm_api_rfm_web_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - RFM API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - RFM API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/rfm-api-rfm-web-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - RFM API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/rfm-api-rfm-web-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - RFM API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "proposals_api_general_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Proposals API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Proposals API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/proposals-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Proposals API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/proposals-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Proposals API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "proposals_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - PROPOSALS API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - PROPOSALS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/proposals-api-latency-slo_prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - PROPOSALS API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/proposals-api-latency-slo_prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - PROPOSALS API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fleet_manager_etl_core_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - FLEET-MANAGER-CORE-API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - FLEET-MANAGER-CORE-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/fleet-manager-etl-service-prod/serviceLevelObjectives/fleet-manager-core-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - FLEET-MANAGER-CORE-API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/fleet-manager-etl-service-prod/serviceLevelObjectives/fleet-manager-core-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - FLEET-MANAGER-CORE-API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fleet_manager_core_api_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Latency SLO - FLEET MANAGER CORE API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - FLEET MANAGER CORE API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/fleet-manager-etl-service-prod/serviceLevelObjectives/fleet-manager-core-api-latency-slo_prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Latency SLO - FLEET MANAGER CORE API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fleet_manager_core_api_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - FLEET MANAGER CORE API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - FLEET MANAGER CORE API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/fleet-manager-etl-service-prod/serviceLevelObjectives/fleet-manager-core-api-latency-slo_prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Latency SLO - FLEET MANAGER CORE API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "classification_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - CLASSIFICATION"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - CLASSIFICATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/fleet-manager-etl-service-prod/serviceLevelObjectives/classification-api-general-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - CLASSIFICATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/fleet-manager-etl-service-prod/serviceLevelObjectives/classification-api-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - CLASSIFICATION"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "classification_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - CLASSIFICATION"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - CLASSIFICATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/fleet-manager-etl-service-prod/serviceLevelObjectives/classification-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - CLASSIFICATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/fleet-manager-etl-service-prod/serviceLevelObjectives/classification-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - CLASSIFICATION"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rouseappraisals_api_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - RouseAppraisals API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - RouseAppraisals API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/rouseappraisals-api-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow burn rate alert for Availability SLO - RouseAppraisals API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "rouseappraisals_api_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - RouseAppraisals API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - RouseAppraisals API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/rouseappraisals-api-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast burn rate alert for Availability SLO - RouseAppraisals API"
    mime_type = "text/markdown"
  }
}


resource "google_monitoring_alert_policy" "rouseappraisals_api_appraisals_client_latency_fast_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - RouseAppraisals API (Fast Burn)"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"],
  ]

  conditions {
    display_name = "Fast Burn rate on Latency SLO - RouseAppraisals API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/rouseappraisals-api-appraisals-client-latency-prod\", \"7200s\")"
      threshold_value = 10
      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Latency SLO - RouseAppraisals API"
    mime_type = "text/markdown"
  }
}



resource "google_monitoring_alert_policy" "rouseappraisals_api_appraisals_client_latency_slow_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - RouseAppraisals API (Slow Burn)"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - RouseAppraisals API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/rouseappraisals-api-appraisals-client-latency-prod\", \"86400s\")"
      threshold_value = 2
      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Latency SLO - RouseAppraisals API"
    mime_type = "text/markdown"
  }
}


resource "google_monitoring_alert_policy" "appraisals_report_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Appraisals Report"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Appraisals Report"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/appraisals-report-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Appraisals Report"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/appraisals-report-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Appraisals Report"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "appraisals_report_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - Appraisals Report"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - Appraisals Report"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/appraisals-report-latency-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Fast Burn rate alert for Latency SLO - Appraisals Report"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "appraisals_report_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - Appraisals Report"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - Appraisals Report"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/appraisals-report-latency-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Latency SLO - Appraisals Report"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "portal_rouseappraisals_api_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - Portal RouseAppraisals"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Portal RouseAppraisals"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-admin-site-service-prod/serviceLevelObjectives/portal-rouseappraisals-api-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Fast Burn rate alert for Availability SLO - Portal RouseAppraisals"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "portal_rouseappraisals_api_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow burn rate on Availability SLO - Portal RouseAppraisals"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Portal RouseAppraisals"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-admin-site-service-prod/serviceLevelObjectives/portal-rouseappraisals-api-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow burn rate alert for Availability SLO - Portal RouseAppraisals"
    mime_type = "text/markdown"
  }
}


resource "google_monitoring_alert_policy" "portal_rouseappraisals_latency_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - PORTAL RouseAppraisals"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - PORTAL RouseAppraisals"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-admin-site-service-prod/serviceLevelObjectives/portal-rouseappraisals-latency-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Latency SLO - PORTAL RouseAppraisals"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "portal_rouseappraisals_latency_slow_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Latency SLO - PORTAL RouseAppraisals"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - PORTAL RouseAppraisals"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-admin-site-service-prod/serviceLevelObjectives/portal-rouseappraisals-latency-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Latency SLO - PORTAL RouseAppraisals"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "client_rouseappraisals_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - CLIENT RouseAppraisals"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - CLIENT RouseAppraisals"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/client-rouseappraisals-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Availability SLO - CLIENT RouseAppraisals"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "client_rouseappraisals_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - CLIENT RouseAppraisals"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - CLIENT RouseAppraisals"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/appraisals-client-site-service-prod/serviceLevelObjectives/client-rouseappraisals-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Availability SLO - CLIENT RouseAppraisals"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "inspector_assets_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Inspector Assets"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Inspector Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/inspector-app-service-prod/serviceLevelObjectives/inspector-assets-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Inspector Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/inspector-app-service-prod/serviceLevelObjectives/inspector-assets-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Inspector Assets"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "inspector_assets_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - INSPECTOR ASSETS"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - INSPECTOR ASSETS"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/inspector-app-service-prod/serviceLevelObjectives/inspector-assets-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - INSPECTOR ASSETS"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/inspector-app-service-prod/serviceLevelObjectives/inspector-assets-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - INSPECTOR ASSETS"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "ftp01_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - FTP01"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - FTP01"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/ftp-prod/serviceLevelObjectives/ftp01-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - FTP01"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/ftp-prod/serviceLevelObjectives/ftp01-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - FTP01"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "ftp02_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - FTP02"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - FTP02"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/ftp-prod/serviceLevelObjectives/ftp02-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - FTP02"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/ftp-prod/serviceLevelObjectives/ftp02-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - FTP02"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "ssh_ftp02_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - SSH FTP02"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - SSH FTP02"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/ftp-prod/serviceLevelObjectives/ssh-ftp02-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - SSH FTP02"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/ftp-prod/serviceLevelObjectives/ssh-ftp02-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - SSH FTP02"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "ssh_ftp01_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - SSH FTP01"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - SSH FTP01"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/ftp-prod/serviceLevelObjectives/ssh-ftp01-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - SSH FTP01"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/ftp-prod/serviceLevelObjectives/ssh-ftp01-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - SSH FTP01"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "dtt_api_rouseanalytics_general_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - DTT API RouseAnalytics"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - DTT API RouseAnalytics"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/dtt-prod/serviceLevelObjectives/dtt-api-rouseanalytics-general-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - DTT API RouseAnalytics"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "dtt_api_rouseanalytics_general_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - DTT API RouseAnalytics"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - DTT API RouseAnalytics"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/dtt-prod/serviceLevelObjectives/dtt-api-rouseanalytics-general-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - DTT API RouseAnalytics"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "analytics_dtt_api_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - DTT API RouseAnalytics"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - DTT API RouseAnalytics"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/dtt-prod/serviceLevelObjectives/analytics-dtt-api-latency-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - DTT API RouseAnalytics"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "analytics_dtt_api_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Latency SLO - DTT API RouseAnalytics"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - DTT API RouseAnalytics"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/dtt-prod/serviceLevelObjectives/analytics-dtt-api-latency-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - DTT API RouseAnalytics"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "classify_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Classify API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Classify API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/vnext-service-prod/serviceLevelObjectives/classify-api-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Classify API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/vnext-service-prod/serviceLevelObjectives/classify-api-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Classify API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "classify_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - CLASSIFY"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - CLASSIFY"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/vnext-service-prod/serviceLevelObjectives/classify-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - CLASSIFY"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/vnext-service-prod/serviceLevelObjectives/classify-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - CLASSIFY"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "bravo_valuation_service_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Bravo Valuation Service"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Bravo Valuation Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/bravo-valuation-service-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Bravo Valuation Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/bravo-valuation-service-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Bravo Valuation Service"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_bravo_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - VALUATION BRAVO"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - VALUATION BRAVO"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-bravo-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - VALUATION BRAVO"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-bravo-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - VALUATION BRAVO"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "charlie_valuation_service_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Charlie Valuation Service"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Charlie Valuation Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/charlie-valuation-service-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Charlie Valuation Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/charlie-valuation-service-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Charlie Valuation Service"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_charlie_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - VALUATION CHARLIE"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - VALUATION CHARLIE"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-charlie-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - VALUATION CHARLIE"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-charlie-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - VALUATION CHARLIE"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_service_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Valuation Service"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Valuation Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-service-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Valuation Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-service-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Valuation Service"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_service_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - VALUATION SERVICE"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - VALUATION SERVICE"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-service-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - VALUATION SERVICE"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-service-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - VALUATION SERVICE"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fm_valuation_service_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - FM Valuation Service"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - FM Valuation Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/fm-valuation-service-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - FM Valuation Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/fm-valuation-service-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - FM Valuation Service"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_fm_valuation_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - VALUATION FM VALUATION"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - VALUATION FM VALUATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-fm-valuation-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - VALUATION FM VALUATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-fm-valuation-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - VALUATION FM VALUATION"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "legacy_api_v2_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Legacy API v2"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Legacy API v2"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/legacy-api-v2-events-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Legacy API v2"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/legacy-api-v2-events-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Legacy API v2"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_legacy_api_v2_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - VALUATION LEGACY API V2"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - VALUATION LEGACY API V2"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-legacy-api-v2-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - VALUATION LEGACY API V2"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-legacy-api-v2-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - VALUATION LEGACY API V2"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_algo_service_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Valuation Algo Service"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Valuation Algo Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-algo-service-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Valuation Algo Service"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-algo-service-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Valuation Algo Service"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_algo_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - VALUATION ALGO"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - VALUATION ALGO"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-algo-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - VALUATION ALGO"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-algo-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - VALUATION ALGO"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_values_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Valuation Values"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Valuation Values"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-values-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Valuation Values"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-values-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Valuation Values"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "valuation_values_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - VALUATION VALUES"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - VALUATION VALUES"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-values-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - VALUATION VALUES"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/valuations-service-service-prod/serviceLevelObjectives/valuation-values-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - VALUATION VALUES"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "identity_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Identity"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Identity"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/identity-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Identity"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/identity-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Identity"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "identity_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - IDENTITY"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - IDENTITY"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/identity-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - IDENTITY"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/identity-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - IDENTITY"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "authorization_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Authorization"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Authorization"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authorization-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Authorization"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authorization-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Authorization"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "authorization_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - AUTHORIZATION"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - AUTHORIZATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authorization-latency-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - AUTHORIZATION"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "authorization_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - AUTHORIZATION"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - AUTHORIZATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authorization-latency-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - AUTHORIZATION"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "authentication_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - Authentication"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Authentication"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authentication-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Burn rate alert for Availability SLO - Authentication"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "authentication_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - Authentication"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Authentication"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authentication-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Authentication"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "authentication_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - AUTHENTICATION"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - AUTHENTICATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authentication-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - AUTHENTICATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authentication-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - AUTHENTICATION"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "authorauthentication_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Author Authentication"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Author Authentication"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authorauthentication-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Author Authentication"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authorauthentication-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Author Authentication"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "authorauthentication_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - AUTHOR AUTHENTICATION"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - AUTHOR AUTHENTICATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authorauthentication-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - AUTHOR AUTHENTICATION"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/authorauthentication-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - AUTHOR AUTHENTICATION"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "auth_assets_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - Auth Assets"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Auth Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/auth-assets-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Auth Assets"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/auth-assets-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Auth Assets"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "auth_assets_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - AUTH ASSETS"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - AUTH ASSETS"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/auth-assets-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - AUTH ASSETS"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/auth-assets-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - AUTH ASSETS"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "messaging_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - Messaging"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - Messaging"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/messaging-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Burn rate alert for Availability SLO - Messaging"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "messaging_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - Messaging"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - Messaging"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/messaging-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - Messaging"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "messaging_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - MESSAGING"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - MESSAGING"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/messaging-latency-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - MESSAGING"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "messaging_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Latency SLO - MESSAGING"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - MESSAGING"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/messaging-latency-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - MESSAGING"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "scim_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - SCIM"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - SCIM"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/scim-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - SCIM"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "scim_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - SCIM"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - SCIM"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/scim-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - SCIM"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/scim-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - SCIM"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "scim_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - SCIM"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    local.notification_channels["pagerduty"]
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - SCIM"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/scim-latency-slo-prod\", \"7200s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }


  documentation {
    content   = "Burn rate alert for Latency SLO - SCIM"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "scim_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Latency SLO - SCIM"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - SCIM"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/identity-service-prod/serviceLevelObjectives/scim-latency-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - SCIM"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "user_management_api_availability_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Availability SLO - User Management API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - User Management API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/user-management-prod/serviceLevelObjectives/user-management-api-availability-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Availability SLO - User Management API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/user-management-prod/serviceLevelObjectives/user-management-api-availability-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Availability SLO - User Management API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "user_management_api_latency_slo_prod_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Burn rate on Latency SLO - User Management API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - User Management API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/user-management-prod/serviceLevelObjectives/user-management-api-latency-slo-prod\", \"3600s\")"
      threshold_value = 15

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Slow Burn rate on Latency SLO - User Management API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/user-management-prod/serviceLevelObjectives/user-management-api-latency-slo-prod\", \"86400s\")"
      threshold_value = 5

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Burn rate alert for Latency SLO - User Management API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fleet_manager_export_api_availability_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Availability SLO - FLEET MANAGER EXPORT API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Availability SLO - FLEET MANAGER EXPORT API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-fmx-api-availability-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Availability SLO - FLEET MANAGER EXPORT API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fleet_manager_export_api_availability_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Availability SLO - FLEET MANAGER EXPORT API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    # local.notification_channels["pagerduty"] Commented out until SLO is stable
  ]
  conditions {
    display_name = "Fast Burn rate on Availability SLO - FLEET MANAGER EXPORT API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-fmx-api-availability-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Availability SLO - FLEET MANAGER EXPORT API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fleet_manager_export_api_latency_slo_prod_slow_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Slow Burn rate on Latency SLO - FLEET MANAGER EXPORT API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
  ]

  conditions {
    display_name = "Slow Burn rate on Latency SLO - FLEET MANAGER EXPORT API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-fmx-api-latency-slo-prod\", \"86400s\")"
      threshold_value = 2

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Slow Burn rate alert for Latency SLO - FLEET MANAGER EXPORT API"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "fleet_manager_export_api_latency_slo_prod_fast_burn_rate_alert" {
  combiner     = "OR"
  display_name = "Fast Burn rate on Latency SLO - FLEET MANAGER EXPORT API"
  notification_channels = [
    local.notification_channels["slack-slos-alerts-prod"],
    # local.notification_channels["pagerduty"] Commented out until SLO is stable
  ]
  conditions {
    display_name = "Fast Burn rate on Latency SLO - FLEET MANAGER EXPORT API"
    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "900s"
      filter          = "select_slo_burn_rate(\"projects/313592812803/services/rfm-service-prod/serviceLevelObjectives/sales-fmx-api-latency-slo-prod\", \"7200s\")"
      threshold_value = 10

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  documentation {
    content   = "Fast Burn rate alert for Latency SLO - FLEET MANAGER EXPORT API"
    mime_type = "text/markdown"
  }
}

