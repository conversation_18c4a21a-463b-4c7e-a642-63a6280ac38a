locals {
  alloydb_rfm1001_cluster_id              = "rfm1001-prod"
  alloydb_rfm1001_rp0_instance_id         = "rfm1001rp0-prod"
  alloydb_rfm1001_rp1_instance_id         = "rfm1001rp1-prod"
  replication_lag_threshold_milliseconds  = 59000
  replication_lag_duration_seconds        = "60s" # "for more than 1 minute"
}

resource "google_monitoring_alert_policy" "alloydb_rfm1001_rp0_replication_lag" {
  project      = local.project
  display_name = "AlloyDB RFM1001 RP0 - High Replication Lag"
  combiner     = "OR"
  conditions {
    display_name = "Replication Lag > ${local.replication_lag_threshold_milliseconds}ms for 1 min on ${local.alloydb_rfm1001_rp0_instance_id}"
    condition_threshold {
      filter = <<-EOT
        metric.type="alloydb.googleapis.com/node/postgres/replay_lag"
        resource.type="alloydb.googleapis.com/InstanceNode"
        resource.labels.cluster_id="${local.alloydb_rfm1001_cluster_id}"
        resource.labels.instance_id="${local.alloydb_rfm1001_rp0_instance_id}"
      EOT
      comparison      = "COMPARISON_GT"
      threshold_value = local.replication_lag_threshold_milliseconds
      duration        = local.replication_lag_duration_seconds # Must be greater than 59s for 60 seconds
      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MAX" # Use ALIGN_MAX for be sensitive at the peak of lag in the minute
      }
    }
  }

  alert_strategy {
    auto_close = "1800s" # Close incidents after 30 minutes if the condition is resolved
  }

  documentation {
    content = <<-EOT
        **Replication Lag Alert for AlloyDB RFM1001 Read Pool RP0**

        The read pool instance '${local.alloydb_rfm1001_rp0_instance_id}' of cluster '${local.alloydb_rfm1001_cluster_id}' is experiencing a replication lag greater than ${local.replication_lag_threshold_milliseconds} milliseconds for more than 1 minute.

        **Suggested Steps:**
        1. Check the load on the primary instance of the RFM1001 cluster.
        2. Review the metrics for the RP0 instance in Google Cloud Monitoring (CPU, IOPS, Connections).
        3. Check AlloyDB logs for errors or warnings.
        4. Consider if there are long DDLs or blocking transactions on the primary.
        5. Playbook: [Link to your AlloyDB lag troubleshooting playbook]
    EOT
    mime_type = "text/markdown"
  }

  notification_channels = [
    local.notification_channels["slack_rfm-alerts"],
    local.notification_channels["pagerduty_rfm_slow_query"],
  ]

  user_labels = {
    "service"  = "alloydb",
    "cluster"  = "rfm1001",
    "readpool" = "rp0",
    "severity" = "critical"
  }
}

resource "google_monitoring_alert_policy" "alloydb_rfm1001_rp1_replication_lag" {
  project      = local.project
  display_name = "AlloyDB RFM1001 RP1 - High Replication Lag"
  combiner     = "OR"
  conditions {
    display_name = "Replication Lag > ${local.replication_lag_threshold_milliseconds}ms for 1 min on ${local.alloydb_rfm1001_rp1_instance_id}"
    condition_threshold {
      filter = <<-EOT
        metric.type="alloydb.googleapis.com/node/postgres/replay_lag"
        resource.type="alloydb.googleapis.com/InstanceNode"
        resource.labels.cluster_id="${local.alloydb_rfm1001_cluster_id}"
        resource.labels.instance_id="${local.alloydb_rfm1001_rp1_instance_id}"
      EOT
      comparison      = "COMPARISON_GT"
      threshold_value = local.replication_lag_threshold_milliseconds
      duration        = local.replication_lag_duration_seconds # Must be greater than 59s for 60 seconds
      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MAX" # Use ALIGN_MAX for be sensitive at the peak of lag in the minute
      }
    }
  }

  alert_strategy {
    auto_close = "1800s" # Close incidents after 30 minutes if the condition is resolved
  }

  documentation {
    content = <<-EOT
      **Replication Lag Alert for AlloyDB RFM1001 Read Pool RP1**

      The read pool instance '${local.alloydb_rfm1001_rp1_instance_id}' of cluster '${local.alloydb_rfm1001_cluster_id}' is experiencing a replication lag greater than ${local.replication_lag_threshold_milliseconds} milliseconds for more than 1 minute.

      **Suggested Steps:**
      1. Check the load on the primary instance of the RFM1001 cluster.
      2. Review the metrics for the RP1 instance in Google Cloud Monitoring (CPU, IOPS, Connections).
      3. Check AlloyDB logs for errors or warnings.
      4. Consider if there are long DDLs or blocking transactions on the primary.
      5. Playbook: [Link to your AlloyDB lag troubleshooting playbook]
    EOT
    mime_type = "text/markdown"
  }

  notification_channels = [
    local.notification_channels["slack_rfm-alerts"],
    local.notification_channels["pagerduty_rfm_slow_query"],
  ]

  user_labels = {
    "service"  = "alloydb",
    "cluster"  = "rfm1001",
    "readpool" = "rp1",
    "severity" = "critical"
  }
}
