variable "name" {}
variable "environment" {}
variable "project" {}
variable "connect_mode" {
  default = null
}
variable "tier" {
  default = "BASIC"
}
variable "memory_size_gb" {
  default = 1
}
variable "zone" {
  default = "us-central1-b"
}
variable "network_link" {}
variable "redis_version" {
  default = "REDIS_3_2"
}
variable "auto_ip_range" {
  default = false
}
variable "base_ip_range" {}
variable "ip_range_number" {}
variable "subrange_bits" {
  default = 13
}
variable "role" {
  default = "cache"
}
variable "division" {}

variable "auth_enabled" {
  default = false
}

locals {
  ip_range      = var.auto_ip_range ? null : cidrsubnet(var.base_ip_range, var.subrange_bits, var.ip_range_number)
  instance_name = "${var.name}-${var.environment}"
}

resource "google_redis_instance" "module" {
  name           = local.instance_name
  tier           = var.tier
  project        = var.project
  memory_size_gb = var.memory_size_gb

  connect_mode       = var.connect_mode
  location_id        = var.zone
  authorized_network = var.network_link
  redis_version      = var.redis_version
  display_name       = "Cache instance ${var.name}"
  reserved_ip_range  = local.ip_range
  auth_enabled       = var.auth_enabled

  labels = {
    instance_name = local.instance_name
    environment   = var.environment
    role          = var.role
    division      = var.division
  }

  timeouts {
    update = "60m"
  }
}

resource "google_secret_manager_secret" "module" {
  for_each = toset(
    var.auth_enabled ? [var.name] : []
  )
  project   = var.project
  secret_id = "${var.name}-auth-string"

  replication {
    auto {}
  }

  labels = {
    environment = var.environment
    role        = var.role
    division    = var.division
  }
}

resource "google_secret_manager_secret_version" "module" {
  for_each = toset(
    var.auth_enabled ? [var.name] : []
  )

  secret      = google_secret_manager_secret.module[var.name].id
  secret_data = google_redis_instance.module.auth_string
  depends_on  = [
    google_secret_manager_secret.module,
    google_redis_instance.module
  ]
}

output "host" {
  value = google_redis_instance.module.host
}

output "port" {
  value = google_redis_instance.module.port
}

output "secret_id" {
  value = var.auth_enabled ? google_secret_manager_secret.module[var.name].secret_id : ""
}
