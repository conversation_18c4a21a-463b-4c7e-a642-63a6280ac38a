variable "name" {}
variable "environment" {}
variable "folder_name" {}
variable "location" {
  default = "us-central1"
}
variable "initial_node_count" {
  default = 0
}
variable "division" {}
variable "role" {}
variable "project" {}
variable "min_node_count" {
  default = 0
}
variable "max_node_count" {
  default = 100
}
variable "network_link" {}
variable "subnet_link" {}
variable "cluster_secondary_range_name" {
  default = "default-pods"
}
variable "services_secondary_range_name" {
  default = "default-services"
}
variable "service_account_roles" {
  default = []
}
variable "service_account" {
  default = ""
}
variable "oauth_scopes" {
  default = [
    "https://www.googleapis.com/auth/logging.write",
    "https://www.googleapis.com/auth/monitoring",
    "https://www.googleapis.com/auth/cloud-platform",
    "https://www.googleapis.com/auth/devstorage.read_write",
  ]
}
variable "taints" {
  default = []
}
variable "vertical_pod_autoscaling" {
  default = false
}
variable "horizontal_pod_autoscaling" {
  default = true
}
variable "enable_http_load_balancing" {
  default = true
}
variable "deletion_protection" {
  default = true
}
variable "min_master_version" {
  default = null
}
variable "autopilot" {
  default = true
}

variable "enable_private_nodes" {
  default = false
}

variable "enable_private_endpoint" {
  default = false
}

variable "enable_master_global_access_config" {
  default = false
}
variable "whitelisted_cidrs" {
  default = []
}

variable "maintenance_policy" {
  description = "Maintenance policy configuration for the cluster"
  type = object({
    start_time = string
    end_time   = string
    recurrence = string
  })
  default = {
    start_time = "1970-01-01T07:00:00Z"
    end_time   = "1970-01-02T04:00:00Z"
    recurrence = "FREQ=WEEKLY;BYDAY=SU"
  }
}
locals {
  cluster_name = "${var.name}-${var.environment}"
  minimum_roles = [
    "roles/monitoring.viewer",
    "roles/monitoring.metricWriter",
    "roles/logging.logWriter",
    "roles/storage.objectViewer",
  ]
  service_account = var.service_account == "" ? google_service_account.module[0].email : var.service_account
  account_roles   = local.service_account != "default" ? toset(concat(local.minimum_roles, var.service_account_roles)) : toset([])
}

resource "google_service_account" "module" {
  provider     = google-beta
  count        = var.service_account == "" ? 1 : 0
  account_id   = local.cluster_name
  display_name = "GKE Cluster ${local.cluster_name}"
}

resource "google_project_iam_member" "module" {
  provider = google-beta
  for_each = local.account_roles
  role     = each.key
  member   = "serviceAccount:${local.service_account}"
  project  = var.project
}

resource "google_bigquery_dataset" "metering" {
  dataset_id    = replace("${local.cluster_name}-metering", "-", "_")
  friendly_name = ""
  description   = "This is a test description"
  location      = "US"

  labels = {
    name        = local.cluster_name
    environment = var.environment
    division    = var.division
    role        = var.role
  }
}

resource "google_container_cluster" "module" {
  provider            = google-beta
  name                = local.cluster_name
  location            = var.location
  initial_node_count  = 1
  network             = var.network_link
  subnetwork          = var.subnet_link
  deletion_protection = var.deletion_protection
  project             = var.project
  monitoring_service  = "monitoring.googleapis.com/kubernetes"
  logging_service     = "logging.googleapis.com/kubernetes"
  enable_autopilot    = var.autopilot

  cluster_autoscaling {
    auto_provisioning_defaults {
      service_account = local.service_account
      oauth_scopes    = var.oauth_scopes
    }
  }

  private_cluster_config {
    enable_private_nodes    = var.enable_private_nodes
    enable_private_endpoint = var.enable_private_endpoint
    dynamic "master_global_access_config" {
      for_each = var.enable_master_global_access_config == null ? [] : [var.enable_master_global_access_config]
      content {
        enabled = master_global_access_config.value
      }
    }
  }
  master_authorized_networks_config {
    gcp_public_cidrs_access_enabled = var.enable_private_nodes == true ? false : true
    dynamic "cidr_blocks" {
      for_each = var.whitelisted_cidrs
      content {
        cidr_block = cidr_blocks.value
      }
    }
  }

  master_auth {
    client_certificate_config {
      issue_client_certificate = false
    }
  }
  resource_labels = {
    name        = local.cluster_name
    environment = var.environment
    division    = var.division
    role        = var.role
  }
  ip_allocation_policy {
    cluster_secondary_range_name  = var.cluster_secondary_range_name
    services_secondary_range_name = var.services_secondary_range_name
  }
  maintenance_policy {
    recurring_window {
      start_time = var.maintenance_policy.start_time
      end_time   = var.maintenance_policy.end_time
      recurrence = var.maintenance_policy.recurrence
    }
  }
  dynamic "vertical_pod_autoscaling" {
    for_each = var.vertical_pod_autoscaling ? [true] : []
    content {
      enabled = vertical_pod_autoscaling.value
    }
  }
  addons_config {
    horizontal_pod_autoscaling {
      disabled = var.horizontal_pod_autoscaling ? false : true
    }
    http_load_balancing {
      disabled = var.enable_http_load_balancing ? false : true
    }
  }
  timeouts {
    update = "60m"
  }
}

locals {
  project_parts      = split("-", var.project)
  project_name       = join("-", slice(local.project_parts, 0, length(local.project_parts) - 1))
  gke_project_folder = "${path.module}/../../../../../gke/${var.folder_name}/${local.project_name}/${google_container_cluster.module.name}"
  datasource_path    = "${path.module}/../../../../../gcp/global/gke-clusters/${local.project_name}-${google_container_cluster.module.name}.tf"
}

resource "local_file" "datasource" {
  content = templatefile("${path.module}/datasource.tpl", {
    cluster_name = google_container_cluster.module.name
    project      = var.project
    location     = google_container_cluster.module.location
    folder_name  = var.folder_name
    project_name = local.project_name
  })
  filename = local.datasource_path
  provisioner "local-exec" {
    command = "chmod 644 ${local.datasource_path}"
  }
}

output "gke_cluster" {
  value = google_container_cluster.module
}

output "service_account_email" {
  value = local.service_account
}
