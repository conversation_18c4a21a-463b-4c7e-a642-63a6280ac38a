variable "name" {}
variable "environment" {}
variable "folder_name" {}
variable "location" {
  default = "us-central1-b"
}
variable "initial_node_count" {
  default = 0
}
variable "primary_pool_initial_node_count" {
  default = 0
}
variable "machine_type" {
  default = "n1-standard-1"
}
variable "default_pool_machine_type" {
  default = "g1-small"
}
variable "machine_platform" {
  default = "general-purpose"
}
variable "division" {}
variable "role" {}
variable "project" {}
variable "min_node_count" {
  default = 0
}
variable "max_node_count" {
  default = 10
}
variable "network_link" {}
variable "subnet_link" {}
variable "cluster_secondary_range_name" {
  default = "default-pods"
}
variable "services_secondary_range_name" {
  default = "default-services"
}
variable "service_account_roles" {
  default = []
}
variable "disk_size_gb" {
  default = 50
}
variable "disk_type" {
  default = "pd-ssd"
}
variable "service_account" {
  default = ""
}
variable "enable_l4_ilb_subsetting" {
  default = false
}
variable "oauth_scopes" {
  default = [
    "https://www.googleapis.com/auth/logging.write",
    "https://www.googleapis.com/auth/monitoring",
    "https://www.googleapis.com/auth/cloud-platform",
    "https://www.googleapis.com/auth/devstorage.read_write",
  ]
}
variable "taints" {
  default = []
}
variable "enable_system_containers_pool" {
  default = true
}
variable "enable_spot_pool" {
  default = false
}
variable "enable_primary_pool" {
  default = true
}
variable "managed_prometheus_config" {
  default = false
}
variable "cluster_autoscaling" {
  default = true
}
variable "vertical_pod_autoscaling" {
  default = false
}
variable "horizontal_pod_autoscaling" {
  default = true
}
variable "enable_istio" {
  default = false
}
variable "istio_auth" {
  default = "AUTH_NONE"
}
variable "enable_cloudrun" {
  default = false
}
variable "enable_http_load_balancing" {
  default = true
}
variable "min_master_version" {
  default = null
}
variable "workload_identity_enabled" {
  default = false
}
variable "workload_identity_pools_enabled" {
  default = false
}
variable "release_channel" {
  default = "UNSPECIFIED"
}
variable "enable_shielded_nodes" {
  default = false
}

variable "initial_default_node_count"{
  default = 2
}

variable "default_auto_upgrade" {
  default = false
}

variable "default_auto_repair" {
  default = true
}

variable "notification_config" {
  default = false
}

variable "notification_pubsub" {
  default = ""
}

variable "enable_filestore_addon" {
  default = false
}

variable "default_node_pool_min_count" {
  default = 1  
}

variable "default_node_pool_autoscaling" {
  default = true
}

variable "enable_dns_cache" {
  default = false
}

variable "maintenance_policy" {
  description = "Maintenance policy configuration for the cluster"
  type = object({
    start_time = string
    end_time   = string
    recurrence = string
  })
  default = {
    start_time = "2019-01-01T01:00:00Z"
    end_time   = "2019-01-01T07:00:00Z"
    recurrence = "FREQ=WEEKLY;BYDAY=SU,MO"
  }
}

# Backward Compatibility
variable "is_legacy" {
  description = "Whether to use legacy monitoring and logging configuration"
  type        = bool
  default     = true
}

locals {
  cluster_name = "${var.name}-${var.environment}"
  minimum_roles = [
    "roles/monitoring.viewer",
    "roles/monitoring.metricWriter",
    "roles/logging.logWriter",
    "roles/storage.objectViewer",
  ]
  service_account = var.service_account == "" ? google_service_account.module[0].email : var.service_account
}

resource "google_service_account" "module" {
  provider     = google-beta
  count        = var.service_account == "" ? 1 : 0
  account_id   = local.cluster_name
  display_name = "GKE Cluster ${local.cluster_name}"
}

resource "google_project_iam_member" "module" {
  provider = google-beta
  for_each = toset(concat(local.minimum_roles, var.service_account_roles))
  role     = each.key
  member   = "serviceAccount:${local.service_account}"
  project  = var.project
}

resource "google_bigquery_dataset" "metering" {
  dataset_id    = replace("${local.cluster_name}-metering", "-", "_")
  friendly_name = ""
  description   = "This is a test description"
  location      = "US"

  labels = {
    name        = local.cluster_name
    environment = var.environment
    division    = var.division
    role        = var.role
  }
}

# TODO: work on the legacy options to ensure backward compatibility
resource "google_container_cluster" "module" {
  provider                 = google-beta
  name                     = local.cluster_name
  location                 = var.location
  min_master_version       = var.min_master_version
  remove_default_node_pool = true
  initial_node_count       = 1
  network                  = var.network_link
  subnetwork               = var.subnet_link
  project                  = var.project
  monitoring_service       = var.is_legacy ? "monitoring.googleapis.com/kubernetes" : null
  logging_service          = var.is_legacy ? "logging.googleapis.com/kubernetes" : null
  enable_shielded_nodes    = var.enable_shielded_nodes
  enable_l4_ilb_subsetting = var.enable_l4_ilb_subsetting

  notification_config {
    pubsub {
      enabled = var.notification_config
      topic = var.notification_pubsub
    }
  }

  # Only include logging_config if not using legacy configuration
  dynamic "logging_config" {
    for_each = var.is_legacy ? [] : [1]
    content {
      enable_components = ["SYSTEM_COMPONENTS", "WORKLOADS"]
    }
  }

  # Only include monitoring_config if not using legacy configuration
  dynamic "monitoring_config" {
    for_each = var.is_legacy ? [] : [1]
    content {
      enable_components = ["SYSTEM_COMPONENTS"]
      managed_prometheus {
        enabled = var.managed_prometheus_config
      }
    }
  }

  maintenance_policy {
    recurring_window {
      start_time = var.maintenance_policy.start_time
      end_time   = var.maintenance_policy.end_time
      recurrence = var.maintenance_policy.recurrence
    }
  }

  master_auth {
    client_certificate_config {
      issue_client_certificate = false
    }
  }
  resource_labels = {
    name        = local.cluster_name
    environment = var.environment
    division    = var.division
    role        = var.role
  }
  resource_usage_export_config {
    enable_network_egress_metering       = true
    enable_resource_consumption_metering = true

    bigquery_destination {
      dataset_id = google_bigquery_dataset.metering.dataset_id
    }
  }
  cluster_autoscaling {
    enabled = var.cluster_autoscaling
    dynamic "resource_limits" {
      for_each = var.cluster_autoscaling ? {
        cpu    = 64
        memory = 128
      } : {}
      content {
        resource_type = resource_limits.key
        maximum       = resource_limits.value
      }
    }
  }
  ip_allocation_policy {
    cluster_secondary_range_name  = var.cluster_secondary_range_name
    services_secondary_range_name = var.services_secondary_range_name
  }
  release_channel {
    channel = var.release_channel
  }
  dynamic "vertical_pod_autoscaling" {
    for_each = var.vertical_pod_autoscaling ? [true] : []
    content {
      enabled = vertical_pod_autoscaling.value
    }
  }
  dynamic "workload_identity_config" {
    for_each = var.workload_identity_enabled ? [true] : []
    content {
      workload_pool = "${var.project}.svc.id.goog"
    }
  }
  addons_config {
    horizontal_pod_autoscaling {
      disabled = var.horizontal_pod_autoscaling ? false : true
    }
    http_load_balancing {
      disabled = var.enable_http_load_balancing ? false : true
    }
    gcp_filestore_csi_driver_config {
      enabled = var.enable_filestore_addon
    }
    dynamic "istio_config" {
      for_each = var.enable_istio ? [false] : []
      content {
        disabled = istio_config.value
        auth     = var.istio_auth
      }
    }
    dynamic "cloudrun_config" {
      for_each = var.enable_cloudrun ? [false] : []
      content {
        disabled = cloudrun_config.value
      }
    }
    dynamic "dns_cache_config" {
      for_each = var.enable_dns_cache ? [1] : []
      content {
        enabled = var.enable_dns_cache
      }
    }
  }
  timeouts {
    update = "60m"
  }
}

resource "google_container_node_pool" "default" {
  provider           = google-beta
  count              = var.enable_system_containers_pool ? 1 : 0
  name               = "default"
  location           = var.location
  cluster            = google_container_cluster.module.name
  initial_node_count = var.initial_default_node_count
  project            = var.project
  management {
    auto_repair  = var.default_auto_repair
    auto_upgrade = var.default_auto_upgrade
  }
  dynamic "autoscaling" {
    for_each = var.default_node_pool_autoscaling == false ? [] : [var.default_node_pool_autoscaling]
      content {
          min_node_count = var.default_node_pool_min_count
          max_node_count = var.max_node_count
      }
  }
  node_config {
    machine_type = var.default_pool_machine_type
    disk_size_gb = var.disk_size_gb
    metadata = {
      disable-legacy-endpoints = true
    }
    labels = {
      machine-type     = var.default_pool_machine_type
      machine-platform = "shared-core"
      machine-pricing  = "on-demand"
    }
    service_account = local.service_account
    oauth_scopes    = var.oauth_scopes
  }
  timeouts {
    update = "60m"
  }
  lifecycle {
    # prevent_destroy = true
  }
}

resource "google_container_node_pool" "primary_node_pool" {
  provider           = google-beta
  count              = var.enable_primary_pool ? 1 : 0
  name               = "primary"
  location           = var.location
  cluster            = google_container_cluster.module.name
  initial_node_count = var.primary_pool_initial_node_count
  project            = var.project
  autoscaling {
    min_node_count = var.min_node_count
    max_node_count = var.max_node_count
  }
  management {
    auto_repair  = true
    auto_upgrade = true
  }
  node_config {
    machine_type = var.machine_type
    disk_size_gb = var.disk_size_gb
    disk_type    = var.disk_type
    metadata = {
      disable-legacy-endpoints = true
    }
    labels = {
      machine-type     = var.machine_type
      machine-platform = var.machine_platform
      machine-pricing  = "on-demand"
    }
    service_account = local.service_account
    oauth_scopes    = var.oauth_scopes
    dynamic "workload_metadata_config" {
      for_each = var.workload_identity_pools_enabled ? [true] : []
      content {
        mode = "GKE_METADATA"
      }
    }
    dynamic "taint" {
      for_each = var.taints
      content {
        key    = taint.value["key"]
        value  = taint.value["value"]
        effect = taint.value["effect"]
      }
    }
  }
  timeouts {
    update = "60m"
  }
  lifecycle {
    # prevent_destroy = true
  }
}

resource "google_container_node_pool" "spot_node_pool" {
  provider           = google-beta
  count              = var.enable_spot_pool ? 1 : 0
  name               = "spot"
  location           = var.location
  cluster            = google_container_cluster.module.name
  initial_node_count = var.initial_node_count
  project            = var.project
  autoscaling {
    min_node_count = var.min_node_count
    max_node_count = var.max_node_count
  }
  management {
    auto_repair  = true
    auto_upgrade = true
  }
  node_config {
    spot  = true
    machine_type = var.machine_type
    disk_size_gb = var.disk_size_gb
    disk_type    = var.disk_type
    metadata = {
      disable-legacy-endpoints = true
    }
    labels = {
      machine-type     = var.machine_type
      machine-platform = var.machine_platform
      machine-pricing  = "spot"
    }
    service_account = local.service_account
    oauth_scopes    = var.oauth_scopes
    dynamic "workload_metadata_config" {
      for_each = var.workload_identity_pools_enabled ? [true] : []
      content {
        mode = "GKE_METADATA"
      }
    }
    dynamic "taint" {
      for_each = var.taints
      content {
        key    = taint.value["key"]
        value  = taint.value["value"]
        effect = taint.value["effect"]
      }
    }
  }
  timeouts {
    update = "60m"
  }
  lifecycle {
    # prevent_destroy = true
  }
}

locals {
  project_parts      = split("-", var.project)
  project_name       = join("-", slice(local.project_parts, 0, length(local.project_parts) - 1))
  gke_project_folder = "${path.module}/../../../../../gke/${var.folder_name}/${local.project_name}/${google_container_cluster.module.name}"
  datasource_path    = "${path.module}/../../../../../gcp/global/gke-clusters/${google_container_cluster.module.name}.tf"
}

resource "local_file" "datasource" {
  content = templatefile("${path.module}/datasource.tpl", {
    cluster_name = google_container_cluster.module.name
    project      = var.project
    location     = google_container_cluster.module.location
    folder_name  = var.folder_name
    project_name = local.project_name
  })
  filename = local.datasource_path
  provisioner "local-exec" {
    command = "chmod 644 ${local.datasource_path}"
  }
}

output "gke_cluster" {
  value = google_container_cluster.module
}

output "service_account_email" {
  value = local.service_account
}

output "service_account_id" {
  value = google_service_account.module[0].id
}
