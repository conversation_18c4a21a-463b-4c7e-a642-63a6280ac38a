import logging
import os
from distutils.util import strtobool

import requests
import sentry_sdk
from opentelemetry import trace
from opentelemetry.context import _SUPPRESS_INSTRUMENTATION_KEY, attach, detach, set_value
from opentelemetry.context.context import Context
from opentelemetry.exporter.cloud_trace import CloudTraceSpanExporter
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import ReadableSpan, TracerProvider
from opentelemetry.sdk.trace.export import _BSP_RESET_ONCE, BatchSpanProcessor, ConsoleSpanExporter, SpanExporter
from opentelemetry.semconv.trace import SpanAttributes
from opentelemetry.trace import Status, StatusCode
from opentelemetry.trace.span import Span
from opentelemetry.util import types

DEBUG = strtobool(os.environ.get("DEBUG", default="False"))
RUNNING_TESTS = strtobool(os.environ.get("RUNNING_TESTS", default="False"))
ENVIRONMENT_NAME = os.environ.get("ENVIRONMENT_NAME", default="local")

HONEYCOMB_ENDPOINT = os.environ.get("HONEYCOMB_ENDPOINT", default="https://api.honeycomb.io/v1/traces")
HONEYCOMB_API_KEY = os.environ.get("HONEYCOMB_API_KEY", default="")
HONEYCOMB_DATASET = os.environ.get("HONEYCOMB_DATASET", default="")
# honeycomb sample rate is integer, 10 means sample 1 of every 10 events, 1 means sample everything
HONEYCOMB_SAMPLE_RATE = os.environ.get("HONEYCOMB_SAMPLE_RATE", default="20")
HONEYCOMB_SERVICE = os.environ.get("HONEYCOMB_SERVICE", "")
GCPROFILER_SERVICE_NAME = os.environ.get("GCPROFILER_SERVICE_NAME", default="default")
ENABLE_GOOGLE_CLOUD_PROFILER = strtobool(os.environ.get("ENABLE_GOOGLE_CLOUD_PROFILER", default="False"))
ENABLE_GOOGLE_CLOUD_DEBUGGER = strtobool(os.environ.get("ENABLE_GOOGLE_CLOUD_DEBUGGER", default="False"))
JAEGER_TRACING_ENDPOINT = os.environ.get("JAEGER_TRACING_ENDPOINT", default=None)

GCPROFILER_LOG_LEVEL = 3 if DEBUG else 2

SENTRY_INTEGRATION_DSN = os.environ.get("SENTRY_INTEGRATION_DSN", default="")
SENTRY_ERROR_SAMPLE_RATE = os.environ.get("SENTRY_ERROR_SAMPLE_RATE", default="0.2")
SENTRY_TRACES_SAMPLE_RATE = os.environ.get("SENTRY_TRACES_SAMPLE_RATE", default="0.2")

SENTRY_ALL_DATA = strtobool(os.environ.get("SENTRY_ALL_DATA", default="False"))
SENTRY_DEBUG = strtobool(os.environ.get("SENTRY_DEBUG", default="False"))
SENTRY_QUIET_EXIT = strtobool(os.environ.get("SENTRY_QUIET_EXIT", default="False"))

logger = logging.getLogger()


def django_request_hook(span: Span, request):
    span.update_name(request.path)
    span.set_attribute("http.query", request.GET.urlencode())
    if request.body:
        span.set_attribute("http.body", request.body.decode("utf-8", errors="ignore"))


class CustomBatchSpanProcessor(BatchSpanProcessor):
    sql_commands = ["SELECT", "INSERT", "UPDATE", "DELETE", "CREATE", "ALTER", "DROP"]

    def __init__(
        self,
        service_name: str,
        span_exporter: SpanExporter,
        max_queue_size: int = None,
        schedule_delay_millis: float = None,
        max_export_batch_size: int = None,
        export_timeout_millis: float = None,
    ):
        self.service_name = service_name
        super().__init__(
            span_exporter,
            max_queue_size,
            schedule_delay_millis,
            max_export_batch_size,
            export_timeout_millis,
        )

    def on_end(self, span: ReadableSpan) -> None:
        # Automatically add the service name to the span
        span._attributes["service_name"] = self.service_name
        # any(word in x for x in lst)
        if "db.statement" in span.attributes and any(
            command in span.attributes["db.statement"] for command in self.sql_commands
        ):
            span_id = "{span:016x}".format(span=span.context.span_id)
            logger.info(span.attributes["db.statement"], extra={"span_id": span_id})
        if self.done:
            logger.warning("Already shutdown, dropping span.")
            return
        if not span.context.trace_flags.sampled:
            return
        if self._pid != os.getpid():
            _BSP_RESET_ONCE.do_once(self._at_fork_reinit)

        if len(self.queue) == self.max_queue_size and not self._spans_dropped:
            logger.warning("Queue is full, likely spans will be dropped.")
            self._spans_dropped = True

        self.queue.appendleft(span)

        if len(self.queue) >= self.max_export_batch_size:
            with self.condition:
                self.condition.notify()

    def _export_batch(self) -> int:
        """Exports at most max_export_batch_size spans and returns the number of
        exported spans.
        """
        idx = 0
        # currently only a single thread acts as consumer, so queue.pop() will
        # not raise an exception
        while idx < self.max_export_batch_size and self.queue:
            self.spans_list[idx] = self.queue.pop()
            idx += 1
        token = attach(set_value(_SUPPRESS_INSTRUMENTATION_KEY, True))
        try:
            # Ignore type b/c the Optional[None]+slicing is too "clever"
            # for mypy
            self.span_exporter.export(self.spans_list[:idx])  # type: ignore
        except (ConnectionError, requests.exceptions.ConnectionError):
            logger.warning("Exception while exporting Span batch.")
        except Exception:  # pylint: disable=broad-except
            logger.exception("Exception while exporting Span batch.")
        detach(token)

        # clean up list
        for index in range(idx):
            self.spans_list[index] = None
        return idx


class OtelTrace:
    """
    First instrument the frameworks and packages your application is using. Then init the tracer.
    Example Flask app using requests and sqlalchemy:

        flask_app = Flask(__name__)

        def response_hook(span: Span, status: str, response_headers: List):
            if span and span.is_recording():
                if hasattr(g, "client_id"):
                    span.set_attribute("client_id", g.client_id)

        OtelTrace.instrument_flask(app=flask_app, response_hook=response_hook, excluded_urls="hz,healthcheck,ready")
        OtelTrace.instrument_requests()
        OtelTrace.instrument_sqlalchemy(engines=flask_app.db_service.engines)
        OtelTrace.init(service_name="my-service-name")

    Traces will appear in Cloud Trace, Sentry, and Honeycomb as "my-service-name".
    """

    sentry_integrations = []
    excluded_url_list = []

    @classmethod
    def build_excluded_url_list(cls, excluded_urls: str):
        """
        :param str excluded_urls: A comma separated lists of paths to ignore (typically healthcheck paths)
        """
        if not excluded_urls:
            return

        url_list = [excluded_url.strip() for excluded_url in excluded_urls.split(",")]
        cls.excluded_url_list = list(set().union(cls.excluded_url_list, url_list))

    @classmethod
    def init(
        cls,
        service_name: str,
        cloud_trace: bool = True,
        sentry: bool = True,
        honeycomb: bool = True,
        jaegertracing: bool = False,
        excluded_urls: str = None,
        trace_propagation_targets: list = None,
        shutdown_timeout: int = 0,
    ):
        if not cls.sentry_integrations:
            logging.warning("You must instrument application frameworks before calling OtelTrace.init")

        trace_propagation_targets = trace_propagation_targets or []

        os.environ["OTEL_SERVICE_NAME"] = service_name

        cls.build_excluded_url_list(excluded_urls)

        resource = Resource.create({"service.name": service_name})
        tracer_provider = TracerProvider(resource=resource)

        def before_send(event, hint):
            if event.get("transaction") in cls.excluded_url_list:
                return None
            return event

        if sentry:
            sentry_extra_kwargs: dict = {}
            if SENTRY_QUIET_EXIT:
                sentry_extra_kwargs = {"shutdown_timeout": 0}
            sentry_sdk.init(
                dsn=SENTRY_INTEGRATION_DSN,
                integrations=cls.sentry_integrations,
                environment=ENVIRONMENT_NAME,
                traces_sampler=cls.sentry_sampler,
                sample_rate=float(SENTRY_ERROR_SAMPLE_RATE),
                send_default_pii=bool(SENTRY_ALL_DATA),
                trace_propagation_targets=trace_propagation_targets,
                debug=bool(SENTRY_DEBUG),
                shutdown_timeout=shutdown_timeout,
                before_send=before_send,
                **sentry_extra_kwargs,
            )

        if DEBUG or ENVIRONMENT_NAME == "local":
            trace_exporter = SpanExporter()
            tracer_provider.add_span_processor(CustomBatchSpanProcessor(service_name, trace_exporter))

        if cloud_trace:
            cloud_trace_exporter = CloudTraceSpanExporter()
            tracer_provider.add_span_processor(CustomBatchSpanProcessor(service_name, cloud_trace_exporter))

        if honeycomb:
            honeycomb_exporter = OTLPSpanExporter(
                endpoint=HONEYCOMB_ENDPOINT,
                headers={
                    "x-honeycomb-team": HONEYCOMB_API_KEY,
                    "x-honeycomb-dataset": HONEYCOMB_DATASET,
                },
                timeout=30,
            )
            tracer_provider.add_span_processor(CustomBatchSpanProcessor(service_name, honeycomb_exporter))

        if jaegertracing and JAEGER_TRACING_ENDPOINT:
            jaeger_exporter = OTLPSpanExporter(
                endpoint=JAEGER_TRACING_ENDPOINT,
            )
            tracer_provider.add_span_processor(CustomBatchSpanProcessor(service_name, jaeger_exporter))

        trace.set_tracer_provider(tracer_provider)

    @classmethod
    def instrument_django(
        cls,
        request_hook: callable = django_request_hook,
        response_hook: callable = None,
        excluded_urls: str = None,
        enable_sql_commenter: bool = False,
    ):
        from opentelemetry.instrumentation.django import DjangoInstrumentor
        from sentry_sdk.integrations.django import DjangoIntegration

        cls.build_excluded_url_list(excluded_urls)

        DjangoInstrumentor().instrument(
            request_hook=request_hook,
            response_hook=response_hook,
            excluded_urls=excluded_urls,
            is_sql_commentor_enabled=enable_sql_commenter,
        )

        cls.sentry_integrations.append(DjangoIntegration())

    @classmethod
    def instrument_flask(
        cls,
        flask_app,
        request_hook: callable = None,
        response_hook: callable = None,
        excluded_urls: str = None,
        enable_sql_commenter: bool = False,
        sql_commenter_options: dict = None,
    ):
        from opentelemetry.instrumentation.flask import FlaskInstrumentor
        from sentry_sdk.integrations.flask import FlaskIntegration

        sql_commenter_options = sql_commenter_options or {}

        cls.build_excluded_url_list(excluded_urls)

        FlaskInstrumentor().instrument_app(
            app=flask_app,
            request_hook=request_hook,
            response_hook=response_hook,
            excluded_urls=excluded_urls,
            enable_commenter=enable_sql_commenter,
            commenter_options=sql_commenter_options,
        )

        cls.sentry_integrations.append(FlaskIntegration())

    @classmethod
    def instrument_fastapi(
        cls,
        fastapi_app,
        client_request_hook: callable = None,
        client_response_hook: callable = None,
        server_request_hook: callable = None,
        excluded_urls: str = None,
    ):
        from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
        from sentry_sdk.integrations.fastapi import FastApiIntegration

        cls.build_excluded_url_list(excluded_urls)

        FastAPIInstrumentor().instrument_app(
            app=fastapi_app,
            client_request_hook=client_request_hook,
            client_response_hook=client_response_hook,
            server_request_hook=server_request_hook,
            excluded_urls=excluded_urls,
        )
        cls.sentry_integrations.append(FastApiIntegration())

    @classmethod
    def instrument_requests(
        cls,
        request_hook: callable = None,
        response_hook: callable = None,
        excluded_urls: str = None,
    ):
        from opentelemetry.instrumentation.requests import RequestsInstrumentor

        cls.build_excluded_url_list(excluded_urls)

        RequestsInstrumentor().instrument(
            request_hook=request_hook,
            response_hook=response_hook,
            excluded_urls=excluded_urls,
        )

    @classmethod
    def instrument_kafka(cls, skip_dependency_checks: bool = False):
        from opentelemetry.instrumentation.confluent_kafka import ConfluentKafkaInstrumentor

        ConfluentKafkaInstrumentor().instrument(skip_dep_check=skip_dependency_checks)

    @classmethod
    def instrument_psycopg2(
        cls,
        enable_commenter: bool = True,
        commenter_options: dict = None,
    ):
        from opentelemetry.instrumentation.psycopg2 import Psycopg2Instrumentor

        commenter_options = commenter_options or {}

        Psycopg2Instrumentor().instrument(
            enable_commenter=enable_commenter,
            commenter_options=commenter_options,
        )

    @classmethod
    def instrument_sqlalchemy(
        cls,
        engines: list,
        enable_commenter: bool = True,
        commenter_options: dict = None,
    ):
        from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor

        commenter_options = commenter_options or {}

        SQLAlchemyInstrumentor().instrument(
            engines=engines,
            enable_commenter=enable_commenter,
            commenter_options=commenter_options,
        )

    @classmethod
    def instrument_redis(
        cls,
        request_hook: callable = None,
        response_hook: callable = None,
    ):
        from opentelemetry.instrumentation.redis import RedisInstrumentor

        RedisInstrumentor().instrument(
            request_hook=request_hook,
            response_hook=response_hook,
        )

    @classmethod
    def sentry_sampler(cls, sampling_context):
        request_path = sampling_context.get("wsgi_environ", {}).get("PATH_INFO")
        if request_path in cls.excluded_url_list:
            return 0
        else:
            return float(SENTRY_TRACES_SAMPLE_RATE)

    @staticmethod
    def get_current_span(context: Context = None) -> Span:
        return trace.get_current_span(context)

    @staticmethod
    def set_span_status(
        status_code: int,
        ex: Exception = None,
        context: Context = None,
    ):
        current_span = trace.get_current_span(context)
        if ex:
            OtelTrace.record_exception(ex)
            current_span.set_status(Status(StatusCode.ERROR))

        sentry_span = sentry_sdk.get_current_span()
        if sentry_span is not None:
            sentry_span.set_http_status(status_code)

        current_span.set_attribute(SpanAttributes.HTTP_STATUS_CODE, status_code)

    @staticmethod
    def record_exception(ex: Exception, context: Context = None):
        if type(ex) == str:
            ex = Exception(ex)
        sentry_sdk.capture_exception(ex)
        current_span = trace.get_current_span(context)
        current_span.record_exception(ex)

    @staticmethod
    def set_attribute(name: str, value: types.AttributeValue, context: Context = None):
        # Sentry SDK attaches request body to trace automatically
        if name != "request_body":
            sentry_span = sentry_sdk.get_current_span()
            if sentry_span is not None:
                sentry_span.set_tag(name, str(value))

        current_span = trace.get_current_span(context)
        current_span.set_attribute(name, value)

    @staticmethod
    def set_attributes(attributes: dict, context: Context = None):
        for k, v in attributes.items():
            OtelTrace.set_attribute(k, v, context)

    @staticmethod
    def set_sentry_tag(name: str, value):
        sentry_sdk.set_tag(name, value)

    @staticmethod
    def set_trace_id(trace_id: str, context: Context = None):
        OtelTrace.set_sentry_tag("trace_id", trace_id)
        OtelTrace.set_attribute("trace_id", trace_id, context)

    @staticmethod
    def start_as_current_span(span_name: str):
        tracer = trace.get_tracer(__name__)
        return tracer.start_as_current_span(span_name)
