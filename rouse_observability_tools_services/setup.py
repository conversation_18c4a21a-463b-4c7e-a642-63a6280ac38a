import setuptools

with open("README.md", "r") as fh:
    long_description = fh.read()

setuptools.setup(
    name="rouse-observability-tools-services",
    version="0.5.2",
    author="Rouse Services",
    author_email="<EMAIL>",
    description="Rouse Observability Tools Services",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/pypa/sampleproject",
    packages=setuptools.find_packages(),
    classifiers=[
        #   3 - Alpha
        #   4 - Beta
        #   5 - Production/Stable
        "Development Status :: 3 - Alpha",
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    install_requires=[
        "opentelemetry-distro==0.52b0",
        "opentelemetry-propagator-gcp>=1.9.0",
        "opentelemetry-resourcedetector-gcp>=1.9.0a0",
        "opentelemetry-exporter-gcp-trace>=1.9.0",
        "opentelemetry-exporter-otlp-proto-http>=1.31.0",
        "opentelemetry-exporter-gcp-monitoring>=1.9.0a0",
        "opentelemetry-instrumentation-django==0.52b0",
        "opentelemetry-instrumentation-psycopg2==0.52b0",
        "opentelemetry-instrumentation-redis==0.52b0",
        "opentelemetry-instrumentation-confluent-kafka==0.52b0",
        "opentelemetry-instrumentation-flask==0.52b0",
        "opentelemetry-instrumentation-fastapi==0.52b0",
        "opentelemetry-instrumentation-requests==0.52b0",
        "opentelemetry-instrumentation-sqlalchemy==0.52b0",
        "honeycomb-beeline>=3.5.1",
        "sentry-sdk>=1.19.0",
        "google-cloud-profiler>=4.0.0",
        "google-cloud-logging>=3.5.0",
        "python-json-logger>=2.0.7",
        "redis>=4.5.4",
    ],
    python_requires=">=3.6",
)
