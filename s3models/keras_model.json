{"class_name": "Functional", "config": {"name": "model", "trainable": true, "layers": [{"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_1"}, "registered_name": null, "name": "input_1", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_2"}, "registered_name": null, "name": "input_2", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_3"}, "registered_name": null, "name": "input_3", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_4"}, "registered_name": null, "name": "input_4", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_5"}, "registered_name": null, "name": "input_5", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_6"}, "registered_name": null, "name": "input_6", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_7"}, "registered_name": null, "name": "input_7", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_8"}, "registered_name": null, "name": "input_8", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_9"}, "registered_name": null, "name": "input_9", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_10"}, "registered_name": null, "name": "input_10", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_11"}, "registered_name": null, "name": "input_11", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_12"}, "registered_name": null, "name": "input_12", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_13"}, "registered_name": null, "name": "input_13", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_14"}, "registered_name": null, "name": "input_14", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_15"}, "registered_name": null, "name": "input_15", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_16"}, "registered_name": null, "name": "input_16", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_17"}, "registered_name": null, "name": "input_17", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_18"}, "registered_name": null, "name": "input_18", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_19"}, "registered_name": null, "name": "input_19", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_20"}, "registered_name": null, "name": "input_20", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_21"}, "registered_name": null, "name": "input_21", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 1], "dtype": "int64", "sparse": false, "ragged": false, "name": "input_22"}, "registered_name": null, "name": "input_22", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 421, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding", "inbound_nodes": [[["input_1", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_1", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 21, "output_dim": 11, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_1", "inbound_nodes": [[["input_2", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_2", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 2359, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_2", "inbound_nodes": [[["input_3", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_3", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 88363, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_3", "inbound_nodes": [[["input_4", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_4", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 19, "output_dim": 10, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_4", "inbound_nodes": [[["input_5", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_5", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 163, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_5", "inbound_nodes": [[["input_6", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_6", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 7, "output_dim": 4, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_6", "inbound_nodes": [[["input_7", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_7", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 255, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_7", "inbound_nodes": [[["input_8", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_8", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 16, "output_dim": 8, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_8", "inbound_nodes": [[["input_9", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_9", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 68, "output_dim": 34, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_9", "inbound_nodes": [[["input_10", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_10", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 6, "output_dim": 3, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_10", "inbound_nodes": [[["input_11", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_11", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 8, "output_dim": 4, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_11", "inbound_nodes": [[["input_12", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_12", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 3, "output_dim": 2, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_12", "inbound_nodes": [[["input_13", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_13", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 3, "output_dim": 2, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_13", "inbound_nodes": [[["input_14", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_14", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 35, "output_dim": 18, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_14", "inbound_nodes": [[["input_15", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_15", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 53450, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_15", "inbound_nodes": [[["input_16", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_16", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 22553, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_16", "inbound_nodes": [[["input_17", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_17", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 4140, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_17", "inbound_nodes": [[["input_18", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_18", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 859, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_18", "inbound_nodes": [[["input_19", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_19", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 167343, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_19", "inbound_nodes": [[["input_20", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_20", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 210680, "output_dim": 50, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_20", "inbound_nodes": [[["input_21", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Embedding", "config": {"name": "embedding_21", "trainable": true, "dtype": "float32", "batch_input_shape": [null, 1], "input_dim": 23, "output_dim": 12, "embeddings_initializer": {"module": "keras.initializers", "class_name": "RandomUniform", "config": {"minval": -0.05, "maxval": 0.05, "seed": null}, "registered_name": null}, "embeddings_regularizer": null, "activity_regularizer": null, "embeddings_constraint": null, "mask_zero": false, "input_length": 1}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "embedding_21", "inbound_nodes": [[["input_22", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape", "inbound_nodes": [[["embedding", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_1", "trainable": true, "dtype": "float32", "target_shape": [11]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 11]}, "name": "reshape_1", "inbound_nodes": [[["embedding_1", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_2", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_2", "inbound_nodes": [[["embedding_2", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_3", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_3", "inbound_nodes": [[["embedding_3", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_4", "trainable": true, "dtype": "float32", "target_shape": [10]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 10]}, "name": "reshape_4", "inbound_nodes": [[["embedding_4", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_5", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_5", "inbound_nodes": [[["embedding_5", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_6", "trainable": true, "dtype": "float32", "target_shape": [4]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 4]}, "name": "reshape_6", "inbound_nodes": [[["embedding_6", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_7", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_7", "inbound_nodes": [[["embedding_7", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_8", "trainable": true, "dtype": "float32", "target_shape": [8]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 8]}, "name": "reshape_8", "inbound_nodes": [[["embedding_8", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_9", "trainable": true, "dtype": "float32", "target_shape": [34]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 34]}, "name": "reshape_9", "inbound_nodes": [[["embedding_9", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_10", "trainable": true, "dtype": "float32", "target_shape": [3]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 3]}, "name": "reshape_10", "inbound_nodes": [[["embedding_10", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_11", "trainable": true, "dtype": "float32", "target_shape": [4]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 4]}, "name": "reshape_11", "inbound_nodes": [[["embedding_11", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_12", "trainable": true, "dtype": "float32", "target_shape": [2]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 2]}, "name": "reshape_12", "inbound_nodes": [[["embedding_12", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_13", "trainable": true, "dtype": "float32", "target_shape": [2]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 2]}, "name": "reshape_13", "inbound_nodes": [[["embedding_13", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_14", "trainable": true, "dtype": "float32", "target_shape": [18]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 18]}, "name": "reshape_14", "inbound_nodes": [[["embedding_14", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_15", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_15", "inbound_nodes": [[["embedding_15", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_16", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_16", "inbound_nodes": [[["embedding_16", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_17", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_17", "inbound_nodes": [[["embedding_17", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_18", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_18", "inbound_nodes": [[["embedding_18", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_19", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_19", "inbound_nodes": [[["embedding_19", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_20", "trainable": true, "dtype": "float32", "target_shape": [50]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 50]}, "name": "reshape_20", "inbound_nodes": [[["embedding_20", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_21", "trainable": true, "dtype": "float32", "target_shape": [12]}, "registered_name": null, "build_config": {"input_shape": [null, 1, 12]}, "name": "reshape_21", "inbound_nodes": [[["embedding_21", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Concatenate", "config": {"name": "concatenate", "trainable": true, "dtype": "float32", "axis": -1}, "registered_name": null, "build_config": {"input_shape": [[null, 50], [null, 11], [null, 50], [null, 50], [null, 10], [null, 50], [null, 4], [null, 50], [null, 8], [null, 34], [null, 3], [null, 4], [null, 2], [null, 2], [null, 18], [null, 50], [null, 50], [null, 50], [null, 50], [null, 50], [null, 50], [null, 12]]}, "name": "concatenate", "inbound_nodes": [[["reshape", 0, 0, {}], ["reshape_1", 0, 0, {}], ["reshape_2", 0, 0, {}], ["reshape_3", 0, 0, {}], ["reshape_4", 0, 0, {}], ["reshape_5", 0, 0, {}], ["reshape_6", 0, 0, {}], ["reshape_7", 0, 0, {}], ["reshape_8", 0, 0, {}], ["reshape_9", 0, 0, {}], ["reshape_10", 0, 0, {}], ["reshape_11", 0, 0, {}], ["reshape_12", 0, 0, {}], ["reshape_13", 0, 0, {}], ["reshape_14", 0, 0, {}], ["reshape_15", 0, 0, {}], ["reshape_16", 0, 0, {}], ["reshape_17", 0, 0, {}], ["reshape_18", 0, 0, {}], ["reshape_19", 0, 0, {}], ["reshape_20", 0, 0, {}], ["reshape_21", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 552], "dtype": "float32", "sparse": false, "ragged": false, "name": "main_input"}, "registered_name": null, "name": "main_input", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "Concatenate", "config": {"name": "concatenate_1", "trainable": true, "dtype": "float32", "axis": 1}, "registered_name": null, "build_config": {"input_shape": [[null, 658], [null, 552]]}, "name": "concatenate_1", "inbound_nodes": [[["concatenate", 0, 0, {}], ["main_input", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense", "trainable": true, "dtype": "float32", "units": 1000, "activation": "linear", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "RandomNormal", "config": {"mean": 0.0, "stddev": 0.05, "seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 1210]}, "name": "dense", "inbound_nodes": [[["concatenate_1", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Activation", "config": {"name": "activation", "trainable": true, "dtype": "float32", "activation": "relu"}, "registered_name": null, "build_config": {"input_shape": [null, 1000]}, "name": "activation", "inbound_nodes": [[["dense", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout", "trainable": true, "dtype": "float32", "rate": 0.25, "noise_shape": null, "seed": null}, "registered_name": null, "build_config": {"input_shape": [null, 1000]}, "name": "dropout", "inbound_nodes": [[["activation", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_1", "trainable": true, "dtype": "float32", "units": 500, "activation": "linear", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "RandomNormal", "config": {"mean": 0.0, "stddev": 0.05, "seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 1000]}, "name": "dense_1", "inbound_nodes": [[["dropout", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Activation", "config": {"name": "activation_1", "trainable": true, "dtype": "float32", "activation": "relu"}, "registered_name": null, "build_config": {"input_shape": [null, 500]}, "name": "activation_1", "inbound_nodes": [[["dense_1", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_1", "trainable": true, "dtype": "float32", "rate": 0.15, "noise_shape": null, "seed": null}, "registered_name": null, "build_config": {"input_shape": [null, 500]}, "name": "dropout_1", "inbound_nodes": [[["activation_1", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_2", "trainable": true, "dtype": "float32", "units": 128, "activation": "linear", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "RandomNormal", "config": {"mean": 0.0, "stddev": 0.05, "seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 500]}, "name": "dense_2", "inbound_nodes": [[["dropout_1", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Activation", "config": {"name": "activation_2", "trainable": true, "dtype": "float32", "activation": "relu"}, "registered_name": null, "build_config": {"input_shape": [null, 128]}, "name": "activation_2", "inbound_nodes": [[["dense_2", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_2", "trainable": true, "dtype": "float32", "rate": 0.15, "noise_shape": null, "seed": null}, "registered_name": null, "build_config": {"input_shape": [null, 128]}, "name": "dropout_2", "inbound_nodes": [[["activation_2", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_3", "trainable": true, "dtype": "float32", "units": 64, "activation": "linear", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "RandomNormal", "config": {"mean": 0.0, "stddev": 0.05, "seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 128]}, "name": "dense_3", "inbound_nodes": [[["dropout_2", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Activation", "config": {"name": "activation_3", "trainable": true, "dtype": "float32", "activation": "relu"}, "registered_name": null, "build_config": {"input_shape": [null, 64]}, "name": "activation_3", "inbound_nodes": [[["dense_3", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_3", "trainable": true, "dtype": "float32", "rate": 0.15, "noise_shape": null, "seed": null}, "registered_name": null, "build_config": {"input_shape": [null, 64]}, "name": "dropout_3", "inbound_nodes": [[["activation_3", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_4", "trainable": true, "dtype": "float32", "units": 1, "activation": "linear", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 64]}, "name": "dense_4", "inbound_nodes": [[["dropout_3", 0, 0, {}]]]}, {"module": "keras.layers", "class_name": "Activation", "config": {"name": "activation_4", "trainable": true, "dtype": "float32", "activation": "linear"}, "registered_name": null, "build_config": {"input_shape": [null, 1]}, "name": "activation_4", "inbound_nodes": [[["dense_4", 0, 0, {}]]]}], "input_layers": [["input_1", 0, 0], ["input_2", 0, 0], ["input_3", 0, 0], ["input_4", 0, 0], ["input_5", 0, 0], ["input_6", 0, 0], ["input_7", 0, 0], ["input_8", 0, 0], ["input_9", 0, 0], ["input_10", 0, 0], ["input_11", 0, 0], ["input_12", 0, 0], ["input_13", 0, 0], ["input_14", 0, 0], ["input_15", 0, 0], ["input_16", 0, 0], ["input_17", 0, 0], ["input_18", 0, 0], ["input_19", 0, 0], ["input_20", 0, 0], ["input_21", 0, 0], ["input_22", 0, 0], ["main_input", 0, 0]], "output_layers": [["activation_4", 0, 0]]}, "keras_version": "2.14.0", "backend": "tensorflow"}